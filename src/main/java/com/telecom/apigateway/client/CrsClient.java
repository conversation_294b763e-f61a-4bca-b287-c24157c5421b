package com.telecom.apigateway.client;

import cn.hutool.json.JSONObject;
import com.telecom.apigateway.common.excption.WafException;
import com.telecom.apigateway.model.dto.CrsDetectResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024-11-14
 */
@Slf4j
@Component
public class CrsClient {
    @Value("${crs.url}")
    private String crsUrl;
    @Resource
    private RestTemplate restTemplate;

    /**
     * 调用 crs 进行检测
     *
     * @param httpRequestInfo 请求信息, 需要满足 http raw 格式, 不然调用 crs 可能会报错
     * @return 检测结果
     */
    public CrsDetectResponseDTO analyseHttpInfoRisk(String httpRequestInfo) throws WafException {
        JSONObject req = new JSONObject();
        req.set("request", httpRequestInfo);
        req.set("response", "");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        String url = crsUrl + "/api/crs/intercept";
        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(req, headers);
        ResponseEntity<CrsDetectResponseDTO> resp = restTemplate.postForEntity(url, requestEntity,
                CrsDetectResponseDTO.class);
        if (!resp.getStatusCode().is2xxSuccessful() || resp.getBody() == null) {
            log.error("CRS intercept 网络失败: {}", resp);
            throw new WafException("CRS intercept 失败");
        }
        CrsDetectResponseDTO body = resp.getBody();
        if (body.getCode() != null && body.getCode() != 200) {
            log.error("CRS intercept 失败: {}", resp);
            log.error("CRS intercept 失败请求: {}", httpRequestInfo);
            throw new WafException("CRS intercept 失败");
        }
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>> crs 请求: {}", req);
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>> crs 响应: {}", resp.getBody());
        log.info("\n");
        return resp.getBody();
    }
}
