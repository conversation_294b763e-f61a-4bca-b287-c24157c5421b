package com.telecom.apigateway.client;

import com.telecom.apigateway.model.entity.IpInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-11-14
 */
@Slf4j
@Component
public class WhoisClient {

    private static final String WHOIS_URL = "https://wq.apnic.net/query?searchtext=";
    private static final String CHAITIN_WHOIS_URL = "https://wq.apnic.net/apnic-bin/jwhois.pl?ip=";

    @Resource
    private RestTemplate restTemplate;

    public IpInfo query(String ip) {
        if (!isValidIp(ip)) {
            log.warn(">>>>>>>>>>>>>>>>>>>> whois 查询无效ip: {}", ip);
            return null;
        }

        String url = WHOIS_URL + ip;
        AttributeObject[] resp = null;
        try {
            resp = restTemplate.getForObject(url, AttributeObject[].class);
        } catch (Exception e) {
            log.warn(">>>>>>>>>>>>>>>>>>>> whois 查询异常:{}", e.getMessage());
            return null;
        }

        List<AttributeObject> attributeObjects = Optional.ofNullable(resp).map(Arrays::asList).orElse(null);

        IpInfo ipInfo = new IpInfo();
        if (attributeObjects != null) {
            // 过滤 "objectType": "inetnum" 的数据
            attributeObjects.stream()
                    .filter(attributeObject -> "inetnum".equals(attributeObject.getObjectType()))
                    .forEach(attributeObject -> {
                        String networkOperator = getAttributeValue(attributeObject, "netname");
                        ipInfo.setIsp(networkOperator);
                    });

            // 过滤 "objectType": "person" 的数据
            attributeObjects.stream()
                    .filter(attributeObject -> "person".equals(attributeObject.getObjectType()))
                    .forEach(attributeObject -> {
                        String person = getAttributeValue(attributeObject, "person");
                        ipInfo.setPerson(person);

                        String phone = getAttributeValue(attributeObject, "phone");
                        ipInfo.setPhone(phone);

                        String email = getAttributeValue(attributeObject, "e-mail");
                        ipInfo.setEmail(email); // Corrected from setCountry(email)

                        String country = getAttributeValue(attributeObject, "country");
                        ipInfo.setCountry(country);
                    });
        }
        return ipInfo;
    }

    private String getAttributeValue(AttributeObject attributeObject, String attributeName) {
        return attributeObject.getAttributes().stream()
                .filter(attribute -> attributeName.equals(attribute.getName()))
                .findFirst()
                .map(Attribute::getValues)
                .map(values -> values[0])
                .orElse(null);
    }

    private boolean isValidIp(String ip) {
        return ip.matches("\\b((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)" +
                "\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)" +
                "\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\b");
    }

    public String queryIpInfoByChaitin(String ip) {
        try {
            String ipInfo = restTemplate.getForObject(CHAITIN_WHOIS_URL + ip, String.class);
            if (ipInfo != null) {
                return ipInfo;
            }
            return "未知信息";
        } catch (Exception e) {
            return "查询失败";
        }
    }

    @Data
    static class AttributeObject {
        private String type;
        private List<Attribute> attributes;
        private String objectType;
        private String primaryKey;
    }

    @Data
    static class Attribute {
        private String name;
        private String[] values;
    }
}
