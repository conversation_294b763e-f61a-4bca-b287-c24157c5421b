package com.telecom.apigateway.client;

import com.telecom.apigateway.model.dto.es.ServerMonitorDTO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AbstractAggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务器监控ES客户端
 */
@Slf4j
@Component
public class ServerMonitorEsClient {

    @Value("${spring.elasticsearch.server-monitor-index}")
    private String indexName;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    /**
     * 获取所有有监控数据的主机ID
     *
     * @return 主机ID列表
     */
    public List<String> getAllHostIds() {
        try {
            TermsAggregationBuilder aggregation = AggregationBuilders
                    .terms("unique_hosts")
                    .field("host.hostId.keyword") // 确保使用keyword类型进行聚合
                    .size(1000); // 限制返回的唯一hostId数量

            NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                    .withAggregations(aggregation)
                    .withMaxResults(0) // 不需要返回文档，只需要聚合结果
                    .build();

            // 执行聚合查询
            SearchHits<ServerMonitorDTO> searchHits = elasticsearchRestTemplate.search(
                    searchQuery,
                    ServerMonitorDTO.class,
                    IndexCoordinates.of(indexName)
            );

            // 从聚合结果中提取hostId
            List<String> hostIds = new ArrayList<>();

            if (searchHits.hasAggregations()) {
                AggregationsContainer<?> aggregationsContainer =
                        searchHits.getAggregations();

                if (aggregationsContainer != null) {
                    Aggregations aggregations = (Aggregations) aggregationsContainer.aggregations();

                    Terms terms = aggregations.get("unique_hosts");

                    if (terms != null) {
                        terms.getBuckets().forEach(bucket -> {
                            hostIds.add(bucket.getKeyAsString());
                        });
                    }
                }
            }

            return hostIds;
        } catch (Exception e) {
            log.error("获取所有主机ID失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取指定主机ID的最新监控数据
     *
     * @param hostId 主机ID
     * @return 最新的监控数据
     */
    public ServerMonitorDTO getLatestByHostId(String hostId) {
        // 使用过滤条件代替term查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.matchQuery("host.hostId", hostId));

        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("@timestamp").order(SortOrder.DESC))
                .withMaxResults(1)
                .build();

        SearchHits<ServerMonitorDTO> searchHits = elasticsearchRestTemplate.search(
                searchQuery,
                ServerMonitorDTO.class,
                IndexCoordinates.of(indexName)
        );

        if (searchHits.hasSearchHits()) {
            return searchHits.getSearchHit(0).getContent();
        }

        return null;
    }

    /**
     * 获取指定主机ID的前两条监控数据
     *
     * @param hostId 主机ID
     * @return 前两条监控数据列表
     */
    public List<ServerMonitorDTO> getLatestTwoByHostId(String hostId) {
        // 使用过滤条件代替term查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.matchQuery("host.hostId", hostId));

        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("@timestamp").order(SortOrder.DESC))
                .withMaxResults(2)
                .build();

        SearchHits<ServerMonitorDTO> searchHits = elasticsearchRestTemplate.search(
                searchQuery,
                ServerMonitorDTO.class,
                IndexCoordinates.of(indexName)
        );

        return searchHits.getSearchHits().stream()
                .map(SearchHit::getContent)
                .collect(Collectors.toList());
    }

    /**
     * 批量获取多个主机ID的最新监控数据
     *
     * @param hostIds 主机ID列表
     * @return 主机ID和最新监控数据的映射
     */
    public Map<String, ServerMonitorDTO> getLatestByHostIds(List<String> hostIds) {
        List<ServerMonitorDTO> resultList = new ArrayList<>();

        for (String hostId : hostIds) {
            ServerMonitorDTO dto = getLatestByHostId(hostId);
            if (dto != null) {
                resultList.add(dto);
            }
        }

        return resultList.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getHost().getHostId(),
                        dto -> dto
                ));
    }

    /**
     * 批量获取多个主机ID的前两条监控数据
     *
     * @param hostIds 主机ID列表
     * @return 主机ID和前两条监控数据的映射
     */
    public Map<String, List<ServerMonitorDTO>> getLatestTwoByHostIds(List<String> hostIds) {
        Map<String, List<ServerMonitorDTO>> result = hostIds.stream()
                .collect(Collectors.toMap(
                        hostId -> hostId,
                        this::getLatestTwoByHostId
                ));

        return result;
    }
} 