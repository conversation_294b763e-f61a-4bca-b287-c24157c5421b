package com.telecom.apigateway.config.interceptor;

import cloud.tianai.captcha.common.response.ApiResponse;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.License;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.service.LicenseService;
import com.telecom.apigateway.service.OperationLogService;
import com.telecom.apigateway.service.UserContextService;
import com.telecom.apigateway.utils.IpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * @program: APIWG-Service
 * @ClassName LicenseInterceptor
 * @description:
 * @author: Levi
 * @create: 2025-05-23 15:04
 * @Version 1.0
 **/

@Slf4j
@Component
@RequiredArgsConstructor
public class LicenseInterceptor implements HandlerInterceptor {
    private final OperationLogService operationLogService;
    private final UserContextService userContextService;
    private final LicenseService licenseService;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        System.out.println("** LicenseInterceptor preHandle");
        Map<String, Object> result = new HashMap<>();
        List<License> allLicenselist = licenseService.list(new LambdaQueryWrapper<License>() .eq(License::getStatus, "0").orderByDesc(License::getExpirationDays) ) ;
        Date now = new Date();
        //检查证书过期情况和证书状态
        // 3. 筛选出已过期的许可证 ID（Java 8 兼容写法）
        List<Long> expiredLicenseIds = allLicenselist.stream()
                .filter(license -> license.getExpirationDays() != null
                        && license.getExpirationDays().before(now))
                .map(License::getId)
                .collect(Collectors.toList()); // 使用 Collectors.toList()

        if (expiredLicenseIds.isEmpty()) {
            System.out.println("没有许可证过期,不需要更新状态");
        }else{
            System.out.println("有许可证过期，ID如下：");
            System.out.println(expiredLicenseIds);
            // 4. 批量更新过期许可证的状态为 1
            licenseService.update(null,
                    new LambdaUpdateWrapper<License>()
                            .set(License::getStatus, "1")
                            .in(License::getId, expiredLicenseIds)
            );
        }

        //重新获取有效授权证书信息
        allLicenselist = licenseService.list(new LambdaQueryWrapper<License>() .eq(License::getStatus, "0").orderByDesc(License::getExpirationDays).last("LIMIT 1") ) ;// 假设 status 是字符串类型  // 按过期天数降序 // 只取第一条（最大值）);
        if(CollectionUtils.isEmpty(allLicenselist)){
            //目前存在有效的授权证书
           throw  new BusinessException(ResultCodeEnum.NOTLICENSE);
        }else{
            //默认返回true
        }

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        System.out.println("** LicenseInterceptor afterCompletion");

    }

}
