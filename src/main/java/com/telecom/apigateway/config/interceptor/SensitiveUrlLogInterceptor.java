package com.telecom.apigateway.config.interceptor;

import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.model.enums.*;
import com.telecom.apigateway.service.OperationLogService;
import com.telecom.apigateway.service.UserContextService;
import com.telecom.apigateway.utils.IpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 敏感URL访问日志拦截器
 * 用于记录特定敏感URL的访问日志
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SensitiveUrlLogInterceptor implements HandlerInterceptor {
    private final OperationLogService operationLogService;
    private final UserContextService userContextService;

    // 需要记录访问日志的敏感URL映射表 <URL前缀, 描述>
    private static final String LOGIN_URL = "/api/auth/login";
    private static final Map<String, String> SENSITIVE_URL_MAP = new HashMap<>();

    static {
        // 攻击者画像相关
        SENSITIVE_URL_MAP.put("/api/risk/portrait", "攻击者画像");
        SENSITIVE_URL_MAP.put("/api/risk/portrait/page", "攻击者画像列表");

//        // 威胁详情相关
//        SENSITIVE_URL_MAP.put("/api/threat/detail", "威胁详情");
//        SENSITIVE_URL_MAP.put("/api/threat/", "威胁信息");

        // 敏感信息泄露相关
        SENSITIVE_URL_MAP.put("/api/sensitive/detail", "敏感信息详情");
        SENSITIVE_URL_MAP.put("/api/sensitive/portrait", "敏感信息泄露画像");
        SENSITIVE_URL_MAP.put("/api/sensitive-api", "敏感信息泄露详情");

//        // 大模型分析相关
//        SENSITIVE_URL_MAP.put("/api/llm/threatAnalysis", "威胁日志智能分析");
//
//        // 异常行为分析相关
//        SENSITIVE_URL_MAP.put("/api/abnormal/list", "异常行为分析");
//        SENSITIVE_URL_MAP.put("/api/abnormal/detail", "异常行为详情");
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String requestURI = request.getRequestURI();
        // 检查是否是敏感URL
        Map.Entry<String, String> matchedEntry = findMatchingSensitiveUrl(requestURI);
        if (matchedEntry != null) {
            try {
                boolean login = userContextService.isLogin();
                String username = login ? userContextService.getCurrentUsername() : "未登录用户";
                String clientIp = IpUtils.getClientIp(request);
                // 构建操作描述
                String description = "查看了" + matchedEntry.getValue();

                // 添加请求参数信息（可选）
                String queryString = request.getQueryString();
                if (StrUtil.isNotBlank(queryString)) {
                    description += "，参数：" + queryString;
                }

                // 记录操作日志
                operationLogService.createLog(
                        username,
                        OperationTypeEnum.VIEW,
                        null,
                        description,
                        clientIp
                );
            } catch (Exception e) {
                // 记录日志失败不应影响正常访问
                log.error("记录敏感URL访问日志失败: {}", e.getMessage(), e);
            }
        }

        // 继续处理请求
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        String requestURI = request.getRequestURI();
        if (requestURI.equals(LOGIN_URL)) {
            // 获取当前用户信息
            boolean login = userContextService.isLogin();
            String username = login ? userContextService.getCurrentUsername() : "未登录用户";
            String clientIp = IpUtils.getClientIp(request);

            // 根据响应状态判断登录是否成功
            int status = response.getStatus();
            if (status == HttpServletResponse.SC_OK && login) {
                operationLogService.createLog(
                        username,
                        OperationTypeEnum.LOGIN,
                        null,
                        OperationTypeEnum.LOGIN.getDescription(),
                        clientIp
                );
            } else {
                operationLogService.createLog(
                        username,
                        OperationTypeEnum.LOGIN,
                        null,
                        "登录失败",
                        clientIp
                );
            }
        }
    }

    /**
     * 查找匹配的敏感URL
     *
     * @param requestURI 请求URI
     * @return 匹配的敏感URL和描述，如果没有匹配则返回null
     */
    private Map.Entry<String, String> findMatchingSensitiveUrl(String requestURI) {
        return SENSITIVE_URL_MAP.entrySet().stream()
                .filter(entry -> requestURI.startsWith(entry.getKey()))
                .findFirst()
                .orElse(null);
    }
}
