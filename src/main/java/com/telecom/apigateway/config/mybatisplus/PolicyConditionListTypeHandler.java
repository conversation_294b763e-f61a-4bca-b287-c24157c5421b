package com.telecom.apigateway.config.mybatisplus;

import cn.hutool.json.JSONUtil;
import com.telecom.apigateway.model.dto.PolicyConditionDTO;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.stereotype.Component;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 策略条件列表类型处理器
 * 用于处理 List<PolicyConditionDTO> 与数据库 JSON 字符串之间的转换
 */
@Component
public class PolicyConditionListTypeHandler extends BaseTypeHandler<List<PolicyConditionDTO>> {

    /**
     * 设置参数值到 PreparedStatement
     * 将 List<PolicyConditionDTO> 转换为 JSON 字符串存储到数据库
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<PolicyConditionDTO> parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = JSONUtil.toJsonStr(parameter);
            ps.setString(i, json);
        } catch (Exception e) {
            throw new SQLException("Error converting List<PolicyConditionDTO> to JSON string", e);
        }
    }

    /**
     * 根据列名从 ResultSet 获取值
     * 将数据库中的 JSON 字符串转换为 List<PolicyConditionDTO>
     */
    @Override
    public List<PolicyConditionDTO> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseConditions(json);
    }

    /**
     * 根据列索引从 ResultSet 获取值
     * 将数据库中的 JSON 字符串转换为 List<PolicyConditionDTO>
     */
    @Override
    public List<PolicyConditionDTO> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseConditions(json);
    }

    /**
     * 从 CallableStatement 获取值
     * 将数据库中的 JSON 字符串转换为 List<PolicyConditionDTO>
     */
    @Override
    public List<PolicyConditionDTO> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseConditions(json);
    }

    /**
     * 解析 JSON 字符串为 List<PolicyConditionDTO>
     *
     * @param json JSON 字符串
     * @return 解析后的条件列表，解析失败时返回空列表
     */
    private List<PolicyConditionDTO> parseConditions(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return JSONUtil.toList(JSONUtil.parseArray(json), PolicyConditionDTO.class);
        } catch (Exception e) {
            // 记录日志但不抛出异常，返回空列表
            System.err.println("Error parsing JSON to List<PolicyConditionDTO>: " + json + ", error: " + e.getMessage());
            return new ArrayList<>();
        }
    }
}
