package com.telecom.apigateway.config.mybatisplus;

import com.telecom.apigateway.model.enums.HttpFieldEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024-11-01
 */
public class HttpFieldEnumArrayTypeHandler extends BaseTypeHandler<HttpFieldEnum[]> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, HttpFieldEnum[] parameter,
                                    JdbcType jdbcType) throws SQLException {
        // 将枚举数组转换为逗号分隔的字符串存储
        String value = Arrays.stream(parameter).map(Enum::name).reduce((a, b) -> a + "," + b).orElse("");
        ps.setString(i, value);
    }

    @Override
    public HttpFieldEnum[] getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return toEnumArray(value);
    }

    @Override
    public HttpFieldEnum[] getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return toEnumArray(value);
    }

    @Override
    public HttpFieldEnum[] getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return toEnumArray(value);
    }

    private HttpFieldEnum[] toEnumArray(String value) {
        // 将逗号分隔的字符串转换为枚举数组
        return value == null || value.isEmpty() ? new HttpFieldEnum[0] :
                Arrays.stream(value.split(","))
                        .map(HttpFieldEnum::valueOf)
                        .toArray(HttpFieldEnum[]::new);
    }
}
