package com.telecom.apigateway.config;

import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-03-18
 */
@Component("indexNameProvider")
public class IndexNameProvider {
    private final Environment environment;

    public IndexNameProvider(Environment environment) {
        this.environment = environment;
    }

    public String getIndexName() {
        return environment.getProperty("spring.elasticsearch.index-name", "nginx-logs-access");
    }
}
