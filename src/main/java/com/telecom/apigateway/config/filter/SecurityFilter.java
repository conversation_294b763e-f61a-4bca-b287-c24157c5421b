package com.telecom.apigateway.config.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.telecom.apigateway.common.Result;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * xss sql 过滤
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
public class SecurityFilter extends OncePerRequestFilter {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain)
            throws ServletException, IOException {

        HttpServletRequest wrappedRequest = new ContentCachingRequestWrapper(request);
        if (isXss(wrappedRequest)) {
            sendErrorResponse(response, 200, "非法的请求");
            return;
        } else if (isSqlInjection(wrappedRequest)) {
            sendErrorResponse(response, 200, "非法的请求");
            return;
        }
        filterChain.doFilter(wrappedRequest, response);
    }

    private boolean isXss(HttpServletRequest request) {
        String[] parameterNames = request.getParameterMap().keySet().toArray(new String[0]);
        return Arrays.stream(parameterNames)
                .map(request::getParameterValues)
                .flatMap(Arrays::stream)
                .anyMatch(this::isXss);
    }

    private boolean isXss(String value) {
        // 实现XSS检查逻辑, todo 完善规则, 考虑到实际请求参数可能包含 / ,需要设计一个更复杂的规则
        return value != null && value.contains("<") && value.contains(">");
    }

    private boolean isSqlInjection(HttpServletRequest request) {
        String[] parameterNames = request.getParameterMap().keySet().toArray(new String[0]);
        return Arrays.stream(parameterNames)
                .map(request::getParameterValues)
                .flatMap(Arrays::stream)
                .anyMatch(this::isSqlInjection);
    }

    private boolean isSqlInjection(String value) {        // 排除的路径正则表达式
        String EXCLUDED_PATH_REGEX = "(?i)^/[a-z]+(?:/[a-z]+)*$";
        // 创建排除路径的Pattern对象
        Pattern excludedPathPattern = Pattern.compile(EXCLUDED_PATH_REGEX);
        Matcher excludedPathMatcher = excludedPathPattern.matcher(value);

        // 检查是否匹配排除路径
        if (excludedPathMatcher.matches()) {
            return false;
        }

        // 实现SQL注入检查逻辑, 简单检查一些常见的 SQL 关键字
        Pattern pattern = Pattern.compile(
                "\\b(and|exec|insert|select|drop|grant|alter|delete|update|count|chr|mid|master|truncate|char|declare" +
                        "|or)\\b|([*;+'%])");

        Matcher matcher = pattern.matcher(value);
        return matcher.find();
    }

    private void sendErrorResponse(HttpServletResponse response, int statusCode, String errorMessage) throws IOException {
        response.setStatus(statusCode);
        response.setContentType("application/json;charset=UTF-8");
        objectMapper.writeValue(response.getOutputStream(), Result.fail(errorMessage));
    }
}
