package com.telecom.apigateway.config;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2025-04-07
 */
@Slf4j
@Component
public class ElasticsearchIndexInitializer implements CommandLineRunner {
    @Value("${spring.elasticsearch.index-name}")
    public String indexName;

    private final RestHighLevelClient client;

    public ElasticsearchIndexInitializer(RestHighLevelClient client) {
        this.client = client;
    }

    @Override
    public void run(String... args) throws Exception {


        GetIndexRequest getIndexRequest = new GetIndexRequest(indexName);
        boolean exists = client.indices().exists(getIndexRequest, RequestOptions.DEFAULT);

        if (!exists) {
            CreateIndexRequest request = new CreateIndexRequest(indexName);

            // 设置 mapping
            String mappingJson = getMappingJson();
            request.source(mappingJson, XContentType.JSON);

            // 设置 settings（可选）
            request.settings(Settings.builder()
                    .put("index.number_of_shards", 1)
                    .put("index.number_of_replicas", 3));
            CreateIndexResponse createIndexResponse = client.indices().create(request, RequestOptions.DEFAULT);

            if (createIndexResponse.isAcknowledged()) {
                log.info("✅ 创建索引成功：{}", indexName);
            } else {
                log.error("❌ 创建索引失败：{}", indexName);
            }
        } else {
            log.info("ℹ️ 索引已存在：{}", indexName);
        }
    }

    private String getMappingJson() throws IOException {
        ClassPathResource resource = new ClassPathResource("mappings/" + indexName + ".json");
        InputStream inputStream = resource.getInputStream();
        StringBuilder textBuilder = new StringBuilder();
        try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.InputStreamReader(inputStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                textBuilder.append(line);
            }
        }
        return textBuilder.toString();
    }
}
