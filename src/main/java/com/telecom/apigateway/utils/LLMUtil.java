package com.telecom.apigateway.utils;

import cn.hutool.Hutool;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.telecom.apigateway.model.vo.request.LLMRequest;

import java.util.HashMap;

/**
 * @program: APIWG-Service
 * @ClassName LLMUtil
 * @description:
 * @author: Levi
 * @create: 2025-01-22 15:33
 * @Version 1.0
 **/
public class LLMUtil {
public static String llmServer="https://open.bigmodel.cn/api/paas/v4/chat/completions";

    public static String threatAnalysis(LLMRequest request, String requestBody){
        HashMap<String,Object> one=new HashMap<>();
        JSONObject entries = new JSONObject();

        entries.set("model","glm-4-flash");
        JSONObject response_format = new JSONObject();
        response_format.set("type","json_object");
        entries.set("response_format",response_format );
        JSONArray messages = new JSONArray();
        JSONObject msg1=new JSONObject();
        msg1.set("role","user");
        msg1.set("content",requestBody);
        JSONObject msg2=new JSONObject();
        msg2.set("role","user");
        String prompt="";
       //String  prompt="分析前面这个数据包有没有存在安全攻击行为？需要先做urldecode或者base64解码，再分析。### 输出格式如下,请严格按照如下格式仅输出JSON，不要输出Python代码或其他信息,json直接返回对象,也不要加换行符等特殊字符，不要加单引号转义双引号：{'is_attack':1,'attack_method':'具体的攻击行为或攻击风险，需要写全，analysis_reason里面分析出来几种威胁，这个字段都要写多少','analysis_reason':'判断为此攻击行为的原因','defense_method':'防御或者修复方法'},json格式说明：is_attack为0或者1，0代表不存在攻击，1代表存在攻击;attack_method字段是攻击类型，比如SQL注入、XSS攻击、命令执行等，如果有多个攻击行为用,隔开，analysis_reason判断为此攻击行为的原因，defense_method防御或者修复方法。}";
       //String prompt="假如你是安全公司开发的网络告智能研判专家，只返回json格式。中文作为网络安全领域的专家，你将收到一条安全事件信息并判断是否需要处置，最终输出只是json字符串，且json长度包含下列所有得key,不包含其它内容，不能是md格式,纯文本，且没有对key的说明。具体包含一下字段。is_attack:该数据包是否含有攻击行为，为整数，0:否 1:是，analysis_reason:判断这个数据包存在攻击行为、风险行为或者威胁行为的原因和规则。defense_method: 防御或者修复方法";
        Integer llmType = request.getLlmType();
        switch (llmType){
            case 1:
                prompt="分析前面这个数据包有没有存在安全攻击行为？比如是否存在web安全攻击，以及是否存在敏感文件访问等。需要先做urldecode或者base64解码，再分析。### 输出格式如下,请严格按照如下格式仅输出JSON，不要输出Python代码或其他信息,json直接返回对象,也不要加换行符等特殊字符，不要加单引号转义双引号：{'is_attack':1,'analysis_reason':'判断这个数据包存在攻击行为、风险行为或者威胁行为的原因和规则','defense_method':'防御或者修复方法'},json格式说明：is_attack为0或者1，0代表不存在攻击，1代表存在攻击;analysis_reason判断为此攻击行为的原因，defense_method防御或者修复方法。如果不含有攻击行为这个字段就显示暂时未发现攻击行为。如果需要防御修复办法，就给出java和python的核心代码实现}，请中文回答内容";
                prompt="分析前面这个数据包有是否存在SQL注入、XSS注入、命令执行、敏感文件访问、文件上传、CRLF注入、弱口令、CSRF、SSRF、/etc/passwd、whoami或ifconfig系统敏感关键字等web安全攻击行为？需要先做urldecode或者base64解码，再分析。输出格式为json字符串，含有三个字段，第一个字段为is_attack，值为0或者1，代表是否存web安全攻击行为，为0代表不存在攻击，为1代表存在攻击，第二个字段为analysis_reason，值为判断为攻击行为的原因，需要把攻击的具体信息和分析原因写出来。第三个字段为defense_method，代表针对analysis_reason字段的攻击行为进行防御或者修复的具体方法。请严格按照前面给出的json格式输出，并用中文回答我。";
                prompt="分析前面这个数据包有是否存在web安全攻击，比如SQL注入、XSS注入、命令执行、敏感文件访问、文件上传、CRLF注入、弱口令、CSRF、SSRF、/etc/passwd、whoami或ifconfig系统敏感关键字等web安全攻击行为？需要先做urldecode或者base64解码，再进行分析。输出格式为json字符串，含有三个字段，第一个字段为is_attack，值为0或者1，为0代表不存在攻击，为1代表存在攻击；第二个字段为analysis_reason，判断这个数据包存在攻击行为或者威胁行为的具体原因是什么,需要把数据包中的威胁内容也显示出来；第三个字段为defense_method，代表针对analysis_reason字段的攻击行为进行防御或者修复的具体方法。请严格按照前面给出的json格式输出，并用中文回答我。";
                break;
            case 2:
                prompt="分析前面这个数据包内有没有含有敏感信息，比如包括但不限于个人信息、身份证号、详细的家庭住址、座机电话号码、手机电话号码、邮箱地址、车牌号、银行卡号？需要先做urldecode或者base64解码，再分析。### 输出格式如下,请严格按照如下格式仅输出JSON,json直接返回对象,也不要加换行符等特殊字符，不要加单引号转义双引号，返回json的格式如下：{'is_attack':1,'analysis_reason':'判断这个数据包存在敏感信息的原因，需要列出哪些是敏感内容','defense_method':'针对敏感信息的脱敏方法，如果不含有敏感信息，就回复没有敏感信息，不需要脱敏。如果需要存在敏感信息，请给出java和python的核心代码实现该种类型的脱敏'},json格式说明：is_attack为0或者1，0代表不存在敏感信息，1代表存在敏感信息;analysis_reason判断为此含有敏感信息的原因，defense_method针对敏感信息的脱敏具体方法，并带上java代码、python代码实现脱敏的代码，只需要给出核心的代码即可。}，请中文回答内容。";
                prompt="分析前面这个数据包内有没有含有敏感信息，比如包括但不限于身份证号、详细的家庭住址、座机电话号码、手机电话号码、邮箱地址、车牌号、银行卡号等泄露人信息的内容。需要先做urldecode或者base64解码，再分析数据。### 输出格式为json字符串，含有三个字段，第一个字段为is_attack，值为0或者1，代表是否存在敏感信息，为1代表存在敏感信息，为0代表不存在敏感信息。第二个字段为analysis_reason，值为判断为敏感信息的原因，需要把数据包中的敏感信息字段内容也显示出来。第三个字段为defense_method，值为针对前面判断出来的敏感信息的处理方法，以及敏感信息的危害。请严格按照前面给出的json格式输出，并用中文回答我。";

                break;
        }

        msg2.set("content",prompt);
        messages.add(msg1);
        messages.add(msg2);

        entries.set("messages",messages);
/*
        String jsonBody = "{\n" +
                "    \"model\": \"glm-4-flash\",\n" +
                "    \"response_format\": {\n" +
                "        \"type\": \"json_object\"\n" +
                "    },\n" +
                "    \"messages\": [\n" +
                "        {\n" +
                "            \"role\": \"user\",\n" +
              //  "            \"content\": \""+requestBody +"\"" +
                "            \"content\"": requestBody
                "        },\n" +
                "        {\n" +
                "            \"role\": \"user\",\n" +
                "            \"content\": \"分析前面这个数据包有没有存在安全攻击行为？需要先做urldecode或者base64解码，再分析。### 输出格式如下,请严格按照如下格式仅输出JSON，不要输出Python代码或其他信息,json直接返回对象，不要加单引号转义双引号：{\\\"is_attack\\\":\\\"1\\\",\\\"attack_method\\\":\\\"具体的攻击行为\\\",\\\"analysis_reason\\\":\\\"判断为此攻击行为的原因\\\",\\\"defense_method\\\":\\\"防御或者修复方法\\\"}\\\",json说明：is_attack为0或者1，0代表不存在攻击，1代表存在攻击;attack_method字段是攻击类型，比如SQL注入、XSS攻击、命令执行等，如果有多个攻击行为用,隔开，analysis_reason判断为此攻击行为的原因，defense_method防御或者修复方法。\\\"}\"\n" +
                "        }\n" +

                "    ]\n" +
                "}";
                */

        String jsonBody = JSONUtil.toJsonStr(entries);

        System.out.println(jsonBody);
        String result = HttpRequest.post(llmServer).header("Authorization", "Bearer d5c674302ef3140e30102610dc6f4447.wPnii68FZOVzSvWJ").body(jsonBody).execute().body();
       //System.out.println(result);

        Object choices = JSONUtil.parseObj(result).get("choices");
        JSONArray objects = JSONUtil.parseArray(choices);
        Object o = objects.get(0);
        Object message = JSONUtil.parseObj(o).get("message");
        Object content = JSONUtil.parseObj(message).get("content");
//        System.out.println(content);
        return  String.valueOf(content);
    }
    public static void main(String[] args) {
    String requestBody="POST /api/article/listArticle HTTP/1.1 dnt: 1 user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/********* accept: application/json, text/plain, */* referer: http://*************:18081/ host: *************:18081 authorization: null connection: keep-alive cookie: sid=Fe26.2**457f0da9f8231c44210486609313764cc2ee922ba2df25adc02c98d9f7f2a791*644caS_8KTB8mYRqBoqxow*Wf0-wo1nDfZvrD3SEYi927Kq3lTz7GOYOLgKU4K6LVrmvO2_jVHLSy8lzFH2jDJDrI015DBT-yFJ6r0RD36qOEuM3z-yH1WcrEjRO0AZRnHTYAi3hBEHMHJbmPXWefXcrlc-x_NdMG1udd8zpPYacYqvaxyo7eLv0m0IQE8FDWYwL9TS7NzNc-yYq9sWhaRT1jX2Ib7Ru0TsQWYHfBkaNpdMMoVBLgaK8_MK6r-Ivu0**8cb1ffd50fce63e5192afa7acb3a40f32d705d2fde47a6151bcc7eef2fbc3a03*837gXOZx40QNn3qydc4ZH-TPrHrSMDUXZiy6IauaaOc; Authorization=39c2a732-9016-4407-bb8f-0e4034f0d41a accept-language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6 accept-encoding: gzip, deflate origin: http://*************:18081 content-type: application/json content-length: 92 { \"current\": 1, \"size\": 10, \"total\": 0, \"searchKey\": \"\", \"sortId\": null, \"articleSearch\": \"/etc/passwd\" } HTTP/1.1 405 METHOD_NOT_ALLOWED server: Api-Gateway-Master transfer-encoding: chunked content-type: text/html; charset=utf-8 connection: keep-alive 请求已拦截";
    try {
        LLMRequest request=new LLMRequest();
        request.setLlmType(1);
        String s = threatAnalysis(request,requestBody);
        System.out.println(s);
    }
    catch (Exception e){
        System.out.println(requestBody);
        System.out.println("错误message:"+e.getMessage());
        System.out.println("错误 cause:"+e.getCause());
    }
    }
}