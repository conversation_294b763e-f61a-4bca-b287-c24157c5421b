package com.telecom.apigateway.utils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class MapUtil {
    public static<T> Map<String, List<T>> grouping(Function<T, String> chooseKey, Collection<T> originList) {
        return originList.stream().collect(Collectors.groupingBy(chooseKey));
    }

    public static<T> Map<String, T> toMapByUnionParams(Function<T, String> chooseKey, List<T> originList) {
        return originList.stream().collect(Collectors.toMap(chooseKey, Function.identity(), (key1, key2) -> key1));
    }
}
