package com.telecom.apigateway.utils;

import com.telecom.apigateway.model.dto.ApiImportFromFileDTO;
import io.swagger.models.HttpMethod;
import io.swagger.models.Operation;
import io.swagger.models.Swagger;
import io.swagger.parser.SwaggerParser;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Paths;
import io.swagger.v3.parser.OpenAPIV3Parser;

import java.util.ArrayList;
import java.util.List;

/**
 * swagger 文档解析
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
public class SwaggerParserUtils {
    public static List<ApiImportFromFileDTO> parseSwaggerJson(String jsonContent) {
        List<ApiImportFromFileDTO> swaggerInfoList = new ArrayList<>();

        // 解析 OpenAPI 3.x 版本
        OpenAPI openAPI = new OpenAPIV3Parser().readContents(jsonContent, null, null).getOpenAPI();
        if (openAPI != null) {
            Paths paths = openAPI.getPaths();
            if (paths != null) {
                paths.forEach((uri, pathItem) -> pathItem.readOperationsMap().forEach((httpMethod, operation) -> {
                    ApiImportFromFileDTO swaggerInfo = new ApiImportFromFileDTO();
                    swaggerInfo.setUri(uri);
                    swaggerInfo.setHttpMethod(httpMethod.name());
                    swaggerInfo.setName(operation.getSummary());
                    swaggerInfo.setRemark(operation.getDescription());
                    swaggerInfoList.add(swaggerInfo);
                }));
            }
        } else {
            // 或: 解析 Swagger 2.x 版本
            Swagger swagger = new SwaggerParser().parse(jsonContent);
            if (swagger != null) {
                swagger.getPaths().forEach((uri, path) -> {
                    for (HttpMethod httpMethod : path.getOperationMap().keySet()) {
                        Operation operation = path.getOperationMap().get(httpMethod);
                        ApiImportFromFileDTO swaggerInfo = new ApiImportFromFileDTO();
                        swaggerInfo.setUri(uri);
                        swaggerInfo.setHttpMethod(httpMethod.name());
                        swaggerInfo.setName(operation.getSummary());
                        swaggerInfo.setRemark(operation.getDescription());
                        swaggerInfoList.add(swaggerInfo);
                    }
                });
            }
        }

        return swaggerInfoList;
    }
}
