package com.telecom.apigateway.utils;

import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2024-10-30
 */
public class DateTimeUtils {

    public static Pair<LocalDateTime, LocalDateTime> rangeTime(Integer range, String startTimeStr, String endTimeStr) {
        // 默认今天
        LocalDateTime endTime = null;
        LocalDateTime startTime = null;
        if (range != null) {
            return rangeTime(range);
        } else if (StrUtil.isNotEmpty(startTimeStr) && StrUtil.isNotBlank(endTimeStr)) {
            try {
                startTime = LocalDateTime.parse(startTimeStr, DateTimeFormatter.ofPattern(Constant.DATE_TIME_PATTERN));
                endTime = LocalDateTime.parse(endTimeStr, DateTimeFormatter.ofPattern(Constant.DATE_TIME_PATTERN));
            } catch (Exception e) {
                throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "检查日期格式");
            }
        }
        return Pair.of(startTime, endTime);
    }

    public static Pair<LocalDateTime, LocalDateTime> rangeTime(Integer range, LocalDateTime startTime1,
                                                               LocalDateTime endTime1) {
        // 默认今天
        LocalDateTime endTime = null;
        LocalDateTime startTime = null;
        if (range != null) {
            return rangeTime(range);
        } else if (startTime1 != null && endTime1 != null) {
            startTime = startTime1;
            endTime = endTime1;
        }
        return Pair.of(startTime, endTime);
    }

    public static Pair<LocalDateTime, LocalDateTime> rangeTime(Integer range) {
        if (range == null) {
            return Pair.of(null, null);
        }
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = null;
        switch (range) {
            // 一月内
            case Constant.RANGE_MONTH:
                startTime = endTime.minusDays(30);
                break;
            // 7天内
            case Constant.RANGE_WEEK:
                startTime = endTime.minusDays(7);
                break;
            // 1天内
            case Constant.RANGE_DAY:
                startTime = endTime.minusDays(1);
                break;
            // 1小时
            case Constant.RANGE_HOUR:
            default:
                startTime = endTime.minusHours(1);
        }
        return Pair.of(startTime, endTime);
    }

    /**
     * 计算两个时间相差的秒数
     */
    public static long diffSeconds(LocalDateTime startTime, LocalDateTime endTime) {
        return Math.abs(startTime.atZone(ZoneId.systemDefault()).toEpochSecond()
                - endTime.atZone(ZoneId.systemDefault()).toEpochSecond());
    }

    /**
     * 判断一串数字是否为为时间
     * 例如 17301714758 true; 1730171475 false
     */
    public static boolean isUnixTimestamp(long timestamp) {
        // 获取当前时间的秒级和毫秒级时间戳
        long currentTimeSeconds = System.currentTimeMillis() / 1000;
        long currentTimeMillis = System.currentTimeMillis();

        // 判断是否在秒级时间戳范围内
        if (timestamp >= 0 && timestamp <= currentTimeSeconds) {
            return true;
        }
        // 判断是否在毫秒级时间戳范围内
        return timestamp >= 0 && timestamp <= currentTimeMillis;
    }

    public static boolean isUnixTimestamp(String timestamp) {
        try {
            return isUnixTimestamp(Long.parseLong(timestamp));
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static boolean isLastDayOfMonth(LocalDateTime dateTime) {
        LocalDate date = dateTime.toLocalDate();
        return date.getDayOfMonth() == date.lengthOfMonth();
    }

    public static boolean isSameMonth(LocalDateTime dt1, LocalDateTime dt2) {
        return dt1.getYear() == dt2.getYear() && dt1.getMonth() == dt2.getMonth();
    }

}
