package com.telecom.apigateway.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

public class PageUtils {
    public static <P, T> Page<T> convertPage(Page<P> page, List<T> t) {
        Page<T> resultPage = new Page<>();
        resultPage.setRecords(t);
        resultPage.setCurrent(page.getCurrent());
        resultPage.setSize(page.getSize());
        resultPage.setTotal(page.getTotal());
        resultPage.setPages(page.getPages());
        return resultPage;
    }
}
