package com.telecom.apigateway.utils;

import javax.validation.*;
import java.util.Set;


public class ValidationUtil {
    private static final Validator validator;

    static {
        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            validator = factory.getValidator();
        } catch (Exception e) {
            throw new ExceptionInInitializerError("Failed to initialize Validator: " + e.getMessage());

        }
    }

    /**
     * 校验对象参数
     *
     * @param <T>    要校验的对象类型
     * @param object 需要校验的对象
     * @throws ConstraintViolationException 校验不通过时抛出异常
     */
    public static <T> void validate(T object) throws ConstraintViolationException {
        Set<ConstraintViolation<T>> violationSet = validator.validate(object);
        if (!violationSet.isEmpty()) {
            StringBuilder msgBuilder = new StringBuilder("参数校验失败：");
            for (ConstraintViolation<T> violation : violationSet) {
                msgBuilder.append(violation.getPropertyPath())
                        .append(" ")
                        .append(violation.getMessage())
                        .append("; ");
            }
            throw new ConstraintViolationException(msgBuilder.toString(), violationSet);
        }
    }
}
