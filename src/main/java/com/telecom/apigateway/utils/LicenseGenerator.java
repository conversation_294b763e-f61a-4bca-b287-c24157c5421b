package com.telecom.apigateway.utils;
import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Date;

import org.springframework.stereotype.Component;
/**
 * @program: APIWG-Service
 * @ClassName LicenseGenerator
 * @description:
 * @author: Levi
 * @create: 2025-03-28 11:03
 * @Version 1.0
 **/
@Component
public class LicenseGenerator {
    private static final String ALGORITHM = "RSA";

    public String generateLicenseContent(String companyName, Date expirationDate,
                                         String contactName, String contactPhone, String privateKeyBase64) throws Exception {
        String licenseContent = String.format("%s,%d,%s,%s", companyName, expirationDate.getTime(), contactName, contactPhone);
        PrivateKey privateKey = getPrivateKey(privateKeyBase64);
        return Base64.getEncoder().encodeToString(encrypt(licenseContent, privateKey));
    }

    private PrivateKey getPrivateKey(String privateKeyBase64) throws Exception {
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyBase64);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

    private byte[] encrypt(String plainText, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        return cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
    }
}