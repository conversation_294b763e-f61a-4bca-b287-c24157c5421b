package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.entity.User;
import com.telecom.apigateway.model.vo.request.QueryUserRequest;
import com.telecom.apigateway.model.vo.response.QueryUserResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserMapper extends BaseMapper<User> {
    /**
     * 查询用户列表
     * @param request 查询请求
     * @return 用户列表
     */
    Page<QueryUserResponse> queryUsers(Page<?> page, @Param("request") QueryUserRequest request);

    /**
     * 查询所有部门列表
     * @return 部门列表
     */
    List<String> queryAllDepartments();
}
