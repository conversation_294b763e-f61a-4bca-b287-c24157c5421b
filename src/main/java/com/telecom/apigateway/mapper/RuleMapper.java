package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.entity.Rule;
import com.telecom.apigateway.model.vo.response.QueryCveRuleResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RuleMapper extends BaseMapper<Rule> {
    
    /**
     * 分页查询CVE规则
     *
     * @param page 分页参数
     * @param cveName CVE名称
     * @param status 状态
     * @param category 类别
     * @return 分页结果
     */
    Page<QueryCveRuleResponse> queryCveRuleWithConfig(
            Page<?> page,
            @Param("cveName") String cveName,
            @Param("status") String status,
            @Param("category") String category
    );
}
