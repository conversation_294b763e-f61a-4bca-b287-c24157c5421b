package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.entity.OperationLog;
import com.telecom.apigateway.model.vo.response.OperationLogResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 操作日志Mapper接口
 */
@Mapper
public interface OperationLogMapper extends BaseMapper<OperationLog> {
    
    /**
     * 分页查询操作日志
     *
     * @param page 分页参数
     * @param username 用户名
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    Page<OperationLogResponse> queryPage(
            Page<?> page,
            @Param("username") String username,
            @Param("operationType") List<String> operationType,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("ip") String ip
    );
}
