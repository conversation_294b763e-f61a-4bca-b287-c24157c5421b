package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.dto.ApplicationQueryDTO;
import com.telecom.apigateway.model.entity.Application;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ApplicationMapper extends BaseMapper<Application> {
    List<Application> getWithChildren(List<String> applicationIds);

    Application getRootApplication(String applicationId);

    List<Application> listWithChainName();

    /**
     * 分页查询应用列表
     * @param page 分页对象
     * @param queryDTO 查询条件
     * @return 查询结果
     */
    Page<Application> selectApplicationPage(@Param("page") Page<Application> page, @Param("query") ApplicationQueryDTO queryDTO);

    /**
     * 根据host和port查询应用，使用JSONB数组格式
     * @param host 主机地址
     * @param port 端口号
     * @return 匹配的应用列表
     */
    List<Application> getByUrl(@Param("host") String host, @Param("port") String port);
}
