package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.DataScope;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.ApiInfoTree;
import com.telecom.apigateway.model.vo.request.QueryApiRequest;
import com.telecom.apigateway.model.vo.response.ApiQueryResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR> Denty
 * @date : 2024/8/13
 */
@Mapper
public interface ApiInfoMapper extends BaseMapper<ApiInfo> {

    List<LinkedHashMap<String, String>> queryExportDataByIds(List<String> ids);

    @DataScope(columnAlias = "app_id")
    IPage<ApiQueryResponse> query(Page<ApiInfo> pageInfo, QueryApiRequest query);

    @DataScope(columnAlias = "app_id")
    List<ApiQueryResponse> query(@Param("query") QueryApiRequest query);

    List<ApiQueryResponse> queryByIds(List<String> ids, boolean containDeleted);

    List<ApiInfoTree> queryParentAndChildrenByIds();
}
