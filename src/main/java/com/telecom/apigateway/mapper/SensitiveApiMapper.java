package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.entity.SensitiveApi;
import com.telecom.apigateway.model.vo.request.QuerySensitiveApiRequest;
import com.telecom.apigateway.model.vo.response.SensitiveApiQueryResponse;
import com.telecom.apigateway.model.vo.response.StatCountResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-29
 */
@Mapper
public interface SensitiveApiMapper extends BaseMapper<SensitiveApi> {
    /**
     * 分页查询
     * 数据权限展示无法使用 @DataScope
     * 在前面处理
     */
    IPage<SensitiveApiQueryResponse> queryPage(Page<T> page, QuerySensitiveApiRequest query);

    Integer getMaxApiSensitiveLevelByApiId(@Param("apiId") String apiId);

    List<StatCountResponse> statLevelCount(List<String> appIds, LocalDateTime time);

    List<StatCountResponse> statRuleCount(List<String> appIds, LocalDateTime time);

    void resetLevel(String apiId);
}
