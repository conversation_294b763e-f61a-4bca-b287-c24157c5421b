package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleTrigger;
import com.telecom.apigateway.model.vo.request.QueryAbRequest;
import com.telecom.apigateway.model.vo.response.QueryAbResponse;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Mapper
public interface AbnormalBehaviorRuleTriggerMapper extends BaseMapper<AbnormalBehaviorRuleTrigger> {
    List<QueryAbResponse> queryPage(QueryAbRequest ruleRequest);
}
