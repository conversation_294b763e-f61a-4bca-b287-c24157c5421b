package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.entity.RiskLogNew;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface RiskLogNewMapper extends BaseMapper<RiskLogNew> {

    /**
     * 按(log_id, rule_type)去重分页查询
     */
    Page<RiskLogNew> selectUniquePageByCondition(
            Page<RiskLogNew> page,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("appIds") List<String> appIds,
            @Param("attackTypes") List<String> attackTypes,
            @Param("clientIp") String clientIp,
            @Param("uri") String uri,
            @Param("riskLogId") String riskLogId,
            @Param("dealt") Boolean dealt,
            @Param("isFalsePositive") Boolean isFalsePositive,
            @Param("riskLevels") List<Integer> riskLevels
    );

    /**
     * 按(log_id, rule_type)去重统计总数
     */
    Long countUniqueByCondition(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("appIds") List<String> appIds,
            @Param("attackTypes") List<String> attackTypes,
            @Param("clientIp") String clientIp,
            @Param("uri") String uri,
            @Param("riskLogId") String riskLogId,
            @Param("dealt") Boolean dealt,
            @Param("isFalsePositive") Boolean isFalsePositive,
            @Param("riskLevels") List<Integer> riskLevels
    );
}
