package com.telecom.apigateway.common.excption;

import com.telecom.apigateway.common.ResultCodeEnum;
import lombok.Getter;

public class BusinessException extends RuntimeException implements IErrorCode {
    private String errorCode;
    private String message;
    @Getter
    private ResultCodeEnum errorCodeEnum;


    public BusinessException() {
        super();
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(ResultCodeEnum codeEnum) {
        this.errorCode = codeEnum.getDetailCode();
        this.message = codeEnum.getErrorMessage();
        this.errorCodeEnum = codeEnum;
    }

    public BusinessException(ResultCodeEnum codeEnum, String message) {
        this.errorCode = codeEnum.getDetailCode();
        String msg = codeEnum.getErrorMessage();
        if (msg.endsWith("{}")) {
            msg = msg.replace("{}", message);
        }
        this.message = msg;
        this.errorCodeEnum = codeEnum;
    }

    public BusinessException(String errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }

    @Override
    public String getDetailCode() {
        return errorCode;
    }

    @Override
    public String getErrorMessage() {
        return message;
    }
}
