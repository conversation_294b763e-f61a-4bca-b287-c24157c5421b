package com.telecom.apigateway.common.excption;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import com.fasterxml.jackson.core.JacksonException;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.common.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.SQLException;


@ResponseBody
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public Result<Void> handleValidException(BindException exception) {
        log.error("[handleValidationExceptions]", exception);
        StringBuilder stringBuilder = new StringBuilder();
        exception.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMsg = error.getDefaultMessage();
            stringBuilder.append(fieldName).append(":").append(errorMsg).append(";");
        });

        return Result.fail(stringBuilder.toString());
    }

    @ExceptionHandler(value = BusinessException.class)
    public Result<Void> exceptionHandler(BusinessException e) {
        if (e.getErrorCodeEnum() != null) {
            return Result.fail(e);
        }
        return Result.fail(e.getMessage());
    }

    @ExceptionHandler(value = NotLoginException.class)
    public Result<Void> exceptionHandler(HttpServletResponse response, NotLoginException e) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        return Result.fail(e.getMessage());
    }

    @ExceptionHandler({UnauthorizationException.class, NotPermissionException.class})
    public Result<Void> exceptionHandler(HttpServletResponse response, Exception e) {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        return Result.fail(e.getMessage());
    }

    @ExceptionHandler({JacksonException.class, HttpMessageConversionException.class})
    public Result<Void> jsonParseExceptionHandler(Exception e) {
        log.error("请求数据解析错误: {}", e.getMessage(), e);
        return Result.fail(ResultCodeEnum.REQUEST_PARSE_ERROR);
    }

    @ExceptionHandler(value = Exception.class)
    public Result<Void> exceptionHandler(HttpServletRequest req, Exception e) {
        log.error("未知异常:{}", e.getMessage(), e);
        return Result.fail(e.getMessage());
    }

    @ExceptionHandler({DataAccessException.class, SQLException.class})
    public Result<Void> exceptionHandler(Exception e) {
        log.error("数据库访问异常: {}", e.getMessage(), e);
        return Result.fail(ResultCodeEnum.DB_ERROR);
    }

}
