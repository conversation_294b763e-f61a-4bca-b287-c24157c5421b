package com.telecom.apigateway.common;

import cn.dev33.satoken.annotation.SaMode;
import cn.dev33.satoken.exception.NotPermissionException;
import com.telecom.apigateway.service.ApplicationPermissionService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;

@Aspect
@Slf4j
@Component
@RequiredArgsConstructor
public class CheckApiPermissionHandler {
    private final ApplicationPermissionService applicationPermissionService;

    @Pointcut("@annotation(com.telecom.apigateway.common.CheckApiPermission) || @within(com.telecom.apigateway.common.CheckApiPermission)")
    public void dataScopePointCut() {}

    @Before("dataScopePointCut()")
    public void doBefore(JoinPoint point) {
        // 获取并合并类和方法上的注解
        MergedPermissions mergedPermissions = getMergedPermissions(point);
        if (mergedPermissions.exclude) {
            return;
        }
        String[] permissions = mergedPermissions.getPermissions();
        SaMode mode = mergedPermissions.getMode();

        if (permissions != null && permissions.length != 0) {
            List<String> permissionList = applicationPermissionService.getPagePermission();

            if (SaMode.AND.equals(mode)) {
                for (String permission : permissions) {
                    if (!permissionList.contains(permission)) {
                        throw new NotPermissionException("未授权" + permission);
                    }
                }
            } else {
                boolean hasPermission = false;
                for (String permission : permissions) {
                    if (permissionList.contains(permission)) {
                        hasPermission = true;
                        break;
                    }
                }
                if (!hasPermission) {
                    throw new NotPermissionException("未授权" + Arrays.toString(permissions));
                }
            }
        }
    }

    private MergedPermissions getMergedPermissions(JoinPoint point) {
        Signature signature = point.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        Class<?> targetClass = method.getDeclaringClass();

        // 获取类上的注解
        CheckApiPermission classAnnotation = targetClass.getAnnotation(CheckApiPermission.class);
        // 获取方法上的注解
        CheckApiPermission methodAnnotation = method.getAnnotation(CheckApiPermission.class);

        Set<String> mergedPermissions = new HashSet<>();
        SaMode mode = SaMode.OR; // 默认模式
        Boolean exclude = false;

        // 添加类级别的权限
        if (classAnnotation != null) {
            Collections.addAll(mergedPermissions, classAnnotation.value());
            // 如果类上指定了AND模式，则使用AND模式
            if (SaMode.AND.equals(classAnnotation.mode())) {
                mode = SaMode.AND;
            }
        }

        // 添加方法级别的权限
        if (methodAnnotation != null) {
            Collections.addAll(mergedPermissions, methodAnnotation.value());
            // 方法上的模式优先级更高
            mode = methodAnnotation.mode();
            exclude = methodAnnotation.exclude();
        }

        return new MergedPermissions(mergedPermissions.toArray(new String[0]), mode, exclude);
    }

    // 用于存储合并后的权限和模式
    @Getter
    private static class MergedPermissions {
        private final String[] permissions;
        private final SaMode mode;
        private boolean exclude = false;

        public MergedPermissions(String[] permissions, SaMode mode) {
            this.permissions = permissions;
            this.mode = mode;
        }

        public MergedPermissions(String[] permissions, SaMode mode, boolean exclude) {
            this.permissions = permissions;
            this.mode = mode;
            this.exclude = exclude;
        }
    }
}