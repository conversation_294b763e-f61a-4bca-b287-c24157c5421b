package com.telecom.apigateway.common;

import cn.dev33.satoken.stp.StpUtil;
import com.telecom.apigateway.common.excption.BusinessException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class RequirePermissionAspect {

    @Around("@annotation(com.telecom.apigateway.common.RequirePermission)")
    public Object checkPermission(ProceedingJoinPoint point) throws Throwable {
        // 获取方法参数
        Object[] args = point.getArgs();
        if (args == null || args.length == 0) {
            throw new BusinessException("方法参数不能为空");
        }
        
        // 获取permission参数
        String permission = null;
        for (Object arg : args) {
            if (arg instanceof String) {
                permission = (String) arg;
                break;
            }
        }
        
        if (permission == null) {
            throw new BusinessException("未找到permission参数");
        }
        
        // 获取当前登录用户ID
        String loginId = StpUtil.getLoginIdAsString();
        boolean isAdmin = loginId.equals("admin") || StpUtil.getRoleList().contains("admin");
        
        // 验证权限
        if (!StpUtil.hasPermission(loginId, permission) && !isAdmin) {
            throw new BusinessException("暂无查看权限，如有疑问请联系管理员");
        }
        
        // 执行原方法
        return point.proceed();
    }
}
