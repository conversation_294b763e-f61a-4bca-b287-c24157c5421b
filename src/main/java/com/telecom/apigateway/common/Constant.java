package com.telecom.apigateway.common;

/**
 * <AUTHOR>
 * @date 2024-08-13
 */
public class Constant {


    public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_PATTERN = "yyyy-MM-dd";
    public static final String MONTH_PATTERN = "yyyy-MM";

    /**
     * 时间范围
     */
    public static final int RANGE_HOUR = 1;
    public static final int RANGE_DAY = 2;
    public static final int RANGE_WEEK = 3;
    public static final int RANGE_MONTH = 4;


    public static final String UNKNOWN_REGION = "未知";
    public static final String INNER_REGION = "局域网";

    public static final String UNKNOWN_REGION_CODE = "UNKNOWN";
    public static final String INNER_REGION_CODE = "INNER";

    public static final String UNKNOWN_FIELD = "UNKNOWN";


    public static class Api {


        /**
         * 1.	201 Created：请求成功并且服务器创建了新的资源，常见于 POST 请求。
         * 2.	202 Accepted：请求已经接受，但尚未处理。适用于异步操作，常见于批量处理、后台处理的场景。
         * 3.	204 No Content：请求成功，但服务器不需要返回任何实体内容，通常用于 DELETE 请求。
         * 4.	206 Partial Content：服务器成功处理了部分请求，通常用于断点续传或部分下载（比如使用 Range 头部）。
         * 5.	301 Moved Permanently：请求的资源已被永久移动到新位置，客户端应当使用新的 URL 来访问资源。
         * 6.	302 Found：请求的资源临时从其他位置响应，客户端应继续使用原 URL，但资源临时从其他位置返回内容。
         * 7.	304 Not Modified：客户端缓存的资源是最新的，服务器无需返回资源内容，常见于使用 ETag 或 Last-Modified 头部的场景。
         */
        public static Integer[] NORMAL_STATUS_CODES = {200, 201, 202, 204, 206, 301, 302, 304};

        /**
         * 接口来源, 0自动 1手动
         */
        public static final Integer SOURCE_AUTO = 0;
        public static final Integer SOURCE_MANUAL = 1;
        public static final Integer SOURCE_APP_MERGE = 2;

        /**
         * 用户应用请求的资源类型, file/api
         */
        public static final String REQUEST_RESOURCE_TYPE_FILE = "file";
        public static final String REQUEST_RESOURCE_TYPE_API = "api";

        /**
         * 未识别的应用 id
         */
        public static final String UNRECOGNIZED_APPLICATION_ID = "unrecognized";

        /**
         * 涉敏等级
         */
        public static final Integer SENSITIVE_LEVEL_SAFE = 0;
        public static final Integer SENSITIVE_LEVEL_LOW = 1;
        public static final Integer SENSITIVE_LEVEL_MID = 2;
        public static final Integer SENSITIVE_LEVEL_HIGH = 3;

        /**
         * api 的 查询时间, 指定查询的时间类型
         */
        public static final String RANGE_TYPE_DISCOVER = "discoverTime";
        public static final String RANGE_TYPE_ONLINE = "onlineTime";
        public static final String RANGE_TYPE_UPDATE = "updateTime";
        public static final String RANGE_TYPE_DISCOVER_DB = "discover_time";
        public static final String RANGE_TYPE_ONLINE_DB = "online_time";
        public static final String RANGE_TYPE_UPDATE_DB = "update_time";

    }

    public static class Role {
        public static final String ADMIN = "admin";
    }
}
