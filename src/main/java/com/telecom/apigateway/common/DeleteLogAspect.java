package com.telecom.apigateway.common;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.service.OperationLogService;
import com.telecom.apigateway.utils.IpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.ApplicationContext;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Objects;

@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class DeleteLogAspect {

    private final OperationLogService logService;
    private final ApplicationContext applicationContext;
    private final ExpressionParser parser = new SpelExpressionParser();
    private final LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();

    @Around("@annotation(com.telecom.apigateway.common.DeleteLogAnnotation)")
    public Object logDelete(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        Throwable targetException = null;
        String logDescription = null;

        try {
            String username = StpUtil.getLoginIdAsString();
            String ip = getClientIP();

            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            DeleteLogAnnotation annotation = method.getAnnotation(DeleteLogAnnotation.class);
            Object[] args = joinPoint.getArgs();

            StandardEvaluationContext context = createEvaluationContext(method, args);
            context.setVariable("currentUser", username);

            ResourceTypeEnum resourceType = annotation.resourceType();
            
            // 在执行删除操作之前先获取要删除的数据信息
            try {
                String displayName = parseDisplayName(annotation.displayName(), context);
                logDescription = "删除 " + resourceType.getName() + ": " + displayName;
            } catch (Exception logException) {
                log.warn("获取删除前数据信息失败，将在执行后尝试获取: {}", logException.getMessage());
                logDescription = null; // 标记需要在执行后再次尝试
            }

            // 执行删除操作
            try {
                result = joinPoint.proceed();
                context.setVariable("result", result);
            } catch (Throwable e) {
                targetException = e;
                context.setVariable("exception", e);
            }

            // 只有删除操作成功时才记录删除日志
            if (targetException == null) {
                try {
                    // 如果之前获取信息失败，现在再次尝试（可能有些方法会返回被删除的对象）
                    if (logDescription == null) {
                        String displayName = parseDisplayName(annotation.displayName(), context);
                        logDescription = "删除 " + resourceType.getName() + ": " + displayName;
                    }

                    logService.createLog(username, OperationTypeEnum.DELETE, resourceType, logDescription, ip);
                } catch (Exception logException) {
                    log.error("记录删除日志失败: {}", logException.getMessage(), logException);
                }
            }

        } catch (Exception e) {
            log.error("删除日志切面处理失败: {}", e.getMessage(), e);
        }

        if (targetException != null) {
            throw targetException;
        }

        return result;
    }

    private StandardEvaluationContext createEvaluationContext(Method method, Object[] args) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setBeanResolver(new BeanFactoryResolver(applicationContext));

        String[] paramNames = discoverer.getParameterNames(method);
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }
        return context;
    }

    private String parseDisplayName(String displayNameSpel, StandardEvaluationContext context) {
        if (StrUtil.isBlank(displayNameSpel)) {
            return "";
        }

        try {
            Expression exp = parser.parseExpression(displayNameSpel);
            Object value = exp.getValue(context);
            if (Objects.isNull(value)) {
                throw new IllegalArgumentException();
            }
            return value.toString();
        } catch (Exception e) {
            log.error("解析显示名称失败: {} -> {}", displayNameSpel, e.getMessage());
            throw new RuntimeException("解析显示名称失败: " + e.getMessage(), e);
        }
    }

    private String getClientIP() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return IpUtils.getClientIp(request);
            }
        } catch (Exception e) {
            log.warn("获取客户端 IP 失败: {}", e.getMessage());
        }
        return "unknown";
    }
} 