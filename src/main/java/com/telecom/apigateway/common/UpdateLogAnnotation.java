package com.telecom.apigateway.common;

import com.telecom.apigateway.model.enums.ResourceTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 更新操作日志注解
 * 专门用于记录更新操作，通过方法执行前后调用查询方法获取数据变更
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface UpdateLogAnnotation {

    /**
     * 资源类型
     */
    ResourceTypeEnum resourceType();

    /**
     * 资源显示名称，支持SpEL表达式
     * 例如：#{#user.username}、#{#result.name}
     */
    String displayName() default "";

    /**
     * 查询方法的SpEL表达式，用于获取更新前的数据
     * 例如：@userService.findById(#id)、@orderService.getByOrderNo(#orderNo)
     */
    String queryMethod();

    /**
     * 查询更新后数据的SpEL表达式（可选）
     * 如果不提供，则使用queryMethod查询更新后的数据
     * 例如：@userService.findById(#id)、@orderService.getByOrderNo(#orderNo)
     */
    String afterQueryMethod() default "";

    /**
     * 排除的字段，这些字段不参与变更比较
     * 默认已排除常见的时间字段：updateTime、createTime等
     */
    String[] excludeFields() default {};
} 