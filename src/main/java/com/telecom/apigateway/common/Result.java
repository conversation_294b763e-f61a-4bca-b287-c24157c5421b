package com.telecom.apigateway.common;

import com.telecom.apigateway.common.excption.IErrorCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@Schema(name = "result", description = "通用返回封装")
public class Result<T> {
    @Schema(name = "code", description = "错误编号，方便前端对错误类型进行处理")
    private String code;

    @Schema(name = "msg", description = "错误信息，用于提示当前错误的原因")
    private String msg;

    @Schema(name = "data", description = "数据封装")
    private T data;

    @Schema(name = "success", description = "是否成功")
    private Boolean success;


    public static <T> Result<T> success(T data) {
        return new ResultBuilder<T>()
                .code("SUC0000")
                .success(true)
                .data(data)
                .msg("操作成功")
                .build();
    }

    public static Result<Void> fail(String msg) {
        return new ResultBuilder<Void>()
                .code(ResultCodeEnum.FAIL.getCode())
                .success(false)
                .msg(msg)
                .build();
    }

    public static Result<Void> fail(IErrorCode errorCodeEnum) {
        return new ResultBuilder<Void>()
                .code(errorCodeEnum.getDetailCode())
                .success(false)
                .msg(errorCodeEnum.getErrorMessage())
                .build();
    }
}
