package com.telecom.apigateway.common;

import lombok.Getter;

@Getter
public enum AttackTypeEnum {
    // SQL 注入攻击规则
    OTHERS("000000", "其他攻击", "其他攻击"),
    SQL_INJECTION("942190", "SQL 注入攻击", "检测 SQL 注入攻击模式，如使用常见的 SQL 关键字或注释符号。"),
    SQLI_UNION("942210", "SQLi UNION 攻击", "检测 SQL 注入中使用 UNION 关键字的情况，企图合并多个查询结果。"),
    SQLI_ERROR_MESSAGE("942260", "SQL 错误消息泄漏", "检测应用程序返回的 SQL 错误消息，可能包含敏感信息。"),

    // XSS 攻击规则
    XSS_ATTACK("941100", "跨站脚本攻击", "检测潜在的跨站脚本 (XSS) 攻击，通常是通过输入 HTML 或 JavaScript 代码。"),
    XSS_STORED("941120", "存储型 XSS 攻击", "检测存储在数据库或文件系统中的 XSS 攻击。"),
    XSS_REFLECTED("941130", "反射型 XSS 攻击", "检测反射型 XSS 攻击，通常是通过 URL 或表单提交传递的恶意脚本。"),

    // CSRF 攻击规则
    CSRF_ATTACK("949110", "跨站请求伪造", "检测表单提交中的跨站请求伪造 (CSRF) 漏洞。"),

    // 远程代码执行（RCE）规则
    REMOTE_CODE_EXECUTION("932150", "远程代码执行", "检测可能的远程代码执行 (RCE) 攻击模式。"),

    // 命令注入规则
    COMMAND_INJECTION("932100", "命令注入", "检测可能的命令注入攻击，通过用户输入来执行系统命令。"),

    // 文件上传攻击规则
    FILE_UPLOAD("920240", "文件上传攻击", "检测恶意文件上传企图，防止未经授权的文件被上传到服务器。"),

    // 路径遍历规则
    PATH_TRAVERSAL("930110", "路径遍历", "检测路径遍历攻击，防止用户访问未授权的文件或目录。"),

    // 文件包含攻击规则
    FILE_INCLUSION("930120", "文件包含攻击", "检测文件包含漏洞，通过输入文件路径执行文件操作。"),

    // XML 外部实体攻击 (XXE) 规则
    XXE_ATTACK("920170", "XML 外部实体攻击", "检测 XML 外部实体攻击，通常用于攻击 XML 解析器。"),

    // 拒绝服务（DoS）攻击规则
    DOS_ATTACK("980130", "拒绝服务攻击", "检测可能的拒绝服务（DoS）攻击，防止过多的请求压垮服务器。"),

    // 信息泄露规则
    INFO_LEAKAGE("950901", "信息泄露", "检测返回的错误消息、调试信息或堆栈跟踪中可能泄露的敏感信息。"),

    // 用户认证攻击规则
    BRUTE_FORCE("921180", "暴力破解", "检测暴力破解登录尝试，防止用户账户被暴力破解。"),

    // HTTP 协议规则
    HTTP_PROTOCOL_VIOLATION("920350", "HTTP 协议违规", "检测不符合 HTTP 协议的请求或响应。"),
    HTTP_RESPONSE_SPLITTING("921110", "HTTP 响应拆分", "检测 HTTP 响应拆分攻击，通过在响应头中注入 CRLF 字符来操纵服务器的响应。"),

    // Cookie 相关规则
    COOKIE_MANIPULATION("942432", "Cookie 操作", "检测对 cookie 的非法修改或篡改。"),

    // 内容安全策略（CSP）规则
    CSP_VIOLATION("920480", "内容安全策略违规", "检测内容安全策略 (CSP) 违反，如未使用 HTTPS 加载资源。"),
    ;

    // 获取规则 ID
    private final String ruleId;
    // 获取规则描述
    private final String description;
    // 获取详细信息
    private final String details;

    // 构造函数
    AttackTypeEnum(String ruleId, String description, String details) {
        this.ruleId = ruleId;
        this.description = description;
        this.details = details;

    }

    @Override
    public String toString() {
        return String.format("规则 ID: %s, 描述: %s, 详情: %s", ruleId, description, details);
    }

    // 示例：可以根据规则 ID 获取对应的规则枚举常量
    public static AttackTypeEnum getByRuleId(String ruleId) {
        for (AttackTypeEnum rule : AttackTypeEnum.values()) {
            if (rule.getRuleId().equals(ruleId)) {
                return rule;
            }
        }
        return OTHERS;
    }
}
