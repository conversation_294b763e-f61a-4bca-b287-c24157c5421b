package com.telecom.apigateway.common;

import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface OperationLogAnnotation {
    /**
     * 操作类型（登录、增加、删除、修改、查看）
     */
    OperationTypeEnum operationType();

    /**
     * 资源类型（应用、资产、规则等）
     */
    ResourceTypeEnum resourceType();

    /**
     * 操作描述模板
     * 可以使用占位符，如：{0}, {1}等，通过SpEL表达式引用方法参数或返回值
     */
    String description() default "";

    /**
     * 资源名称的SpEL表达式，用于从方法参数或返回值中提取资源名称
     * 例如：#result.name 或 #args[0].name
     */
    String resourceNameSpel() default "";

    String[] spelArgs() default {};
}