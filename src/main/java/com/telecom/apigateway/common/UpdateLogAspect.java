package com.telecom.apigateway.common;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.service.OperationLogService;
import com.telecom.apigateway.utils.IpUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.ApplicationContext;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class UpdateLogAspect {

    private final OperationLogService logService;
    private final ApplicationContext applicationContext;
    private final ExpressionParser parser = new SpelExpressionParser();
    private final LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();

    @Around("@annotation(com.telecom.apigateway.common.UpdateLogAnnotation)")
    public Object logUpdate(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        Throwable targetException = null;

        try {
            String username = StpUtil.getLoginIdAsString();
            String ip = getClientIP();

            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            UpdateLogAnnotation annotation = method.getAnnotation(UpdateLogAnnotation.class);
            Object[] args = joinPoint.getArgs();

            StandardEvaluationContext context = createEvaluationContext(method, args);
            context.setVariable("currentUser", username);

            Object beforeData = null;
            try {
                beforeData = executeQueryMethod(annotation.queryMethod(), context);
            } catch (Exception e) {
                log.warn("获取更新前数据失败: {}", e.getMessage());
            }

            try {
                result = joinPoint.proceed();
                context.setVariable("result", result);
            } catch (Throwable e) {
                targetException = e;
            }

            if (targetException == null) {
                Object afterData = null;
                try {
                    String afterQuery = StrUtil.isNotBlank(annotation.afterQueryMethod())
                            ? annotation.afterQueryMethod()
                            : annotation.queryMethod();
                    afterData = executeQueryMethod(afterQuery, context);
                } catch (Exception e) {
                    log.warn("获取更新后数据失败: {}", e.getMessage());
                }

                ResourceTypeEnum resourceType = annotation.resourceType();
                try {
                    if (beforeData != null && afterData != null) {
                        String changes = buildChangeDescription(beforeData, afterData, annotation.excludeFields());

                        if (StrUtil.isNotBlank(changes)) {
                            String displayName = parseDisplayName(annotation.displayName(), context);
                            String description = buildDescription(resourceType.getName(), displayName, changes);
                            logService.createLog(username, OperationTypeEnum.UPDATE, resourceType, description, ip);
                        }
                    } else {
                        log.warn("无法获取完整的更新前后数据，跳过日志记录");
                    }
                } catch (Exception logException) {
                    log.error("记录更新日志失败: {}", logException.getMessage(), logException);
                }
            }

        } catch (Exception e) {
            log.error("更新日志切面处理失败: {}", e.getMessage(), e);
        }

        if (targetException != null) {
            throw targetException;
        }

        return result;
    }

    private StandardEvaluationContext createEvaluationContext(Method method, Object[] args) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setBeanResolver(new BeanFactoryResolver(applicationContext));

        String[] paramNames = discoverer.getParameterNames(method);
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }
        return context;
    }

    private Object executeQueryMethod(String queryMethodSpel, StandardEvaluationContext context) {
        if (StrUtil.isBlank(queryMethodSpel)) {
            return null;
        }

        try {
            Expression exp = parser.parseExpression(queryMethodSpel);
            Object result = exp.getValue(context);
            
            if (result != null) {
                try {
                    return ObjectUtil.cloneByStream(result);
                } catch (Exception cloneException) {
                    log.warn("对象克隆失败，使用原对象: {}", cloneException.getMessage());
                    return result;
                }
            }
            return result;
        } catch (Exception e) {
            log.warn("执行查询方法失败: {} -> {}", queryMethodSpel, e.getMessage());
            return null;
        }
    }

    private String parseDisplayName(String displayNameSpel, StandardEvaluationContext context) {
        if (StrUtil.isBlank(displayNameSpel)) {
            return displayNameSpel;
        }

        try {
            Expression exp = parser.parseExpression(displayNameSpel);
            Object value = exp.getValue(context);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            log.error("解析显示名称失败: {} -> 错误详情: {}", displayNameSpel, e.getMessage(), e);
            try {
                Object resultObj = context.lookupVariable("result");
                if (resultObj != null) {
                    log.error("result 对象类型: {}, 内容: {}", resultObj.getClass().getName(), resultObj);
                } else {
                    log.error("result 对象为 null");
                }
            } catch (Exception innerE) {
                log.error("无法获取 result 对象信息: {}", innerE.getMessage());
            }
            return "";
        }
    }

    private String buildDescription(String resourceName, String displayName, String changes) {
        return resourceName + ": 修改了 " + displayName + " , 修改内容: " + changes;
    }

    private String buildChangeDescription(Object oldObj, Object newObj, String[] excludeFields) {
        Map<String, Object> oldMap = BeanUtil.beanToMap(oldObj, false, true);
        Map<String, Object> newMap = BeanUtil.beanToMap(newObj, false, true);

        Set<String> excludeSet = new HashSet<>(Arrays.asList(excludeFields));
        excludeSet.addAll(Arrays.asList("updateTime", "updatedTime", "lastModifiedTime",
                "modifyTime", "lastUpdateTime", "createTime", "createdTime", "id"));

        return oldMap.entrySet().stream()
                .filter(entry -> {
                    String fieldName = entry.getKey();
                    if (excludeSet.contains(fieldName)) {
                        return false;
                    }
                    Object newVal = newMap.get(fieldName);
                    return !Objects.equals(safeStr(entry.getValue()), safeStr(newVal));
                })
                .map(entry -> {
                    String field = entry.getKey();
                    Object oldVal = entry.getValue();
                    Object newVal = newMap.get(field);
                    String fieldLabel = getFieldDescription(oldObj.getClass(), field);
                    return fieldLabel + ": " + safeStr(oldVal) + " → " + safeStr(newVal);
                })
                .collect(Collectors.joining("; "));
    }

    private String getFieldDescription(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                Schema schema = field.getAnnotation(Schema.class);
                if (schema != null && StrUtil.isNotBlank(schema.description())) {
                    return schema.description();
                }
                return fieldName;
            } catch (NoSuchFieldException ignored) {
                clazz = clazz.getSuperclass();
            }
        }
        return fieldName;
    }

    private String safeStr(Object obj) {
        if (obj == null) {
            return "空";
        }
        if (obj instanceof String[]) {
            return Arrays.toString((String[]) obj);
        }
        if (obj.getClass().isArray() && obj.getClass().getComponentType().isEnum()) {
            Object[] enumArray = (Object[]) obj;
            return Arrays.stream(enumArray)
                    .map(this::safeStr)
                    .collect(Collectors.joining(", ", "[", "]"));
        }
        if (obj instanceof Enum<?>) {
            Enum<?> enumObj = (Enum<?>) obj;
            try {
                Method getDescriptionMethod = enumObj.getClass().getMethod("getDescription");
                Object description = getDescriptionMethod.invoke(enumObj);
                if (description != null) {
                    return description.toString();
                }
            } catch (Exception ignored) {
            }

            try {
                Method getNameMethod = enumObj.getClass().getMethod("getName");
                Object name = getNameMethod.invoke(enumObj);
                if (name != null) {
                    return name.toString();
                }
            } catch (Exception ignored) {
            }

            return enumObj.name();
        }
        return obj.toString();
    }

    private String getClientIP() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return IpUtils.getClientIp(request);
            }
        } catch (Exception e) {
            log.warn("获取客户端 IP 失败: {}", e.getMessage());
        }
        return "unknown";
    }
} 