package com.telecom.apigateway.common;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.service.OperationLogService;
import com.telecom.apigateway.utils.IpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.ApplicationContext;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class InsertLogAspect {

    private final OperationLogService logService;
    private final ApplicationContext applicationContext;
    private final ExpressionParser parser = new SpelExpressionParser();
    private final LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();

    @Around("@annotation(com.telecom.apigateway.common.InsertLogAnnotation)")
    public Object logInsert(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        Throwable targetException = null;

        try {
            String username = StpUtil.getLoginIdAsString();
            String ip = getClientIP();

            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            InsertLogAnnotation annotation = method.getAnnotation(InsertLogAnnotation.class);
            Object[] args = joinPoint.getArgs();

            StandardEvaluationContext context = createEvaluationContext(method, args);
            context.setVariable("currentUser", username);

            try {
                result = joinPoint.proceed();
                context.setVariable("result", result);
            } catch (Throwable e) {
                targetException = e;
                context.setVariable("exception", e);
            }

            ResourceTypeEnum resourceType = annotation.resourceType();
            try {
                String displayName = parseDisplayName(annotation.displayName(), context);
                String description = "创建了 " + resourceType.getName() + ": " + displayName;

                logService.createLog(username, OperationTypeEnum.INSERT, resourceType, description, ip);
            } catch (Exception logException) {
                log.error("记录新增日志失败: {}", logException.getMessage(), logException);
            }

        } catch (Exception e) {
            log.error("新增日志切面处理失败: {}", e.getMessage(), e);
        }

        if (targetException != null) {
            throw targetException;
        }

        return result;
    }

    private StandardEvaluationContext createEvaluationContext(Method method, Object[] args) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setBeanResolver(new BeanFactoryResolver(applicationContext));

        String[] paramNames = discoverer.getParameterNames(method);
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }
        return context;
    }

    private String parseDisplayName(String displayNameSpel, StandardEvaluationContext context) {
        if (StrUtil.isBlank(displayNameSpel)) {
            return "";
        }

        try {
            Expression exp = parser.parseExpression(displayNameSpel);
            Object value = exp.getValue(context);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            log.error("解析显示名称失败: {} -> {}", displayNameSpel, e.getMessage());
            return "";
        }
    }

    private String getClientIP() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return IpUtils.getClientIp(request);
            }
        } catch (Exception e) {
            log.warn("获取客户端 IP 失败: {}", e.getMessage());
        }
        return "unknown";
    }
} 