package com.telecom.apigateway.common;

import com.telecom.apigateway.model.enums.ResourceTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 新增操作日志注解
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface InsertLogAnnotation {

    /**
     * 资源类型
     */
    ResourceTypeEnum resourceType();

    /**
     * 资源显示名称，支持SpEL表达式
     * 例如：#{#user.username}、#{#result.name}
     */
    String displayName();
} 