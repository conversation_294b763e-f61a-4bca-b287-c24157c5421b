package com.telecom.apigateway.common;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SpEL表达式启动验证器
 * 在应用启动时验证注解中SpEL表达式的bean引用是否正确
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "logging.spel.validation.enabled", havingValue = "true", matchIfMissing = true)
public class SpelValidationStartupChecker implements ApplicationListener<ContextRefreshedEvent> {

    private final ApplicationContext applicationContext;
    
    // 匹配 @beanName 格式的正则表达式
    private static final Pattern BEAN_REFERENCE_PATTERN = Pattern.compile("@([a-zA-Z][a-zA-Z0-9_]*)");
    
    // 匹配 @beanName.methodName(...) 格式的正则表达式
    private static final Pattern BEAN_METHOD_PATTERN = Pattern.compile("@([a-zA-Z][a-zA-Z0-9_]*)\\.([a-zA-Z][a-zA-Z0-9_]*)\\s*\\(([^)]*)\\)");

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext() == applicationContext) {
            log.info("开始验证SpEL表达式中的bean引用...");
            List<String> errors = new ArrayList<>();
            
            validateAnnotations(errors);
            
            if (!errors.isEmpty()) {
                log.error("发现SpEL表达式错误:");
                errors.forEach(log::error);
                throw new IllegalStateException("SpEL表达式验证失败，请检查bean名称是否正确");
            }
            
            log.info("SpEL表达式验证完成，所有bean引用正确");
        }
    }

    private void validateAnnotations(List<String> errors) {
        // 获取所有bean
        Map<String, Object> allBeans = applicationContext.getBeansOfType(Object.class);
        
        for (Object bean : allBeans.values()) {
            Class<?> clazz = bean.getClass();
            // 处理CGLIB代理类
            if (clazz.getName().contains("$$")) {
                clazz = clazz.getSuperclass();
            }
            
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                validateMethodAnnotations(method, errors);
            }
        }
    }

    private void validateMethodAnnotations(Method method, List<String> errors) {
        String methodInfo = method.getDeclaringClass().getSimpleName() + "." + method.getName();
        
        // 验证 UpdateLogAnnotation
        UpdateLogAnnotation updateAnnotation = method.getAnnotation(UpdateLogAnnotation.class);
        if (updateAnnotation != null) {
            validateSpelExpression(updateAnnotation.queryMethod(), methodInfo, "queryMethod", errors);
            validateSpelExpression(updateAnnotation.afterQueryMethod(), methodInfo, "afterQueryMethod", errors);
            validateSpelExpression(updateAnnotation.displayName(), methodInfo, "displayName", errors);
        }
        
        // 验证 DeleteLogAnnotation
        DeleteLogAnnotation deleteAnnotation = method.getAnnotation(DeleteLogAnnotation.class);
        if (deleteAnnotation != null) {
            validateSpelExpression(deleteAnnotation.displayName(), methodInfo, "displayName", errors);
        }
        
        // 验证 InsertLogAnnotation
        InsertLogAnnotation insertAnnotation = method.getAnnotation(InsertLogAnnotation.class);
        if (insertAnnotation != null) {
            validateSpelExpression(insertAnnotation.displayName(), methodInfo, "displayName", errors);
        }
        
        // 验证 OperationLogAnnotation
        OperationLogAnnotation operationAnnotation = method.getAnnotation(OperationLogAnnotation.class);
        if (operationAnnotation != null) {
            validateSpelExpression(operationAnnotation.description(), methodInfo, "description", errors);
            validateSpelExpression(operationAnnotation.resourceNameSpel(), methodInfo, "resourceNameSpel", errors);
            
            for (int i = 0; i < operationAnnotation.spelArgs().length; i++) {
                validateSpelExpression(operationAnnotation.spelArgs()[i], methodInfo, "spelArgs[" + i + "]", errors);
            }
        }
    }

    private void validateSpelExpression(String expression, String methodInfo, String fieldName, List<String> errors) {
        if (StrUtil.isBlank(expression)) {
            return;
        }
        
        // 检查bean名称是否存在
        Matcher beanMatcher = BEAN_REFERENCE_PATTERN.matcher(expression);
        while (beanMatcher.find()) {
            String beanName = beanMatcher.group(1);
            if (!applicationContext.containsBean(beanName)) {
                errors.add(String.format("方法 %s 的 %s 中引用的bean '%s' 不存在: %s", 
                    methodInfo, fieldName, beanName, expression));
            }
        }
        
        // 检查bean方法是否存在
        Matcher methodMatcher = BEAN_METHOD_PATTERN.matcher(expression);
        while (methodMatcher.find()) {
            String beanName = methodMatcher.group(1);
            String methodName = methodMatcher.group(2);
            String parameters = methodMatcher.group(3);
            
            if (applicationContext.containsBean(beanName)) {
                Object bean = applicationContext.getBean(beanName);
                Class<?> beanClass = getBeanClass(bean);
                
                int paramCount = countParameters(parameters);
                MethodValidationResult result = validateMethod(beanClass, methodName, paramCount);
                
                if (!result.exists) {
                    errors.add(String.format("方法 %s 的 %s 中引用的bean '%s' 没有方法 '%s': %s", 
                        methodInfo, fieldName, beanName, methodName, expression));
                } else if (!result.parameterCountMatches) {
                    errors.add(String.format("方法 %s 的 %s 中引用的bean '%s' 的方法 '%s' 参数数量不匹配，期望 %d 个参数: %s", 
                        methodInfo, fieldName, beanName, methodName, paramCount, expression));
                }
            }
        }
    }
    
    /**
     * 获取bean的实际类（处理代理类）
     */
    private Class<?> getBeanClass(Object bean) {
        Class<?> beanClass = bean.getClass();
        
        // 处理CGLIB代理类
        if (beanClass.getName().contains("$$")) {
            beanClass = beanClass.getSuperclass();
        }
        
        // 处理JDK动态代理
        if (beanClass.getName().contains("$Proxy")) {
            Class<?>[] interfaces = beanClass.getInterfaces();
            if (interfaces.length > 0) {
                // 选择第一个非系统接口
                for (Class<?> intf : interfaces) {
                    if (!intf.getName().startsWith("java.") && 
                        !intf.getName().startsWith("org.springframework.") &&
                        !intf.getName().startsWith("org.apache.")) {
                        beanClass = intf;
                        break;
                    }
                }
            }
        }
        
        return beanClass;
    }
    
    /**
     * 计算参数数量（简单计算逗号分隔的参数）
     */
    private int countParameters(String parameters) {
        if (StrUtil.isBlank(parameters) || parameters.trim().isEmpty()) {
            return 0;
        }
        
        // 简单按逗号分割计算参数数量
        String[] params = parameters.split(",");
        int count = 0;
        for (String param : params) {
            if (StrUtil.isNotBlank(param.trim())) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 验证方法存在性和参数数量
     */
    private MethodValidationResult validateMethod(Class<?> clazz, String methodName, int expectedParamCount) {
        try {
            boolean exists = false;
            boolean parameterCountMatches = false;
            
            // 获取所有公共方法（包括继承的）
            Method[] methods = clazz.getMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    exists = true;
                    if (method.getParameterCount() == expectedParamCount) {
                        parameterCountMatches = true;
                        break;
                    }
                }
            }
            
            // 如果公共方法中没找到，检查声明的方法（包括私有方法）
            if (!exists) {
                Method[] declaredMethods = clazz.getDeclaredMethods();
                for (Method method : declaredMethods) {
                    if (method.getName().equals(methodName)) {
                        exists = true;
                        if (method.getParameterCount() == expectedParamCount) {
                            parameterCountMatches = true;
                            break;
                        }
                    }
                }
            }
            
            return new MethodValidationResult(exists, parameterCountMatches);
        } catch (Exception e) {
            log.warn("检查方法存在性时发生异常: {}.{} - {}", clazz.getName(), methodName, e.getMessage());
            return new MethodValidationResult(true, true); // 发生异常时假设方法存在且参数匹配，避免误报
        }
    }
    
    /**
     * 方法验证结果
     */
    private static class MethodValidationResult {
        final boolean exists;
        final boolean parameterCountMatches;
        
        MethodValidationResult(boolean exists, boolean parameterCountMatches) {
            this.exists = exists;
            this.parameterCountMatches = parameterCountMatches;
        }
    }
} 