package com.telecom.apigateway.common;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.service.OperationLogService;
import com.telecom.apigateway.utils.IpUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.context.ApplicationContext;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class OperationLogAspect {

    private final OperationLogService logService;
    private final ApplicationContext applicationContext;
    private final ExpressionParser parser = new SpelExpressionParser();
    private final TemplateParserContext templateParserContext = new TemplateParserContext();
    private final LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();

    @Around("@annotation(com.telecom.apigateway.common.OperationLogAnnotation)")
    public Object logOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        if (log.isDebugEnabled()) {
            log.debug("开始记录操作日志，方法: {}", joinPoint.getSignature().toShortString());
        }

        Object result = null;
        Throwable targetException = null;
        
        try {
            // 获取用户和 IP
            String username = StpUtil.getLoginIdAsString();
            String ip = getClientIP();

            // 1. 获取方法参数（更新前的快照）
            // 获取方法、注解
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            OperationLogAnnotation annotation = method.getAnnotation(OperationLogAnnotation.class);
            Object[] args = joinPoint.getArgs();
            StandardEvaluationContext context = createEvaluationContext(method, args);

            // 2. 执行目标方法，拿到返回值
            try {
                result = joinPoint.proceed();
                context.setVariable("result", result);
                context.setVariable("currentUser", username);
            } catch (Throwable e) {
                // 目标方法抛出异常，先保存异常，稍后重新抛出
                targetException = e;
                context.setVariable("exception", e);
                context.setVariable("currentUser", username);
            }

            // 3. 记录操作日志（即使目标方法抛出异常也要记录）
            try {
                // SpEL 解析
                String resourceName = "";
                if (StringUtils.isBlank(annotation.resourceNameSpel())) {
                    resourceName = annotation.resourceType().getName();
                } else {
                    resourceName = parseExpression(annotation.resourceNameSpel(), context);
                }
                String description = parseDescription(annotation, context, resourceName, joinPoint.getArgs(), result);
                
                // 如果目标方法抛出异常，在描述中标注
                if (targetException != null) {
                    description += " (执行失败: " + targetException.getMessage() + ")";
                }

                // 保存日志
                logService.createLog(username, annotation.operationType(), annotation.resourceType(), description, ip);

                if (log.isDebugEnabled()) {
                    log.debug("操作日志记录完成: {}", description);
                }
            } catch (Exception logException) {
                // 只捕获日志记录过程中的异常，不影响目标方法的异常传播
                log.error("记录操作日志失败: {}", logException.getMessage(), logException);
            }

        } catch (Exception e) {
            // 捕获日志记录准备阶段的异常
            log.error("操作日志切面处理失败: {}", e.getMessage(), e);
        }
        
        // 如果目标方法抛出了异常，重新抛出
        if (targetException != null) {
            throw targetException;
        }
        
        return result;
    }

    private StandardEvaluationContext createEvaluationContext(Method method, Object[] args) {
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 允许 SpEL 通过 @beanName 调用 Spring Bean 方法
        context.setBeanResolver(new BeanFactoryResolver(applicationContext));

        String[] paramNames = discoverer.getParameterNames(method);
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                // 避免前后修改一个对象，无法对比更新
                Object arg = args[i];
                if (arg instanceof MultipartFile || 
                    (arg != null && arg.getClass().isArray() && arg.getClass().getComponentType() == MultipartFile.class)) {
                    // 跳过文件类型的参数克隆（包括 MultipartFile 和 MultipartFile[] 数组）
                    context.setVariable(paramNames[i], arg);
                } else {
                    context.setVariable(paramNames[i], ObjectUtil.cloneByStream(arg));
                }
            }
        }
        return context;
    }

    private String parseExpression(String spel, StandardEvaluationContext context) {
        if (StrUtil.isBlank(spel)) return "";

        try {
            Expression exp = parser.parseExpression(spel);
            Object value = exp.getValue(context);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            log.warn("SpEL 表达式解析失败: {} -> {}", spel, e.getMessage());
            return "";
        }
    }

    private String parseDescription(
            OperationLogAnnotation annotation,
            StandardEvaluationContext context,
            String resourceName, Object[] args,
            Object result
    ) {
        OperationTypeEnum operationType = annotation.operationType();
        String descTemplate = annotation.description();

        if (OperationTypeEnum.LOGIN.equals(operationType)) {
            return operationType.getDescription();
        }

        try {
            if (StrUtil.isNotBlank(descTemplate)) {
                descTemplate = descTemplate.replace("{resourceName}", resourceName);
                descTemplate = descTemplate.replace("{currentUser}", parseExpression("#currentUser", context));
                if (descTemplate.contains("#")) {
                    Expression exp = parser.parseExpression(descTemplate, templateParserContext);
                    return String.valueOf(exp.getValue(context));
                }

                if (descTemplate.contains("{") && descTemplate.contains("}")) {
                    return MessageFormat.format(descTemplate, args);
                }

                return descTemplate;
            }

            // 使用枚举默认描述
            String operationDesc = operationType.getDescription();
            operationDesc = operationDesc.replace("{resourceName}", resourceName);
            operationDesc = operationDesc.replace("{currentUser}", parseExpression("#currentUser", context));
            Object[] spelArgs = annotation.spelArgs();

            // ✅ 全量更新差异对比
            if (operationType.equals(OperationTypeEnum.FULL_UPDATE)) {
                Object oldObj = parser.parseExpression((String) spelArgs[1], templateParserContext).getValue(context);
                String diff = buildDiffDescription(oldObj, result);
                String format = MessageFormat.format(operationDesc, spelArgs[0], diff);
                Expression exp = parser.parseExpression(format, templateParserContext);
                return String.valueOf(exp.getValue(context));
            }

            String format = MessageFormat.format(operationDesc, spelArgs);
            Expression exp = parser.parseExpression(format, templateParserContext);
            return String.valueOf(exp.getValue(context));
        } catch (Exception e) {
            log.warn("构建描述失败，使用默认描述: {}", e.getMessage());
            return operationType.getDescription();
        }
    }

    private String getClientIP() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return IpUtils.getClientIp(request);
            }
        } catch (Exception e) {
            log.warn("获取客户端 IP 失败: {}", e.getMessage());
        }
        return "unknown";
    }

    private String buildDiffDescription(Object oldObj, Object newObj) {
        Map<String, Object> oldMap = BeanUtil.beanToMap(oldObj, false, true);
        Map<String, Object> newMap = BeanUtil.beanToMap(newObj, false, true);

        return oldMap.entrySet().stream()
                .filter(entry -> {
                    String fieldName = entry.getKey();
                    if (isExcludeField(fieldName)) {
                        return false;
                    }
                    Object newVal = newMap.get(fieldName);
                    return !Objects.equals(safeStr(entry.getValue()), safeStr(newVal));
                })
                .map(entry -> {
                    String field = entry.getKey();
                    Object oldVal = entry.getValue();
                    Object newVal = newMap.get(field);
                    String fieldLabel = getFieldDescription(oldObj.getClass(), field);
                    return fieldLabel + ": " + safeStr(oldVal) + " → " + safeStr(newVal);
                })
                .collect(Collectors.joining("; "));
    }

    private boolean isExcludeField(String fieldName) {
        return "updateTime".equalsIgnoreCase(fieldName)
                || "updatedTime".equalsIgnoreCase(fieldName)
                || "lastModifiedTime".equalsIgnoreCase(fieldName)
                || "modifyTime".equalsIgnoreCase(fieldName)
                || "lastUpdateTime".equalsIgnoreCase(fieldName)
                || "createTime".equalsIgnoreCase(fieldName)
                || "createdTime".equalsIgnoreCase(fieldName);
    }

    private String getFieldDescription(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                Schema schema = field.getAnnotation(Schema.class);
                if (schema != null && StrUtil.isNotBlank(schema.description())) {
                    return schema.description();
                }
                return fieldName;
            } catch (NoSuchFieldException ignored) {
                clazz = clazz.getSuperclass();
            }
        }
        return fieldName;
    }


    private String safeStr(Object obj) {
        if (obj == null) {
            return "空";
        }
        if (obj instanceof String[]) {
            return Arrays.toString((String[]) obj);
        }
        // 处理枚举数组
        if (obj.getClass().isArray() && obj.getClass().getComponentType().isEnum()) {
            Object[] enumArray = (Object[]) obj;
            return Arrays.stream(enumArray)
                    .map(this::safeStr)
                    .collect(Collectors.joining(", ", "[", "]"));
        }
        // 处理枚举类型
        if (obj instanceof Enum<?>) {
            Enum<?> enumObj = (Enum<?>) obj;
            // 尝试获取枚举的描述方法
            try {
                Method getDescriptionMethod = enumObj.getClass().getMethod("getDescription");
                Object description = getDescriptionMethod.invoke(enumObj);
                if (description != null) {
                    return description.toString();
                }
            } catch (Exception ignored) {
                // 如果没有 getDescription 方法，继续尝试其他方法
            }

            // 尝试获取枚举的 name 或 value 方法
            try {
                Method getNameMethod = enumObj.getClass().getMethod("getName");
                Object name = getNameMethod.invoke(enumObj);
                if (name != null) {
                    return name.toString();
                }
            } catch (Exception ignored) {
                // 如果没有 getName 方法，使用默认的 name()
            }

            // 使用枚举的名称
            return enumObj.name();
        }
        return obj.toString();
    }
}
