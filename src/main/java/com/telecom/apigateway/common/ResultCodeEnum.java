package com.telecom.apigateway.common;

import com.telecom.apigateway.common.excption.IErrorCode;
import lombok.Getter;

@Getter
public enum ResultCodeEnum implements IErrorCode {
    FAIL("AGW9999", "操作失败"),
    TEST_ERROR("0001", "测试错误响应"),

    DB_ERROR("SYS0001", "数据库操作异常"),
    REQUEST_PARSE_ERROR("SYS0002", "请求数据解析错误, 请检查格式"),

    TAG_HAS_RELATION_API("TAG0001", "标签存在关联的API"),
    TAG_NOT_EXISTED("TAG0002", "标签不存在"),

    LOG_QUERY_PARAM_ERROR("LOG0001", "日志查询参数校验失败: {}"),

    IMPORT_EXCEL_FAILED("API0001", "导入excel失败"),
    IMPORT_JSON_FAILED("API0002", "导入json失败"),
    IMPORT_YAML_FAILED("API0003", "导入yaml失败"),
    NOT_SUPPORT_API_FILE_TYPE("API0004", "不支持的api文件类型"),
    DUPLICATE_API("API0005", "重复的api"),
    API_PARAM_ERROR("API0006", "请求参数错误: {}"),
    API_NOT_EXISTED("API0007", "api 不存在"),
    URL_FORMAT_ERROR("API0008", "url 格式不正确"),
    SENSITIVE_API_NOT_EXISTED("API0009", "涉敏 api 不存在"),
    FILE_READ_ERROR("API0010", "文件读取失败"),
    FILE_TOO_LARGE("API0011", "文件大小超出限制"),
    INVALID_PARAMETER("API0012", "文件不能为空"),
    DELETE_API_ERROR("API0013", "删除 api 失败"),

    FEATURE_MUST_HAS_PARENT_APPLICATION("APP0000", "新增功能必须指定其所属应用"),
    FEATURE_CAN_NOT_HAS_CHILDREN_APPLICATION("APP0001", "功能下无法新增应用/功能"),
    EXISTED_APPLICATION("APP0002", "该uri已设定"),

    EXISTED_RULE_NAME("RUL0001", "存在相同规则名"),
    UNSATISFIED_RULE_PARAM("RUL0002", "规则参数缺失"),
    RULE_REGEX_ERROR("RUL0003", "正则表达式有误"),

    APPLICATION_NOT_EXISTED("APP0001", "应用不存在"),

    THREAT_NOT_EXISTED("TH0001", "威胁信息不存在"),

    RULE_NOT_EXISTED("RL0001", "规则不存在"),

    GROUP_NOT_EXISTED("GP0001", "ip组不存在"),
    SYSTEM_NOT_OPERATE("GP0002", "系统生成ip组不可编辑或删除"),
    IP_EXISTED("GP0003", "ip已存在"),
    ONLY_JOIN_IP_GROUP("GP0004", "ip组选择错误"),
    IP_GROUP_EXISTED("GP0004", "ip组已存在"),

    ADD_FIREWALL_FAILED("FWL0001", "添加防火墙失败"),
    UPDATE_FIREWALL_FAILED("FWL0002", "更新防火墙失败"),
    DELETE_FIREWALL_FAILED("FWL0003", "删除防火墙失败"),
    CRS_DETECT_FAILED("FWL0004", "crs检测失败"),

    USER_LOCKED("USR0001", "用户已被锁定"),
    USER_PASSWORD_ERROR("USR0002", "密码错误"),

    ABNORMAL_BEHAVIOR_RULE_CHECK_ERROR("ABR0001", "规则校验失败: {}"),
    NOTLICENSE("AGW0000","系统未授权或授权过期"),
    ;

    private final String code;
    private final String msg;

    ResultCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getDetailCode() {
        return code;
    }

    @Override
    public String getErrorMessage() {
        return msg;
    }
}
