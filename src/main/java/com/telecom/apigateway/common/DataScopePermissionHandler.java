package com.telecom.apigateway.common;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.HexValue;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Aspect
@Slf4j
@Component
@RequiredArgsConstructor
public class DataScopePermissionHandler implements DataPermissionHandler {
    ThreadLocal<DataScopeParams> threadLocal = new ThreadLocal<>();

    @Pointcut("@annotation(com.telecom.apigateway.common.DataScope)")
    public void dataScopePointCut() {
    }

    @Before("dataScopePointCut()")
    public void doBefore(JoinPoint point) {
        DataScope controllerDataScope = getAnnotation(point);
        if (Objects.nonNull(controllerDataScope)) {
            String username = StpUtil.getLoginIdAsString();
            Set<String> applicationPermissions = new HashSet<>();
            List<String> roleList = StpUtil.getRoleList();
            boolean isAdmin = username.equals("admin") || roleList.contains("admin");
            applicationPermissions = Sets.newHashSet(StpUtil.getPermissionList());
            DataScopeParams dataScopeParams = new DataScopeParams(
                    controllerDataScope.tableAlias(),
                    controllerDataScope.columnAlias(),
                    isAdmin,
                    applicationPermissions
            );
            threadLocal.set(dataScopeParams);
            log.debug(dataScopeParams.toString());
        }
    }

    @After("dataScopePointCut()")
    public void clearThreadLocal() {
        threadLocal.remove();
        log.debug("线程已清除");
    }

    @SneakyThrows
    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        DataScopeParams dataScopeParams = threadLocal.get();
        if (Objects.isNull(dataScopeParams)) {
            return where;
        }

        if (Objects.isNull(where)) {
            where = new HexValue(" 1 = 1");
        }

        Set<String> applications = dataScopeParams.getSecretary();
        if (!applications.isEmpty()) {
            StringBuilder inClause = new StringBuilder();
            String tablePrefix = dataScopeParams.getTableAlias();
            // 如果有表别名，添加表别名前缀
            String columnName = !tablePrefix.isEmpty() ?
                    tablePrefix + "." + dataScopeParams.getColumnAlias() :
                    dataScopeParams.getColumnAlias();

            inClause.append(" AND ").append(columnName).append(" IN (");

            int i = 0;
            for (String appId : applications) {
                if (i > 0) {
                    inClause.append(",");
                }
                inClause.append("'").append(appId).append("'");
                i++;
            }
            inClause.append(")");

            return new HexValue("(" + where + ")" + inClause);
        }

        // 没有任何权限则返回永假条件
        return new HexValue("(" + where + ") AND 1 = 0");
    }

    private DataScope getAnnotation(JoinPoint point) {
        Signature signature = point.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        if (Objects.nonNull(method)) {
            return method.getAnnotation(DataScope.class);
        }
        return null;
    }

    @Data
    @AllArgsConstructor
    static class DataScopeParams {
        private String tableAlias;
        private String columnAlias;
        private Boolean isAdmin;
        private Set<String> secretary;
    }
}
