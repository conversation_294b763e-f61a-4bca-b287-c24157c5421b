package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.AbnormalBehaviorRuleMapper;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRule;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import com.telecom.apigateway.model.vo.response.QuerySystemAbrResponse;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Service
public class AbnormalBehaviorRuleService extends ServiceImpl<AbnormalBehaviorRuleMapper, AbnormalBehaviorRule> {

    @Override
    public List<AbnormalBehaviorRule> list() {
        return lambdaQuery().eq(AbnormalBehaviorRule::getIsDeleted, false).list();
    }

    public List<QuerySystemAbrResponse> getSystemRules() {
        return lambdaQuery()
                .eq(AbnormalBehaviorRule::getSource, AbnormalBehaviorRuleEnum.Source.SYSTEM)
                .eq(AbnormalBehaviorRule::getIsDeleted, false)
                .orderByAsc(AbnormalBehaviorRule::getName)
                .list().stream().map(r ->
                        QuerySystemAbrResponse.builder()
                                .id(r.getId())
                                .name(r.getName())
                                .abnormalType(r.getAbnormalType())
                                .abnormalDuration(r.getAbnormalDuration())
                                .abnormalThreshold(r.getAbnormalThreshold())
                                .policy(r.getPolicy())
                                .policyDuration(r.getPolicyDuration())
                                .enable(r.getEnable())
                                .condition(r.getCondition())
                                .build()
                ).collect(Collectors.toList());
    }


    public void deleteByApiId(String apiId) {
        lambdaUpdate()
                .set(AbnormalBehaviorRule::getIsDeleted, true)
                .set(AbnormalBehaviorRule::getUpdateTime, LocalDateTime.now())
                .set(AbnormalBehaviorRule::getUpdateUser, StpUtil.getLoginIdAsString())
                .eq(AbnormalBehaviorRule::getSource, AbnormalBehaviorRuleEnum.Source.CUSTOM)
                .apply("'" + apiId + "'  = any(asset_id)")
                .update();
    }
}
