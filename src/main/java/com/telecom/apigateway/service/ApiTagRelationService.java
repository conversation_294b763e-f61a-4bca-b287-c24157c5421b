package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.ApiTagRelation;

import java.util.Collection;
import java.util.List;

public interface ApiTagRelationService extends IService<ApiTagRelation> {
    List<ApiTagRelation> listByTagId(String tagId);

    void deleteByTagId(String tagId);

    List<ApiTagRelation> listByTagIds(Collection<String> tagIds);

}
