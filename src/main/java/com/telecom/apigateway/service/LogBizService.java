package com.telecom.apigateway.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsRiskRuleDTO;
import com.telecom.apigateway.model.dto.EsSensitiveRuleDTO;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleTrigger;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.Blocklist;
import com.telecom.apigateway.model.enums.LogEnum;
import com.telecom.apigateway.model.vo.request.QueryLogRequest;
import com.telecom.apigateway.model.vo.response.ApplicationResponse;
import com.telecom.apigateway.model.vo.response.LogQueryResponse;
import com.telecom.apigateway.utils.MapUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-01
 */
@Service
public class LogBizService {

    @Resource
    private ApplicationBusinessService applicationBusinessService;
    @Resource
    private ApplicationService applicationService;
    @Resource
    private NginxLogEsClient nginxLogEsClient;
    @Resource
    private BlocklistService blocklistService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private AbnormalBehaviorRuleTriggerService abrtService;

    public IPage<LogQueryResponse> queryPageLog(QueryLogRequest request) {
        request.format();
        if (CollUtil.isNotEmpty(request.getAppId())) {
            List<String> limitAppIds = applicationService.getIdsWithChildren(request.getAppId());
            request.setAppId(limitAppIds);
        }
        // 业务应用名
        Map<String, ApplicationResponse> bizNameAppMap = getBizAppsMap();
        // 黑白名单集合
        List<Blocklist> blocklistList = blocklistService.list();
        Map<String, String> blocklistMap = blocklistList.stream()
                .collect(Collectors.toMap(Blocklist::getBlockId, Blocklist::getName, (a, b) -> a));
        // 异常行为规则
        Map<String, AbnormalBehaviorRuleTrigger> abrMap = abrtService.list().stream()
                .collect(Collectors.toMap(AbnormalBehaviorRuleTrigger::getId, a -> a,
                        (a, b) -> a));
        if (StrUtil.isNotBlank(request.getAbnormalBehaviorRuleTriggerId())) {
            AbnormalBehaviorRuleTrigger abrt = abrtService.getById(request.getAbnormalBehaviorRuleTriggerId());
            if (abrt != null && ArrayUtil.isNotEmpty(abrt.getLogIds())) {
                request.setLogIds(Arrays.asList(abrt.getLogIds()));
            }
        }

        Page<EsNginxDTO> esNginxDTOPage = nginxLogEsClient.queryPage(request.toEsQueryDTO());
        List<EsNginxDTO> records = esNginxDTOPage.getRecords();
        List<LogQueryResponse> logQueryResponseList = BeanUtil.copyToList(records, LogQueryResponse.class);

        // 获得api信息
        List<String> apiIds =
                logQueryResponseList.stream().map(LogQueryResponse::getApiId).distinct().collect(Collectors.toList());
        List<ApiInfo> apis = apiInfoService.getByIds(apiIds);
        Map<String, ApiInfo> apiMap = MapUtil.toMapByUnionParams(ApiInfo::getId, apis);

        for (LogQueryResponse ele : logQueryResponseList) {
            ele.setIsEncryptApi(Optional.ofNullable(apiMap.get(ele.getApiId())).map(ApiInfo::getEncrypted).orElse(false));
            // riskRules[].type 集合
            if (CollUtil.isNotEmpty(ele.getSensitiveRules())) {
                ele.setSensitiveRuleIds(ele.getSensitiveRules().stream().map(EsSensitiveRuleDTO::getRuleId).collect(Collectors.toSet()));
            }
            if (ele.getRiskRules() != null || ele.getRejectRiskRules() != null) {
                Set<String> riskTypes1 = ele.getRiskRules() == null ? new HashSet<>() :
                        ele.getRiskRules().stream().map(EsRiskRuleDTO::getType).collect(Collectors.toSet());
                Set<String> riskTypes2 = ele.getRejectRiskRules() == null ? new HashSet<>() :
                        ele.getRejectRiskRules().stream().map(EsRiskRuleDTO::getType).collect(Collectors.toSet());
                riskTypes1.addAll(riskTypes2);
                ele.setRiskTypes(riskTypes1);
            }
            ele.setAppName(getBizAppsName(ele.getAppId(), bizNameAppMap));
            // 设置请求状态 => 写到 get 方法中了
            ele.setRequestState(ele.getReqState());
            // 触发 waf 黑名单的, 返回黑名单名称
            if (LogEnum.WafDetectStatus.REJECT.name().equalsIgnoreCase(ele.getWafDetectStatus())) {
                ele.setBlocklistName(blocklistMap.getOrDefault(ele.getWafDetectId(), ""));
            }
            // 触发 waf 白名单的, 返回白名单名称
            if (LogEnum.WafDetectStatus.ALLOW.name().equalsIgnoreCase(ele.getWafDetectStatus())) {
                ele.setBlocklistName(blocklistMap.getOrDefault(ele.getWafDetectId(), ""));
            }
            // 异常行为规则
            // if (ele.getRequestState().contains(LogEnum.RequestState.ABRT_LOG) ||
            //         ele.getRequestState().contains(LogEnum.RequestState.ABRT_REJECT)) {
            //     Optional.ofNullable(abrMap.get(ele.getAbnormalBehaviorRuleTriggerId())).ifPresent(a -> {
            //         ele.setAbnormalType(a.getAbnormalType());
            //         ele.setAbnormalBehaviorRuleName(a.getRuleName());
            //     });
            // }
            if (ele.getRequestState().contains(LogEnum.RequestState.ABRT_LOG) ||
                    ele.getRequestState().contains(LogEnum.RequestState.ABRT_REJECT)) {
                Optional.ofNullable(abrMap.get(ele.getAbnormalBehaviorRuleTriggerId())).ifPresent(a -> {
                    ele.setAbnormalType(a.getAbnormalType());
                    ele.setAbnormalBehaviorRuleName(a.getRuleName());
                });
            }
        }

        return new Page<LogQueryResponse>().setRecords(logQueryResponseList)
                .setTotal(esNginxDTOPage.getTotal())
                .setCurrent(esNginxDTOPage.getCurrent())
                .setSize(esNginxDTOPage.getSize())
                .setPages(esNginxDTOPage.getPages());
    }

    private String getBizAppsName(String appId, Map<String, ApplicationResponse> appMap) {
        ApplicationResponse app = appMap.getOrDefault(appId, null);
        return app == null ? "未知" : app.getName();
    }

    private Map<String, ApplicationResponse> getBizAppsMap() {
        List<ApplicationResponse> apps = getBizApps();
        return apps.stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId, ele -> ele));
    }

    private List<ApplicationResponse> getBizApps() {
        return applicationBusinessService.listWithBizName();
    }

    public LogQueryResponse detail(String logId) {
        QueryLogRequest logReq = QueryLogRequest.builder().logId(logId).build();
        logReq.setPageNum(1);
        logReq.setPageSize(1);
        IPage<LogQueryResponse> logQueryResponseIPage = queryPageLog(logReq);
        if (logQueryResponseIPage.getTotal() == 0) {
            return null;
        }
        return logQueryResponseIPage.getRecords().get(0);
    }
}
