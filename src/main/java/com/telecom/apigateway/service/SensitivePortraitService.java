package com.telecom.apigateway.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.dto.EsSensitiveRuleDTO;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.SensitiveApi;
import com.telecom.apigateway.model.entity.SensitiveRule;
import com.telecom.apigateway.model.vo.request.QueryApiRequest;
import com.telecom.apigateway.model.vo.request.QuerySensitivePortraitLeakRequest;
import com.telecom.apigateway.model.vo.request.QuerySensitivePortraitLogRequest;
import com.telecom.apigateway.model.vo.request.QuerySensitivePortraitLogStatRequest;
import com.telecom.apigateway.model.vo.response.ApiHttpInfoResponse;
import com.telecom.apigateway.model.vo.response.ApiQueryResponse;
import com.telecom.apigateway.model.vo.response.ApplicationResponse;
import com.telecom.apigateway.model.vo.response.SensitiveApiTriggerCountResponse;
import com.telecom.apigateway.model.vo.response.SensitiveLatestDataResponse;
import com.telecom.apigateway.model.vo.response.SensitivePortraitChartResponse;
import com.telecom.apigateway.model.vo.response.SensitivePortraitDetailResponse;
import com.telecom.apigateway.model.vo.response.SensitivePortraitLogResponse;
import com.telecom.apigateway.model.vo.response.SensitivePortraitResponse;
import com.telecom.apigateway.model.vo.response.SensitiveTopResponse;
import com.telecom.apigateway.model.vo.response.StatCount;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import com.telecom.apigateway.utils.PageUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.PipelineAggregatorBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedNested;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Cardinality;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Deque;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-22
 */
@Slf4j
@Service
public class SensitivePortraitService {

    @Resource
    private NginxAccessLogService nginxAccessLogService;
    @Resource
    private NginxLogEsClient nginxLogEsClient;
    @Resource
    private SensitiveRuleService sensitiveRuleService;
    @Resource
    private SensitiveApiService sensitiveApiService;
    @Resource
    private ApplicationService applicationService;
    @Resource
    private ApplicationBusinessService applicationBusinessService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;


    public List<SensitiveLatestDataResponse> latestData() {
        List<SensitiveRule> rules = sensitiveRuleService.list();
        List<String> ruleIds =
                rules.stream().map(SensitiveRule::getId).collect(Collectors.toList());
        Map<String, SensitiveRule> ruleMap = rules.stream()
                .collect(Collectors.toMap(SensitiveRule::getId, sensitiveRule -> sensitiveRule));
        if (ruleIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<ApiInfo> apis = apiInfoService.list();
        List<String> apiIds = apis.stream().map(ApiInfo::getId).distinct().collect(Collectors.toList());
        Map<String, String> apiMap = apis.stream().collect(Collectors.toMap(ApiInfo::getId, ApiInfo::getName));
        if (apiMap.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, String> appMap = applicationBusinessService.listWithBizName()
                .stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                        ApplicationResponse::getName));

        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(4)
                .build()
                .addExistQuery("sensitiveRules")
                .addMultipleQuery("sensitiveRules.ruleId", ruleIds)
                .addMultipleQuery("apiId", apiIds)
                .orderBy("logTime", SortOrder.DESC);
        List<EsNginxDTO> ngLogs = nginxLogEsClient.query(queryDTO);

        List<SensitiveLatestDataResponse> results = new ArrayList<>();
        ngLogs.forEach(ngLog -> {
            if (results.size() >= 4) {
                return;
            }
            List<EsSensitiveRuleDTO> sensitiveRules = ngLog.getSensitiveRules();
            sensitiveRules.forEach(sensitiveRule -> {
                if (results.size() >= 4) {
                    return;
                }
                SensitiveLatestDataResponse result = new SensitiveLatestDataResponse();
                result.setLogId(ngLog.getUuid());
                result.setKeyword(sensitiveRule.getContent());
                result.setApiId(ngLog.getApiId());
                result.setApiName(apiMap.getOrDefault(ngLog.getApiId(), ngLog.getUri()));
                result.setAppName(appMap.getOrDefault(ngLog.getAppId(), "未知"));
                result.setClientIp(ngLog.getClientIp());
                result.setLogTime(ngLog.getLogTime());
                result.setRequestState(ngLog.getReqState());
                SensitiveRule rule = ruleMap.get(sensitiveRule.getRuleId());
                if (rule != null) {
                    result.setSensitiveRuleName(rule.getName());
                    result.setSensitiveLevel(rule.getLevel());
                } else {
                    result.setSensitiveRuleName("未知");
                    result.setSensitiveLevel(3);
                }
                results.add(result);
            });
        });
        return results;
    }

    public SensitiveTopResponse top() {
        List<SensitiveRule> rules = sensitiveRuleService.list();
        List<String> ruleIds =
                rules.stream().map(SensitiveRule::getId).collect(Collectors.toList());
        if (ruleIds.isEmpty()) {
            return SensitiveTopResponse.empty();
        }
        List<ApiInfo> apis = apiInfoService.list();
        List<String> apiIds = apis.stream().map(ApiInfo::getId).distinct().collect(Collectors.toList());
        if (apiIds.isEmpty()) {
            return SensitiveTopResponse.empty();
        }

        List<SensitiveTopResponse.TopData> results = new ArrayList<>();
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startOfToday = endTime.toLocalDate().atStartOfDay();

        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .end(endTime)
                .queryFields(Arrays.asList("sensitiveRules", "apiId", "logTime"))
                .build()
                .addExistQuery("sensitiveRules")
                .addMultipleQuery("sensitiveRules.ruleId", ruleIds)
                .addMultipleQuery("apiId", apiIds);
        // 总量
        LocalDateTime startOfYear = startOfToday.toLocalDate().atStartOfDay().withDayOfYear(1);
        queryDTO.setStart(startOfYear);
        long total = getContentCount(queryDTO);
        // 总量: 数据前几
        queryDTO.setQueryCount(5);
        List<StatCount> statCounts = nginxAccessLogService.groupCount(queryDTO, "sensitiveRules.content");
        for (StatCount statCount : statCounts) {
            results.add(new SensitiveTopResponse.TopData(statCount.getLabel(), (long) statCount.getCount(), 0L));
        }
        // 新增
        EsQueryDTO queryDTOToday = EsQueryDTO.builder()
                .start(startOfToday)
                .end(endTime)
                .queryCount(5)
                .queryFields(Arrays.asList("sensitiveRules", "apiId", "logTime"))
                .build()
                .addExistQuery("sensitiveRules")
                .addMultipleQuery("sensitiveRules.ruleId", ruleIds)
                .addMultipleQuery("apiId", apiIds);

        EsQueryDTO queryDTOYesterday = EsQueryDTO.builder()
                .start(startOfToday.minusDays(1))
                .end(startOfToday)
                .queryCount(5)
                .queryFields(Arrays.asList("sensitiveRules", "apiId", "logTime"))
                .build()
                .addExistQuery("sensitiveRules")
                .addMultipleQuery("sensitiveRules.ruleId", ruleIds)
                .addMultipleQuery("apiId", apiIds);

        long newTotal = getContentCount(queryDTOToday) - getContentCount(queryDTOYesterday);

        List<StatCount> statCountsTopDay = nginxAccessLogService.groupCount(queryDTOToday, "sensitiveRules.content");
        List<StatCount> statCountsYesterday = nginxAccessLogService.groupCount(queryDTOYesterday,
                "sensitiveRules.content");
        for (SensitiveTopResponse.TopData result : results) {
            result.setIncrease(0L);
            for (StatCount statCountToday : statCountsTopDay) {
                if (result.getLabel().equals(statCountToday.getLabel())) {
                    result.setIncrease((long) statCountToday.getCount());
                }
            }
            for (StatCount statCountYesterday : statCountsYesterday) {
                if (result.getLabel().equals(statCountYesterday.getLabel())) {
                    result.setIncrease(result.getIncrease() - statCountYesterday.getCount());
                }
            }
        }

        return new SensitiveTopResponse(total, newTotal, results);
    }

    /**
     * 以具体数据+标签计算敏感数据，如果数据字段是一样的，但是标签不同，则算不一样的数据
     */
    private long getContentCount(EsQueryDTO queryDTO) {
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        NestedAggregationBuilder nestedAggregationBuilder =
                AggregationBuilders.nested("sensitive_rules_nested", "sensitiveRules")
                        .subAggregation(AggregationBuilders.terms("content_total_count")
                                .field("sensitiveRules.content")
                                .size(1));
        nativeSearchQuery.addAggregation(nestedAggregationBuilder);

        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);
        if (searchHits.hasAggregations()) {
            AggregationsContainer<?> container = searchHits.getAggregations();
            Aggregations aggregations = (Aggregations) container.aggregations();
            ParsedNested nested = aggregations.get("sensitive_rules_nested");
            return nested.getDocCount();
        }
        return 0L;
    }

    public List<SensitivePortraitResponse.Content> searchByContent(EsQueryDTO queryDTO, BaseData baseData) {
        List<StatCount> statCounts = nginxAccessLogService.groupCount(queryDTO, "sensitiveRules.content");
        List<SensitivePortraitResponse.Content> results = new ArrayList<>();
        for (StatCount statCount : statCounts) {
            EsQueryDTO newQueryDto = EsQueryDTO.builder().build();
            newQueryDto.addMultipleQuery("apiId", baseData.getAllApiIds());
            BeanUtil.copyProperties(queryDTO, newQueryDto);

            for (int i = newQueryDto.getParams().size() - 1; i >= 0; i--) {
                if (newQueryDto.getParams().get(i).getLabel().equals("sensitiveRules.content")) {
                    newQueryDto.getParams().remove(i);
                }
            }

            SensitivePortraitResponse.Content r = new SensitivePortraitResponse.Content();
            r.setKeyword(statCount.getLabel());
            // api
            List<String> apiIdsByContent = findApiIdsBySensitiveContent(queryDTO);
            apiIdsByContent = apiIdsByContent.stream()
                    .map(ele -> baseData.getAllApiMap().get(ele).getName())
                    .collect(Collectors.toList());
            r.setApis(apiIdsByContent);
            // logTime
            newQueryDto.addQuery("sensitiveRules.content", statCount.getLabel()).setQueryCount(1);
            EsNginxDTO ngLog = nginxLogEsClient.queryOne(newQueryDto);
            r.setLastLogTime(ngLog.getLogTime());
            // rules
            List<String> ruleIdsByContent = findRuleIdsBySensitiveContent(queryDTO);
            List<SensitivePortraitResponse.Content.RuleAndLevel> ruleAndLevels = ruleIdsByContent.stream()
                    .map(ele -> {
                        SensitiveRule rule = baseData.getAllRuleMap().get(ele);
                        return SensitivePortraitResponse.Content.RuleAndLevel.builder()
                                .sensitiveRuleId(rule.getId())
                                .sensitiveRuleName(rule.getName())
                                .sensitiveLevel(rule.getLevel())
                                .build();
                    }).collect(Collectors.toList());
            r.setSensitiveRules(ruleAndLevels);
            r.setSensitiveCount(statCount.getCount());

            results.add(r);
        }

        results.sort(Comparator.comparing(SensitivePortraitResponse.Content::getLastLogTime).reversed());
        return results;
    }

    public List<SensitivePortraitResponse.Api> searchByApi(EsQueryDTO queryDTO, BaseData baseData) {
        List<StatCount> statCounts = nginxAccessLogService.groupCount(queryDTO, "apiId");

        List<SensitivePortraitResponse.Api> results = new ArrayList<>();
        for (StatCount statCount : statCounts) {
            EsQueryDTO newQueryDto = EsQueryDTO.builder().build();
            BeanUtil.copyProperties(queryDTO, newQueryDto);

            SensitivePortraitResponse.Api r = new SensitivePortraitResponse.Api();
            ApiQueryResponse api = baseData.getAllApiMap().get(statCount.getLabel());
            if (api == null) {
                continue;
            }
            r.setApiId(api.getId());
            r.setApiName(api.getName());
            r.setSensitiveLevel(api.getSensitiveLevel());
            // appName
            r.setAppId(baseData.getAllAppMap().get(api.getAppId()).getApplicationId());
            r.setAppName(baseData.getAllBizNameAppMap().get(api.getAppId()).getName());
            // logTime
            newQueryDto.addQuery("apiId", statCount.getLabel()).setQueryCount(1);
            EsNginxDTO ngLog = nginxLogEsClient.queryOne(newQueryDto);
            r.setLastLogTime(ngLog.getLogTime());
            // rules
            List<String> ruleByContent = sensitiveApiService.lambdaQuery()
                    .eq(SensitiveApi::getApiId, statCount.getLabel())
                    .eq(SensitiveApi::getDeleted, false)
                    .list().stream()
                    .map(SensitiveApi::getSensitiveRuleId)
                    .distinct()
                    .map(ele -> baseData.getAllRuleMap().get(ele).getName())
                    .collect(Collectors.toList());
            r.setSensitiveRules(ruleByContent);
            r.setSensitiveCount(statCount.getCount());

            results.add(r);
        }

        results.sort(Comparator.comparing(SensitivePortraitResponse.Api::getLastLogTime).reversed());
        return results;
    }

    public List<SensitivePortraitResponse.App> searchByApp(EsQueryDTO queryDTO, BaseData baseData) {
        List<StatCount> statCounts = nginxAccessLogService.groupCount(queryDTO, "appId");

        List<SensitivePortraitResponse.App> results = new ArrayList<>();
        for (StatCount statCount : statCounts) {
            EsQueryDTO newQueryDto = EsQueryDTO.builder().build();
            BeanUtil.copyProperties(queryDTO, newQueryDto);

            SensitivePortraitResponse.App r = new SensitivePortraitResponse.App();
            Application app1 = baseData.getAllAppMap().get(statCount.getLabel());
            if (app1 == null) {
                continue;
            }
            // appName
            r.setAppId(app1.getApplicationId());
            r.setAppName(app1.getName());
            // parentName
            r.setParentAppName("无");
            baseData.getAllApps().stream().filter(app -> app.getApplicationId().equals(app1.getParentId()))
                    .findFirst().ifPresent(app -> r.setParentAppName(app.getName()));
            // logTime
            newQueryDto.addQuery("appId", app1.getApplicationId()).setQueryCount(1);
            EsNginxDTO ngLog = nginxLogEsClient.queryOne(newQueryDto);
            r.setLastLogTime(ngLog.getLogTime());
            // rules
            List<String> apiIdsByApp =
                    baseData.getAllApis().stream().filter(api -> api.getAppId().equals(statCount.getLabel())).map(ApiQueryResponse::getId).collect(Collectors.toList());
            if (apiIdsByApp.isEmpty()) {
                apiIdsByApp.add("-1");
            }
            List<String> ruleByContent = sensitiveApiService.lambdaQuery()
                    .in(SensitiveApi::getApiId, apiIdsByApp)
                    .eq(SensitiveApi::getDeleted, false)
                    .list().stream()
                    .map(SensitiveApi::getSensitiveRuleId)
                    .distinct()
                    .map(ele -> baseData.getAllRuleMap().get(ele).getName())
                    .collect(Collectors.toList());
            r.setSensitiveRules(ruleByContent);

            results.add(r);
        }

        results.sort(Comparator.comparing(SensitivePortraitResponse.App::getLastLogTime).reversed());
        return results;
    }


    public List<SensitivePortraitResponse.ClientIp> searchByClientIp(EsQueryDTO queryDTO) {
        List<StatCount> statCounts = nginxAccessLogService.groupCount(queryDTO, "clientIp");

        List<SensitivePortraitResponse.ClientIp> results = new ArrayList<>();
        for (StatCount statCount : statCounts) {
            EsQueryDTO newQueryDto = EsQueryDTO.builder().build();
            BeanUtil.copyProperties(queryDTO, newQueryDto);
            newQueryDto.setQueryFields(Arrays.asList("uuid", "logTime", "clientIpInfo"));
            SensitivePortraitResponse.ClientIp r = new SensitivePortraitResponse.ClientIp();
            r.setClientIp(statCount.getLabel());
            // logTime
            newQueryDto.addQuery("clientIp", r.getClientIp()).setQueryCount(1);
            EsNginxDTO ngLog = nginxLogEsClient.queryOne(newQueryDto);
            r.setLastLogTime(ngLog.getLogTime());
            r.setAddr(ngLog.getClientIpInfo().getAddr());
            r.setIsp(ngLog.getClientIpInfo().getTranslatedIsp());
            r.setSensitiveCount(statCount.getCount());
            results.add(r);
        }

        // 根据 logTime 排序
        results.sort(Comparator.comparing(SensitivePortraitResponse.ClientIp::getLastLogTime).reversed());
        return results;
    }

    private List<String> findApiIdsBySensitiveContent(EsQueryDTO queryDTO) {
        NativeSearchQuery query = queryDTO.toNativeSearchQuery();
        // 聚合去重 apiId
        TermsAggregationBuilder apiIdAgg = AggregationBuilders
                .terms("distinct_apiIds")
                .field("apiId")
                .size(100);
        query.addAggregation(apiIdAgg);
        query.setMaxResults(0);


        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(query, EsNginxDTO.class);
        if (searchHits.hasAggregations()) {
            AggregationsContainer<?> container = searchHits.getAggregations();
            Aggregations aggregations = (Aggregations) container.aggregations();
            ParsedStringTerms apiIdTerms = aggregations.get("distinct_apiIds");

            List<String> result = new ArrayList<>();
            for (Terms.Bucket bucket : apiIdTerms.getBuckets()) {
                result.add(bucket.getKeyAsString());
            }
            return result;
        }
        return Collections.emptyList();
    }

    public List<String> findRuleIdsBySensitiveContent(EsQueryDTO queryDTO) {
        NativeSearchQuery query = queryDTO.toNativeSearchQuery();

        NestedAggregationBuilder agg = AggregationBuilders
                .nested("nested_sensitive_rules", "sensitiveRules")
                .subAggregation(
                        // AggregationBuilders
                        //         .filter("filtered_content",
                        //                 QueryBuilders.termQuery("sensitiveRules.content", content)
                        //         )
                        //         .subAggregation(
                        //                 AggregationBuilders.terms("distinct_ruleIds")
                        //                         .field("sensitiveRules.ruleId")
                        //                         .size(10000) // 自定义结果条数
                        //         )
                        AggregationBuilders.terms("distinct_ruleIds")
                                .field("sensitiveRules.ruleId")
                                .size(10000) // 自定义结果条数
                );
        query.addAggregation(agg);
        query.setMaxResults(0);

        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(query, EsNginxDTO.class);

        if (searchHits.hasAggregations()) {
            AggregationsContainer<?> container = searchHits.getAggregations();
            Aggregations aggregations = (Aggregations) container.aggregations();
            // 进入嵌套聚合结构
            ParsedNested nestedAgg = aggregations.get("nested_sensitive_rules");
            ParsedStringTerms ruleIdTerms = nestedAgg.getAggregations().get("distinct_ruleIds");

            List<String> result = new ArrayList<>();
            for (Terms.Bucket bucket : ruleIdTerms.getBuckets()) {
                result.add(bucket.getKeyAsString());
            }
            return result;
        }

        return Collections.emptyList();
    }

    public Page<?> search(String type, List<String> keywords, Integer pageNum, Integer pageSize) {
        if (StrUtil.isBlank(type)) {
            return Page.of(pageNum, pageSize, 0);
        }
        if (CollUtil.isEmpty(keywords)) {
            return Page.of(pageNum, pageSize, 0);
        }
        keywords = keywords.stream().map(String::trim).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isEmpty(keywords)) {
            return Page.of(pageNum, pageSize, 0);
        }
        BaseData baseData = getBaseData();
        if (baseData.isEmptyData()) {
            return Page.of(pageNum, pageSize, 0);
        }
        EsQueryDTO queryDTO = getBaseQuery(keywords);
        queryDTO.addMultipleQuery("sensitiveRules.ruleId", baseData.getAllRuleIds());
        queryDTO.addMultipleQuery("apiId", baseData.getAllApiIds())
                .addMultipleQuery("appId", baseData.getAllAppIds());
        int from = (pageNum - 1) * pageSize;
        int to = from + pageSize;
        switch (type.trim()) {
            case "content":
                List<SensitivePortraitResponse.Content> contents = searchByContent(queryDTO, baseData);
                return PageUtils.convertPage(
                        new Page<>(pageNum, pageSize, contents.size()),
                        contents.subList(from, Math.min(contents.size(), to)));
            case "api":
                List<SensitivePortraitResponse.Api> apis = searchByApi(queryDTO, baseData);
                return PageUtils.convertPage(
                        new Page<>(pageNum, pageSize, apis.size()),
                        apis.subList(from, Math.min(apis.size(), to)));
            case "app":
                List<SensitivePortraitResponse.App> apps = searchByApp(queryDTO, baseData);
                return PageUtils.convertPage(
                        new Page<>(pageNum, pageSize, apps.size()),
                        apps.subList(from, Math.min(apps.size(), to)));
            case "clientIp":
                List<SensitivePortraitResponse.ClientIp> clientIps = searchByClientIp(queryDTO);
                return PageUtils.convertPage(
                        new Page<>(pageNum, pageSize, clientIps.size()),
                        clientIps.subList(from, Math.min(clientIps.size(), to)));
        }
        throw new BusinessException("type error");
    }

    private List<String> getQueryAppIds(List<String> keywords) {
        List<Application> apps = applicationService.lambdaQuery()
                .eq(Application::getDeleted, false)
                .like(Application::getName, keywords.get(0))
                .list();
        apps = applicationService.getWithChildren(apps.stream().map(Application::getApplicationId).collect(Collectors.toList()));
        if (keywords.size() > 1) {
            for (int i = 1; i < keywords.size(); i++) {
                int finalI = i;
                apps = apps.stream().filter(app -> app.getName().contains(keywords.get(finalI))).collect(Collectors.toList());
            }
        }
        List<String> appIds = apps.stream().map(Application::getApplicationId).collect(Collectors.toList());
        appIds = applicationService.getIdsWithChildren(appIds);
        return appIds;
    }

    public Object searchDetail(String type, String keyword, LocalDateTime startTime, LocalDateTime endTime) {
        if (StrUtil.isBlank(type)) {
            return null;
        }
        if (StrUtil.isBlank(keyword)) {
            return null;
        }
        BaseData baseData = getBaseData();
        if (baseData.isEmptyData() || !baseData.containsTarget(type, keyword)) {
            return null;
        }
        EsQueryDTO queryDTO = getBaseQuery(null, startTime, endTime);
        queryDTO.addMultipleQuery("apiId", baseData.getAllApiIds());
        queryDTO.addMultipleQuery("appId", baseData.getAllAppIds());
        switch (type.trim()) {
            case "content":
                queryDTO.addMultipleQuery("sensitiveRules.ruleId", baseData.getAllRuleIds());
                queryDTO.addQuery("sensitiveRules.content", keyword);
                return searchDetailByContent(queryDTO, baseData, keyword);
            case "api":
                queryDTO.removeQuery("apiId");
                queryDTO.addQuery("apiId", keyword);
                queryDTO.addMultipleQuery("sensitiveRules.ruleId", baseData.getAllRuleIds());
                return searchDetailByApi(queryDTO, baseData, keyword);
            case "app":
                // 根据 keyword 的 appId 查询子app和及apiId
                queryDTO.removeQuery("apiId");
                queryDTO.removeQuery("appId");
                return searchDetailByApp(queryDTO, baseData, keyword);
            case "clientIp":
                queryDTO.addQuery("clientIp", keyword);
                return searchDetailByClientIp(queryDTO, baseData, keyword);
        }
        throw new BusinessException("type error");
    }


    public Object getCharts(String type, String keyword, LocalDateTime startTime, LocalDateTime endTime) {
        BaseData baseData = getBaseData();
        if (baseData.isEmptyData() || !baseData.containsTarget(type, keyword)) {
            return null;
        }
        if (StrUtil.isBlank(keyword)) {
            return null;
        }
        EsQueryDTO queryDTO = getBaseQuery(null, startTime, endTime);
        queryDTO.addMultipleQuery("apiId", baseData.getAllApiIds());
        switch (type.trim()) {
            case "content":
                queryDTO.addQuery("sensitiveRules.content", keyword.trim());
                return SensitivePortraitChartResponse.Content.builder()
                        .trends(statTrend(queryDTO))
                        .topApis(statApi(queryDTO, baseData))
                        .topClientIps(statClient(queryDTO))
                        .build();
            case "api":
                queryDTO.removeQuery("apiId");
                queryDTO.addQuery("apiId", keyword.trim());
                return SensitivePortraitChartResponse.Api.builder()
                        .trends(statTrend(queryDTO))
                        .topRules(statRule(queryDTO))
                        .topContents(statContent(queryDTO))
                        .topClientIps(statClient(queryDTO))
                        .build();
            case "app":
                queryDTO.addQuery("appId", keyword.trim());
                return SensitivePortraitChartResponse.App.builder()
                        .trends(statTrend(queryDTO))
                        .topApis(statApi(queryDTO, baseData))
                        .topRules(statRule(queryDTO))
                        .topContents(statContent(queryDTO))
                        .topClientIps(statClient(queryDTO))
                        .build();
            case "clientIp":
                queryDTO.addQuery("clientIp", keyword.trim());
                return SensitivePortraitChartResponse.ClientIp.builder()
                        .trends(statTrend(queryDTO))
                        .topApis(statApi(queryDTO, baseData))
                        .topRules(statRule(queryDTO))
                        .topContents(statContent(queryDTO))
                        .build();
        }
        throw new BusinessException("type error");
    }

    private SensitivePortraitDetailResponse.Content searchDetailByContent(EsQueryDTO queryDTO,
                                                                          BaseData baseData,
                                                                          String keyword) {
        SensitivePortraitDetailResponse.Content content = new SensitivePortraitDetailResponse.Content();
        long count = getSensitiveCount(queryDTO);
        long clientCount = getClientCount(queryDTO);
        SensitivePortraitResponse.Content data1 = searchByContent(queryDTO, baseData).get(0);
        // api
        List<String> apiIdsByContent = findApiIdsBySensitiveContent(queryDTO);
        List<SensitivePortraitDetailResponse.Content.ApiAndApp> apis = apiIdsByContent.stream()
                .map(ele -> {
                    ApiInfo apiInfo = apiInfoService.getById(ele);
                    return SensitivePortraitDetailResponse.Content.ApiAndApp.builder()
                            .apiId(ele)
                            .apiName(apiInfo.getName())
                            .appId(baseData.getAllAppMap().get(apiInfo.getAppId()).getApplicationId())
                            .appName(baseData.getAllBizNameAppMap().get(apiInfo.getAppId()).getName())
                            .build();
                }).collect(Collectors.toList());

        content.setKeyword(keyword);
        content.setApis(apis);
        content.setLastLogTime(data1.getLastLogTime());
        content.setSensitiveRules(data1.getSensitiveRules());

        content.setSensitiveApiCount(data1.getApis().size());
        content.setSensitiveCount(Math.toIntExact(count));
        content.setClientIpCount(Math.toIntExact(clientCount));

        return content;
    }

    private SensitivePortraitDetailResponse.Api searchDetailByApi(EsQueryDTO queryDTO,
                                                                  BaseData baseData,
                                                                  String apiId) {
        SensitivePortraitDetailResponse.Api apiDetail = new SensitivePortraitDetailResponse.Api();
        apiDetail.setApiId(apiId);
        apiDetail.setApiName(baseData.getAllApiMap().get(apiId).getName());
        String appId = baseData.getAllApiMap().get(apiId).getAppId();
        apiDetail.setAppName(baseData.getAllBizNameAppMap().get(appId).getName());

        return searchByApi(queryDTO, baseData).stream().findFirst()
                .map(api -> {
                    long count = getSensitiveCount(queryDTO);
                    long clientCount = getClientCount(queryDTO);
                    long sensitiveContentCount = getContentCount(queryDTO);
                    apiDetail.setSensitiveCount(count);
                    apiDetail.setClientIpCount(clientCount);
                    apiDetail.setSensitiveContentCount(sensitiveContentCount);

                    apiDetail.setSensitiveLevel(api.getSensitiveLevel());
                    apiDetail.setSensitiveRules(api.getSensitiveRules());
                    apiDetail.setLastLogTime(api.getLastLogTime());
                    return apiDetail;
                })
                .orElse(apiDetail);
    }

    private SensitivePortraitDetailResponse.App searchDetailByApp(EsQueryDTO queryDTO,
                                                                  BaseData baseData,
                                                                  String appId) {
        List<String> appIds = applicationService.getIdsWithChildren(Collections.singletonList(appId), false);

        SensitivePortraitDetailResponse.App appDetail = new SensitivePortraitDetailResponse.App();
        long count = getSensitiveCount(queryDTO);
        long clientCount = getClientCount(queryDTO);
        long sensitiveContentCount = getContentCount(queryDTO);
        List<String> apiIdsByAppId =
                baseData.getAllApis().stream()
                        .filter(ele -> appIds.contains(ele.getAppId())).map(ApiQueryResponse::getId).collect(Collectors.toList());
        List<SensitiveApi> list = sensitiveApiService.lambdaQuery()
                .select(SensitiveApi::getApiId)
                .between(SensitiveApi::getCreateTime, queryDTO.getStart(), queryDTO.getEnd())
                .in(SensitiveApi::getApiId, apiIdsByAppId).groupBy(SensitiveApi::getApiId).list();

        List<String> apiIds =
                apiInfoService.getByApplicationIds(appIds, false).stream().map(ApiInfo::getId).collect(Collectors.toList());
        queryDTO.addMultipleQuery("apiId", apiIds);
        queryDTO.addMultipleQuery("appId", appIds);
        SensitivePortraitResponse.App app = searchByApp(queryDTO, baseData).get(0);

        appDetail.setAppId(appId);
        appDetail.setAppName(app.getAppName());
        appDetail.setParentAppName(app.getParentAppName());
        appDetail.setSensitiveRules(app.getSensitiveRules());

        appDetail.setLastLogTime(app.getLastLogTime());

        appDetail.setSensitiveCount(Math.toIntExact(count));
        appDetail.setClientIpCount(Math.toIntExact(clientCount));
        appDetail.setSensitiveContentCount(Math.toIntExact(sensitiveContentCount));
        appDetail.setSensitiveApiCount(list.size());

        return appDetail;
    }


    private SensitivePortraitDetailResponse.ClientIp searchDetailByClientIp(EsQueryDTO queryDTO,
                                                                            BaseData baseData,
                                                                            String clientIp) {
        SensitivePortraitDetailResponse.ClientIp clientIpDetail = new SensitivePortraitDetailResponse.ClientIp();
        clientIpDetail.setClientIp(clientIp);

        return searchByClientIp(queryDTO).stream().findFirst()
                .map(clientIpInfo -> {
                    clientIpDetail.setAddr(clientIpInfo.getAddr());
                    clientIpDetail.setIsp(clientIpInfo.getIsp());

                    List<String> ruleNames = findRuleIdsBySensitiveContent(queryDTO).stream()
                            .map(ele -> baseData.getAllRuleMap().get(ele).getName())
                            .collect(Collectors.toList());
                    clientIpDetail.setSensitiveRules(ruleNames);
                    clientIpDetail.setLastLogTime(clientIpInfo.getLastLogTime());

                    long count = getSensitiveCount(queryDTO);
                    long sensitiveContentCount = getContentCount(queryDTO);
                    long sensitiveApiCount = findApiIdsBySensitiveContent(queryDTO).size();
                    clientIpDetail.setSensitiveApiCount(Math.toIntExact(sensitiveApiCount));
                    clientIpDetail.setSensitiveCount(Math.toIntExact(count));
                    clientIpDetail.setSensitiveContentCount(Math.toIntExact(sensitiveContentCount));
                    return clientIpDetail;
                })
                .orElse(clientIpDetail);
    }

    /**
     * 该敏感数据泄露的总次数，一个请求算一次（在时间范围内）
     */
    private long getSensitiveCount(EsQueryDTO queryDTO) {
        return nginxLogEsClient.queryCount(queryDTO);
    }

    private List<SensitiveApiTriggerCountResponse> statApi(EsQueryDTO queryDTO, BaseData baseData) {
        EsQueryDTO newQueryDTO = EsQueryDTO.builder().build();
        BeanUtil.copyProperties(queryDTO, newQueryDTO);
        newQueryDTO.setQueryCount(10);

        Map<String, ApplicationResponse> allBizNameAppMap = baseData.getAllBizNameAppMap();
        List<SensitiveApiTriggerCountResponse> results = new ArrayList<>();
        List<StatCount> statCounts = nginxAccessLogService.groupCount(newQueryDTO, "apiId");
        for (StatCount statCount : statCounts) {
            ApiQueryResponse apiInfo = baseData.getAllApiMap().get(statCount.getLabel());
            if (apiInfo == null) continue;
            ApplicationResponse biaNameApp = allBizNameAppMap.get(apiInfo.getAppId());
            results.add(
                    SensitiveApiTriggerCountResponse.builder()
                            .appId(biaNameApp.getApplicationId())
                            .appName(biaNameApp.getName())
                            .apiId(apiInfo.getId())
                            .apiName(biaNameApp.getName() + ":" + apiInfo.getName())
                            .count(statCount.getCount())
                            .build()
            );

            statCount.setLabel(baseData.getAllApiMap().get(statCount.getLabel()).getName());
        }
        return results;
    }

    private List<StatCount> statClient(EsQueryDTO queryDTO) {
        EsQueryDTO newQueryDTO = EsQueryDTO.builder().build();
        BeanUtil.copyProperties(queryDTO, newQueryDTO);
        newQueryDTO.setQueryCount(10);
        return nginxAccessLogService.groupCount(newQueryDTO, "clientIp");
    }

    private List<StatCount> statRule(EsQueryDTO queryDTO) {
        List<SensitiveRule> rules = sensitiveRuleService.list();
        // map  id name
        Map<String, String> ruleMap = rules.stream().collect(Collectors.toMap(SensitiveRule::getId,
                SensitiveRule::getName));

        EsQueryDTO newQueryDTO = EsQueryDTO.builder().build();
        BeanUtil.copyProperties(queryDTO, newQueryDTO);
        newQueryDTO.setQueryCount(10);
        List<StatCount> statCounts = nginxAccessLogService.groupCount(newQueryDTO, "sensitiveRules.ruleId");
        for (StatCount statCount : statCounts) {
            statCount.setLabel(ruleMap.get(statCount.getLabel()));
        }
        return statCounts;
    }

    private List<StatCount> statContent(EsQueryDTO queryDTO) {
        EsQueryDTO newQueryDTO = EsQueryDTO.builder().build();
        BeanUtil.copyProperties(queryDTO, newQueryDTO);
        newQueryDTO.setQueryCount(10);

        return nginxAccessLogService.groupCount(newQueryDTO, "sensitiveRules.content");
    }

    private long getClientCount(EsQueryDTO queryDTO) {
        CardinalityAggregationBuilder aggregation = AggregationBuilders
                .cardinality("distinct_client_ip")
                .field("clientIp");
        // 构建查询
        NativeSearchQuery query = queryDTO.toNativeSearchQuery();
        query.addAggregation(aggregation);
        query.setMaxResults(0);
        // 执行查询
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(query, EsNginxDTO.class);

        if (searchHits.hasAggregations()) {
            AggregationsContainer<?> container = searchHits.getAggregations();
            Aggregations aggregations = (Aggregations) container.aggregations();
            // 解析聚合结果
            Cardinality nested = aggregations.get("distinct_client_ip");
            return nested.getValue();
        }
        return 0;
    }


    private List<StatCount> statTrend(EsQueryDTO queryDTO) {
        boolean isMonth = queryDTO.getStart().plusDays(90).isBefore(queryDTO.getEnd());
        String statFormat = isMonth ? "yyyy-MM月" : "yyyy-MM-dd日";
        String labelFormat = isMonth ? "MM月" : "dd日";

        // date_histogram 按天聚合
        DateHistogramAggregationBuilder dateHistogram = AggregationBuilders
                .dateHistogram("logTime_per_day")
                .field("logTime")
                .format(statFormat)
                .minDocCount(0)
                // .timeZone(ZoneId.systemDefault())
                ;
        if (isMonth) {
            dateHistogram.calendarInterval(DateHistogramInterval.MONTH);
        } else {
            dateHistogram.fixedInterval(DateHistogramInterval.DAY);
        }

        NativeSearchQuery query = queryDTO.toNativeSearchQuery();
        query.addAggregation(dateHistogram);
        query.setMaxResults(0);

        List<StatCount> statCounts = new ArrayList<>();
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(query, EsNginxDTO.class);
        if (searchHits.hasAggregations()) {
            AggregationsContainer<?> container = searchHits.getAggregations();
            Aggregations aggregations = (Aggregations) container.aggregations();
            Histogram histogram = aggregations.get("logTime_per_day");

            if (histogram != null) {
                histogram.getBuckets().forEach(bucket -> {
                    String yyyyMMdd = bucket.getKeyAsString(); // yyyy-MM-dd
                    statCounts.add(new StatCount(yyyyMMdd, Math.toIntExact(bucket.getDocCount())));
                });
            }
        }

        List<StatCount> results = new ArrayList<>();
        LocalDateTime start = queryDTO.getStart();
        LocalDateTime end = queryDTO.getEnd();
        while (isMonth ?
                start.withDayOfMonth(1).isBefore(end.withDayOfMonth(1)) ||
                        start.withDayOfMonth(1).isEqual(end.withDayOfMonth(1)) :
                start.isBefore(end) || start.isEqual(end)) {
            String yyyyMMdd = start.format(DateTimeFormatter.ofPattern(statFormat));
            StatCount st = new StatCount(start.format(DateTimeFormatter.ofPattern(labelFormat)), 0);
            statCounts.stream().filter(statCount -> statCount.getLabel().equals(yyyyMMdd))
                    .findFirst()
                    .ifPresent(statCount -> st.setCount(statCount.getCount()));
            results.add(st);
            start = isMonth ? start.plusMonths(1) : start.plusDays(1);
        }
        return results;
    }

    private EsQueryDTO getBaseQuery(List<String> keywords) {
        LocalDateTime now = LocalDateTime.now();
        return getBaseQuery(keywords, now.minusDays(30), now);
    }

    private EsQueryDTO getBaseQuery(List<String> keywords, @Nonnull LocalDateTime startTime,
                                    @Nonnull LocalDateTime endTime) {
        EsQueryDTO queryDTO = EsQueryDTO.builder().build();

        queryDTO.setQueryCount(100);
        queryDTO.setStart(startTime);
        queryDTO.setEnd(endTime);
        queryDTO.setQueryFields(Arrays.asList("uuid", "logTime"));
        queryDTO.addQuery("requestResourceType", "api");
        queryDTO.addExistQuery("sensitiveRules");
        queryDTO.addExistQuery("sensitiveRules");
        queryDTO.orderBy("logTime", SortOrder.DESC);

        if (CollUtil.isNotEmpty(keywords)) {
            List<EsQueryDTO.Element> shouldElements = new ArrayList<>();
            // 涉敏规则
            LambdaQueryChainWrapper<SensitiveRule> ruleQ =
                    sensitiveRuleService.lambdaQuery().eq(SensitiveRule::getIsDeleted, false);
            for (String keyword : keywords) {
                ruleQ.like(SensitiveRule::getName, keyword);
            }
            List<String> ruleIds = ruleQ.list().stream().map(SensitiveRule::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(ruleIds)) {
                shouldElements.add(new EsQueryDTO.Element("sensitiveRules.ruleId", ruleIds,
                        EsQueryDTO.QueryType.NESTED_LIST));
            }
            // 应用名
            List<String> queryAppIds = getQueryAppIds(keywords);
            if (CollUtil.isNotEmpty(queryAppIds)) {
                shouldElements.add(new EsQueryDTO.Element("appId", queryAppIds, EsQueryDTO.QueryType.LIST));
            }
            List<String> queryKeys = keywords.stream().map(keyword -> "*" + keyword + "*").collect(Collectors.toList());
            // 应用 host
            shouldElements.add(new EsQueryDTO.Element("httpHost", queryKeys, EsQueryDTO.QueryType.LIKE_LIST));
            // api uri
            shouldElements.add(new EsQueryDTO.Element("uri", queryKeys, EsQueryDTO.QueryType.LIKE_LIST));
            // clientIp
            shouldElements.add(new EsQueryDTO.Element("clientIp", queryKeys, EsQueryDTO.QueryType.LIKE_LIST));
            // content
            shouldElements.add(new EsQueryDTO.Element("sensitiveRules.content", queryKeys,
                    EsQueryDTO.QueryType.NESTED_LIKE_LIST));

            queryDTO.addShouldQuery(shouldElements);
        }
        return queryDTO;
    }


    private BaseData getBaseData() {
        BaseData baseData = new BaseData();
        baseData.setAllRules(sensitiveRuleService.list());
        baseData.setAllRuleIds(baseData.getAllRules().stream().map(SensitiveRule::getId).collect(Collectors.toList()));
        baseData.setAllRuleMap(baseData.getAllRules().stream().collect(Collectors.toMap(SensitiveRule::getId,
                Function.identity(), (k1, k2) -> k1)));

        baseData.setAllApps(applicationService.list());
        baseData.setAllAppIds(baseData.getAllApps().stream().map(Application::getApplicationId).collect(Collectors.toList()));
        baseData.setAllAppMap(baseData.getAllApps().stream().collect(Collectors.toMap(Application::getApplicationId,
                Function.identity(), (k1, k2) -> k1)));

        baseData.setAllBizNameApps(applicationBusinessService.listWithBizName());
        baseData.setAllBizNameAppMap(baseData.getAllBizNameApps().stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                Function.identity(), (k1, k2) -> k1)));

        baseData.setAllApis(apiInfoService.getBaseMapper().query(new QueryApiRequest()));
        baseData.setAllApiIds(baseData.getAllApis().stream().map(ApiQueryResponse::getId).collect(Collectors.toList()));
        baseData.setAllApiMap(baseData.getAllApis().stream().collect(Collectors.toMap(ApiQueryResponse::getId,
                Function.identity(), (k1, k2) -> k1)));
        return baseData;
    }

    private EsQueryDTO copyQuery(EsQueryDTO queryDTO) {
        EsQueryDTO build = EsQueryDTO.builder().build();
        BeanUtil.copyProperties(queryDTO, build);
        return build;
    }

    public Page<SensitivePortraitLogResponse> queryLog(QuerySensitivePortraitLogRequest query) {
        BaseData baseData = getBaseData();
        List<SensitiveRule> rules = sensitiveRuleService.list();
        EsQueryDTO queryDTO = query.toEsQueryDTO(rules);
        List<SensitivePortraitLogResponse> records = new ArrayList<>();
        if (CollUtil.isEmpty(query.getApiId())) {
            queryDTO.addMultipleQuery("apiId", baseData.getAllApiIds());
        }
        if (CollUtil.isEmpty(query.getAppId())) {
            queryDTO.addMultipleQuery("appId", baseData.getAllAppIds());
        } else {
            List<String> idsWithChildren = applicationService.getIdsWithChildren(query.getAppId(), false);
            if (idsWithChildren.isEmpty()) {
                idsWithChildren.add("-1");
            }
            queryDTO.addMultipleQuery("appId", idsWithChildren);
        }
        queryDTO.orderBy("logTime", SortOrder.DESC);

        int size = query.getPageSize();
        int from = (query.getPageNum() - 1) * size;

        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        nativeSearchQuery.addAggregation(
                AggregationBuilders.nested("sensitive_rules_nested", "sensitiveRules")
                        .subAggregation(
                                AggregationBuilders.terms("rule_content_composite")
                                        .script(new Script("doc['sensitiveRules.ruleId'].value + '||' + " +
                                                "doc['sensitiveRules.content'].value"))
                                        .size(10000)
                                        .order(BucketOrder.key(true))
                                        .subAggregation(
                                                PipelineAggregatorBuilders.bucketSort("pagination",
                                                        Collections.singletonList(
                                                                SortBuilders.fieldSort("_key").order(SortOrder.ASC)
                                                        )).from(from).size(size)
                                        )
                                        .subAggregation(
                                                AggregationBuilders.topHits("top_doc")
                                                        .fetchSource(false)
                                                        .size(1)
                                        )
                        )
        );

        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);
        if (searchHits.hasAggregations()) {
            AggregationsContainer<?> container = searchHits.getAggregations();
            Aggregations aggregations = (Aggregations) container.aggregations();
            ParsedNested nestedAgg = aggregations.get("sensitive_rules_nested");

            ParsedStringTerms compositeTerms = nestedAgg.getAggregations().get("rule_content_composite");

            List<String> logIds = new ArrayList<>();
            List<SensitivePortraitLogResponse> list = new ArrayList<>();
            for (Terms.Bucket bucket : compositeTerms.getBuckets()) {
                String[] parts = bucket.getKeyAsString().split("\\|\\|", 2);
                String ruleId = parts.length > 0 ? parts[0] : "";
                String content = parts.length > 1 ? parts[1] : "";

                ParsedTopHits topHits = bucket.getAggregations().get("top_doc");
                String docId = topHits.getHits().getAt(0).getId();
                logIds.add(docId);

                SensitivePortraitLogResponse response = new SensitivePortraitLogResponse();
                response.setLogId(docId);
                response.setSensitiveRule(baseData.getAllRuleMap().get(ruleId).getName());
                response.setSensitiveLevel(baseData.getAllRuleMap().get(ruleId).getLevel());
                response.setSensitiveContent(content);
                list.add(response);
            }

            NativeSearchQuery nativeSearchQuery1 = queryDTO.toNativeSearchQuery();
            nativeSearchQuery1.addAggregation(
                    AggregationBuilders.nested("sensitive_rules_nested", "sensitiveRules")
            );

            long total = 0;
            SearchHits<EsNginxDTO> docCountSearchHits = elasticsearchRestTemplate.search(nativeSearchQuery1,
                    EsNginxDTO.class);
            if (docCountSearchHits.hasAggregations()) {
                AggregationsContainer<?> container1 = docCountSearchHits.getAggregations();
                Aggregations aggregations1 = (Aggregations) container1.aggregations();
                ParsedNested nestedAgg1 = aggregations1.get("sensitive_rules_nested");
                total = nestedAgg1.getDocCount();
            }
            List<SensitivePortraitLogResponse> results = new ArrayList<>();
            for (SensitivePortraitLogResponse response : list) {
                queryDTO.removeQuery("uuid");
                queryDTO.addQuery("uuid", response.getLogId());
                EsNginxDTO esNginxDTO = nginxLogEsClient.queryOne(queryDTO);
                SensitivePortraitLogResponse logResponse = toLogResponse(esNginxDTO, baseData);
                logResponse.setSensitiveLevel(response.getSensitiveLevel());
                logResponse.setSensitiveContent(response.getSensitiveContent());
                logResponse.setSensitiveRule(response.getSensitiveRule());
                results.add(logResponse);
            }
            return new Page<SensitivePortraitLogResponse>(query.getPageNum(), query.getPageSize(), total)
                    .setRecords(results);
        }
        return null;
        //
        // Page<EsNginxDTO> logPage = nginxLogEsClient.queryPage(queryDTO);
        //
        // for (EsNginxDTO ngLog : logPage.getRecords()) {
        //     List<SensitivePortraitLogResponse> logResponse = toLogResponse(ngLog, baseData);
        //     records.addAll(logResponse);
        // }
        // return PageUtils.convertPage(logPage, records);
    }

    private SensitivePortraitLogResponse toLogResponse(EsNginxDTO ngLog, BaseData baseData) {
        SensitivePortraitLogResponse logResponse = new SensitivePortraitLogResponse();
        logResponse.setLogId(ngLog.getUuid());
        logResponse.setLogTime(ngLog.getLogTime());
        logResponse.setClientIp(ngLog.getClientIp());
        logResponse.setApiId(ngLog.getApiId());
        logResponse.setAppId(ngLog.getAppId());
        ApiQueryResponse api = baseData.getAllApiMap().get(ngLog.getApiId());
        if (api == null) {
            log.info("api is null, apiId: {}", ngLog.getApiId());
        } else {
            logResponse.setApiName(api.getName());
        }
        logResponse.setAppName(baseData.getAllBizNameAppMap().get(ngLog.getAppId()).getName());
        logResponse.setUri(ngLog.getUri());
        logResponse.setUrl(ngLog.getUrl());
        logResponse.setRequestState(ngLog.getReqState());

        logResponse.setAddr(ngLog.getClientIpInfo().getAddr());
        return logResponse;
    }

    private List<SensitivePortraitLogResponse> toLogResponses(EsNginxDTO ngLog, BaseData baseData) {
        List<SensitivePortraitLogResponse> results = new ArrayList<>();
        for (EsSensitiveRuleDTO sensitiveRule : ngLog.getSensitiveRules()) {
            SensitivePortraitLogResponse logResponse = new SensitivePortraitLogResponse();
            logResponse.setLogId(ngLog.getUuid());
            logResponse.setLogTime(ngLog.getLogTime());
            logResponse.setClientIp(ngLog.getClientIp());
            logResponse.setApiId(ngLog.getApiId());
            logResponse.setAppId(ngLog.getAppId());
            ApiQueryResponse api = baseData.getAllApiMap().get(ngLog.getApiId());
            if (api == null) {
                log.info("api is null, apiId: {}", ngLog.getApiId());
            } else {
                logResponse.setApiName(api.getName());
            }
            logResponse.setAppName(baseData.getAllBizNameAppMap().get(ngLog.getAppId()).getName());
            logResponse.setUri(ngLog.getUri());
            logResponse.setUrl(ngLog.getUrl());
            logResponse.setRequestState(ngLog.getReqState());

            logResponse.setSensitiveRule(baseData.getAllRuleMap().get(sensitiveRule.getRuleId()).getName());
            logResponse.setSensitiveContent(sensitiveRule.getContent());
            logResponse.setSensitiveLevel(baseData.getAllRuleMap().get(sensitiveRule.getRuleId()).getLevel());

            logResponse.setAddr(ngLog.getClientIpInfo().getAddr());
            results.add(logResponse);
        }
        return results;
    }

    public List<?> statLog(QuerySensitivePortraitLogStatRequest query) {
        BaseData baseData = getBaseData();
        switch (query.getType()) {
            case "content":
                return queryContent(query, baseData);
            case "api":
                return queryApi(query, baseData);
            case "clientIp":
                return queryClientIp(query, baseData);
            default:
                return Collections.emptyList();
        }
    }

    public List<SensitivePortraitResponse.StatContent> queryContent(QuerySensitivePortraitLogStatRequest query,
                                                                    BaseData baseData) {
        EsQueryDTO queryDTO = getQueryDTO(query, baseData);

        List<SensitivePortraitResponse.Content> sp_contents = searchByContent(queryDTO, baseData);

        List<SensitivePortraitResponse.StatContent> results = new ArrayList<>();
        for (SensitivePortraitResponse.Content content : sp_contents) {
            EsQueryDTO queryDTO1 = copyQuery(queryDTO);
            queryDTO1.setQueryCount(1);
            queryDTO1.setQueryFields(Arrays.asList("logTime", "uuid", "clientIp", "sensitiveRules", "appId", "apiId"));
            queryDTO1.addQuery("sensitiveRules.content", content.getKeyword());
            queryDTO1.orderBy("logTime", SortOrder.ASC);
            SensitivePortraitResponse.StatContent statContent = new SensitivePortraitResponse.StatContent();
            EsNginxDTO ngLog1 = nginxLogEsClient.queryOne(queryDTO1);
            statContent.setKeyword(content.getKeyword());
            statContent.setSensitiveCount(content.getSensitiveCount());
            statContent.setLastLogTime(content.getLastLogTime());

            if (ngLog1 != null) {
                statContent.setFirstLogTime(ngLog1.getLogTime());
                results.add(statContent);
            }
        }
        return results;
    }

    public List<SensitivePortraitResponse.StatApi> queryApi(QuerySensitivePortraitLogStatRequest query,
                                                            BaseData baseData) {
        EsQueryDTO queryDTO = getQueryDTO(query, baseData);

        List<SensitivePortraitResponse.Api> sp_apis = searchByApi(queryDTO, baseData);

        queryDTO.removeQuery("apiId");
        List<SensitivePortraitResponse.StatApi> results = new ArrayList<>();
        for (SensitivePortraitResponse.Api spApi : sp_apis) {
            EsQueryDTO queryDTO1 = copyQuery(queryDTO);
            queryDTO1.setQueryCount(1);
            queryDTO1.setQueryFields(Arrays.asList("logTime", "uuid", "clientIp", "sensitiveRules", "appId", "apiId"));
            queryDTO1.addQuery("apiId", spApi.getApiId());
            queryDTO1.orderBy("logTime", SortOrder.ASC);
            EsNginxDTO ngLog1 = nginxLogEsClient.queryOne(queryDTO1);
            if (ngLog1 != null) {
                SensitivePortraitResponse.StatApi statApi = new SensitivePortraitResponse.StatApi(spApi);
                statApi.setFirstLogTime(ngLog1.getLogTime());
                statApi.setAppName(baseData.getAllBizNameAppMap().get(ngLog1.getAppId()).getName());
                results.add(statApi);
            }
        }
        return results;
    }

    public List<SensitivePortraitResponse.StatClientIp> queryClientIp(QuerySensitivePortraitLogStatRequest query,
                                                                      BaseData baseData) {
        EsQueryDTO queryDTO = getQueryDTO(query, baseData);
        List<SensitivePortraitResponse.ClientIp> ap_clientIps = searchByClientIp(queryDTO);

        List<SensitivePortraitResponse.StatClientIp> results = new ArrayList<>();
        for (SensitivePortraitResponse.ClientIp clientIp : ap_clientIps) {
            SensitivePortraitResponse.StatClientIp statClient = new SensitivePortraitResponse.StatClientIp();
            statClient.setClientIp(clientIp.getClientIp());
            statClient.setAddr(clientIp.getAddr());
            statClient.setIsp(clientIp.getIsp());
            statClient.setSensitiveCount(clientIp.getSensitiveCount());
            statClient.setLastLogTime(clientIp.getLastLogTime());

            EsQueryDTO queryDTO1 = copyQuery(queryDTO);
            queryDTO1.setQueryCount(1);
            queryDTO1.setQueryFields(Arrays.asList("logTime", "uuid", "clientIp", "sensitiveRules", "appId", "apiId"));
            queryDTO1.addQuery("clientIp", clientIp.getClientIp());
            queryDTO1.orderBy("logTime", SortOrder.ASC);
            EsNginxDTO ngLog1 = nginxLogEsClient.queryOne(queryDTO1);
            if (ngLog1 != null) {
                statClient.setFirstLogTime(ngLog1.getLogTime());
                results.add(statClient);
            }
        }
        return results;
    }


    private EsQueryDTO getQueryDTO(QuerySensitivePortraitLogStatRequest query, BaseData baseData) {
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .pageNum(query.getPageNum())
                .pageSize(query.getPageSize())
                .start(query.getStartTime())
                .end(query.getEndTime())
                .build()
                .addExistQuery("sensitiveRules")
                .addQuery("requestResourceType", Constant.Api.REQUEST_RESOURCE_TYPE_API)
                .addQuery(StrUtil.isNotBlank(query.getContent()), "sensitiveRules.content", query.getContent())
                .addQuery(StrUtil.isNotBlank(query.getClientIp()), "clientIp", query.getClientIp());
        if (StrUtil.isNotBlank(query.getAppId())) {
            queryDTO.addMultipleQuery("appId", getAppIdWithChildren(baseData.getAllApps(), query.getAppId()));
        } else {
            queryDTO.addMultipleQuery("appId", baseData.getAllAppIds());
        }
        if (StrUtil.isNotBlank(query.getApiId())) {
            queryDTO.addQuery("apiId", query.getApiId());
        } else {
            queryDTO.addMultipleQuery("apiId", baseData.getAllApiIds());
        }

        Optional.ofNullable(query.getQueryCount()).ifPresent(queryDTO::setQueryCount);
        return queryDTO;
    }

    private List<String> getAppIdWithChildren(List<Application> apps, String appId) {
        Map<String, List<Application>> parentIdMap = apps.stream()
                .collect(Collectors.groupingBy(app -> app.getParentId() == null ? "ROOT" : app.getParentId()));

        List<String> result = new ArrayList<>();
        Deque<String> queue = new ArrayDeque<>();
        queue.add(appId);

        while (!queue.isEmpty()) {
            String current = queue.poll();
            result.add(current); // 直接加入结果
            List<Application> children = parentIdMap.getOrDefault(current, Collections.emptyList());
            for (Application child : children) {
                queue.add(child.getApplicationId());
            }
        }

        return result;
    }

    /**
     * 麻了
     */
    public Object statLeak(QuerySensitivePortraitLeakRequest query) {
        QuerySensitivePortraitLogStatRequest request = new QuerySensitivePortraitLogStatRequest();
        request.setType(query.getType());
        request.setStartTime(query.getStartTime());
        request.setEndTime(query.getEndTime());
        // request.setQueryCount(10);
        BaseData baseData = getBaseData();

        AtomicInteger index = new AtomicInteger(1);

        switch (query.getType()) {
            case "content": //  api - ip
                request.setContent(query.getKeyword());
                return new JSONObject()
                        .set("id", index.getAndIncrement())
                        .set("content", query.getKeyword())
                        .set("apis", queryApi(request, baseData).stream()
                                .map(statApi -> {
                                    request.setApiId(statApi.getApiId());
                                    return JSONUtil.parseObj(statApi)
                                            .set("id", index.getAndIncrement())
                                            .set("firstLogTime",
                                                    statApi.getFirstLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                            .set("lastLogTime",
                                                    statApi.getLastLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                            .set("clientIps", queryClientIp(request, baseData).stream()
                                                    .map(ele -> JSONUtil.parseObj(ele)
                                                            .set("firstLogTime",
                                                                    ele.getFirstLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                                            .set("lastLogTime",
                                                                    ele.getLastLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                                            .set("id", index.getAndIncrement()))
                                                    .collect(Collectors.toList()));
                                }).collect(Collectors.toList()));
            case "api": //  content - ip
                request.setApiId(query.getKeyword());
                return new JSONObject()
                        .set("id", index.getAndIncrement())
                        .set("apiId", query.getKeyword())
                        .set("contents", queryContent(request, baseData).stream()
                                .map(statContent -> {
                                    request.setContent(statContent.getKeyword());
                                    return JSONUtil.parseObj(statContent)
                                            .set("id", index.getAndIncrement())
                                            .set("firstLogTime",
                                                    statContent.getFirstLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                            .set("lastLogTime",
                                                    statContent.getLastLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                            .set("clientIps", queryClientIp(request, baseData).stream()
                                                    .map(ele -> JSONUtil.parseObj(ele)
                                                            .set("firstLogTime",
                                                                    ele.getFirstLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                                            .set("lastLogTime",
                                                                    ele.getLastLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                                            .set("id", index.getAndIncrement()))
                                                    .collect(Collectors.toList()));
                                }).collect(Collectors.toList()));
            case "app": //  content - api - ip
                request.setAppId(query.getKeyword());
                return new JSONObject()
                        .set("id", index.getAndIncrement())
                        .set("appId", query.getKeyword())
                        .set("contents", queryContent(request, baseData).stream()
                                .map(statContent -> {
                                    request.setContent(statContent.getKeyword());
                                    request.setApiId(null);
                                    return JSONUtil.parseObj(statContent)
                                            .set("id", index.getAndIncrement())
                                            .set("firstLogTime",
                                                    statContent.getFirstLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                            .set("lastLogTime",
                                                    statContent.getLastLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                            .set("apis", queryApi(request, baseData).stream()
                                                    .map(statApi -> {
                                                        request.setApiId(statApi.getApiId());
                                                        return JSONUtil.parseObj(statApi)
                                                                .set("id", index.getAndIncrement())
                                                                .set("firstLogTime",
                                                                        statApi.getFirstLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                                                .set("lastLogTime",
                                                                        statApi.getLastLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                                                .set("clientIps",
                                                                        queryClientIp(request, baseData).stream()
                                                                                .map(ele -> JSONUtil.parseObj(ele)
                                                                                        .set("id",
                                                                                                index.getAndIncrement())
                                                                                        .set("firstLogTime",
                                                                                                ele.getFirstLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                                                                        .set("lastLogTime",
                                                                                                ele.getLastLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                                                                )
                                                                                .collect(Collectors.toList()));
                                                    }).collect(Collectors.toList()));
                                }).collect(Collectors.toList()));
            case "clientIp": //  content - api
                request.setClientIp(query.getKeyword());
                return new JSONObject()
                        .set("id", index.getAndIncrement())
                        .set("clientIp", query.getKeyword())
                        .set("contents", queryContent(request, baseData).stream()
                                .map(statContent -> {
                                    request.setContent(statContent.getKeyword());
                                    return JSONUtil.parseObj(statContent)
                                            .set("id", index.getAndIncrement())
                                            .set("firstLogTime",
                                                    statContent.getFirstLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                            .set("lastLogTime",
                                                    statContent.getLastLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                            .set("apis", queryApi(request, baseData).stream()
                                                    .map(statApi -> JSONUtil.parseObj(statApi)
                                                            .set("id", index.getAndIncrement())
                                                            .set("firstLogTime",
                                                                    statApi.getFirstLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                                            .set("lastLogTime",
                                                                    statApi.getLastLogTime().format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)))
                                                    )
                                                    .collect(Collectors.toList()));
                                }).collect(Collectors.toList()));
        }
        return null;
    }

    @Data
    public static class BaseData {
        private List<SensitiveRule> allRules;
        private Map<String, SensitiveRule> allRuleMap;
        private List<String> allRuleIds;

        private List<Application> allApps;
        private Map<String, Application> allAppMap;
        private List<ApplicationResponse> allBizNameApps;
        private Map<String, ApplicationResponse> allBizNameAppMap;
        private List<String> allAppIds;

        private List<ApiQueryResponse> allApis;
        private Map<String, ApiQueryResponse> allApiMap;
        private List<String> allApiIds;
        private List<ApiHttpInfoResponse> allApiInfos;

        public boolean isEmptyData() {
            return CollUtil.isEmpty(allRules) || CollUtil.isEmpty(allApps) || CollUtil.isEmpty(allApis);
        }

        public boolean containsTarget(String type, String target) {
            if (type.equals("app")) {
                return allAppIds.contains(target);
            } else if (type.equals("api")) {
                return allApiIds.contains(target);
            }
            return true;
        }
    }
}
