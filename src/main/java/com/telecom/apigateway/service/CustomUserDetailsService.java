package com.telecom.apigateway.service;

import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.Role;
import com.telecom.apigateway.model.entity.User;
import com.telecom.apigateway.model.entity.UserRole;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.telecom.apigateway.model.entity.UserInfo;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class CustomUserDetailsService {

    private final UserService userService;
    private final UserInfoService userInfoService;
    private final RoleService roleService;
    private final UserRoleService userRoleService;

//    public UserDetails loadUserByUsername(String usernameOrPhone) throws UsernameNotFoundException {
//        User user = userService.getByUsernameOrPhone(usernameOrPhone)
//                .orElseThrow(() ->
//                        new UsernameNotFoundException("User not found with username or phone : " + usernameOrPhone)
//                );
//        if (user.getAccountStatus().equals("LOCKED")) {
//            throw new BusinessException(ResultCodeEnum.USER_LOCKED);
//        }
//        UserInfo userInfo = userInfoService.lambdaQuery()
//                .eq(UserInfo::getUsername, user.getUsername())
//                .eq(UserInfo::getDeleted, false)
//                .oneOpt()
//                .orElseThrow(() ->
//                        new UsernameNotFoundException("User not found with username or phone : " + usernameOrPhone)
//                );
//
//        List<UserRole> userRoles = userRoleService.listByUsername(user.getUsername());
//
//        List<Role> list = roleService.lambdaQuery()
//                .in(Role::getRoleId, userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList()))
//                .eq(Role::getDeleted, false)
//                .list();
//
//        Set<GrantedAuthority> authorities = list.stream()
//                .map((role) -> new SimpleGrantedAuthority(role.getRoleId()))
//                .collect(Collectors.toSet());
//
//        return new org.springframework.security.core.userdetails.User(
//                user.getUsername(),
//                user.getPassword(),
//                authorities
//        );
//    }
}