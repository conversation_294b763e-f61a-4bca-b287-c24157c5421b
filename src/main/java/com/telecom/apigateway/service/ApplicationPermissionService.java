package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.entity.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ApplicationPermissionService {
    private final RoleService roleService;
    private final UserRoleService userRoleService;
    private final ApplicationService applicationService;
    private final MenuPermissionService menuPermissionService;

    public List<String> getApplicationPermissions(String username) {
        List<Role> roles = getRolesByUsername(username);
        if (roles.stream().map(Role::getRoleId).collect(Collectors.toList()).contains(Constant.Role.ADMIN)) {
            return applicationService.list().stream().map(Application::getApplicationId).collect(Collectors.toList());
        }
        // 现在最多两层应用
        List<String> applicationIds = roles.stream().map(Role::getApplicationPermissions).flatMap(Arrays::stream).distinct().collect(Collectors.toList());
        List<String> childrenApplicationIds = applicationService.getIdsWithChildren(applicationIds, false);
        List<String> result = new ArrayList<>();
        result.addAll(applicationIds);
        result.addAll(childrenApplicationIds);

        return result.stream().distinct().collect(Collectors.toList());
    }

    public List<Role> getRolesByUsername(String username) {
        List<UserRole> userRoles = userRoleService.listByUsername(username);
        if (userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList()).contains(Constant.Role.ADMIN)) {
            return roleService.list();
        }
        return roleService.listByRoleIds(userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList()));
    }

    public List<String> getRoleIdsByUsername(String username) {
        return getRolesByUsername(username).stream().map(Role::getRoleId).collect(Collectors.toList());
    }

    public List<String> getPagePermission() {
        String username = StpUtil.getLoginIdAsString();
        List<Role> roles = this.getRolesByUsername(username);
        if (roles.stream().map(Role::getRoleId).anyMatch((id) -> id.equals(Constant.Role.ADMIN))) {
            return menuPermissionService.list().stream().map(MenuPermission::getCode).collect(Collectors.toList());
        }
        return roles.stream().map(Role::getMenuPermissions).flatMap(Arrays::stream).distinct().collect(Collectors.toList());
    }
}
