package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.model.entity.UserInfo;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;

import java.util.*;

public interface UserInfoService extends IService<UserInfo> {
    Optional<UserInfo> getByUsername(String username);

    List<UserInfo> listByUsernames(Collection<String> updateUsernames);
    List<UserInfo> list();
}
