package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.Application;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface ApplicationService extends IService<Application> {
    Optional<Application> getByApplicationId(String applicationId);

    List<Application> getApplications(Collection<String> applicationIds);

    List<Application> getByUrl(String host, String port, String uri);

    Application getByApplicationIdWithPermission(String applicationId);

    List<Application> listWithDataScope();

    List<Application> list();

    List<Application> getWithChildren(List<String> applicationIds);

    List<Application> getWithChildren(List<String> applicationIds, boolean validPermission);

    List<String> getIdsWithChildren(List<String> applicationIds, boolean validPermission);

    List<String> getIdsWithChildren(List<String> applicationIds);

    Optional<Application> getRootApplication(String applicationId);

    Optional<Application> getBaseApplication(String applicationId);

    List<Application> listWithChainName();

    List<String> getApplicationPermissions(List<String> applicationIds, List<String> permissionList);

}
