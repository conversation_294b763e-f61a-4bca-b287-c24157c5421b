package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.UserRole;

import java.util.List;

public interface UserRoleService extends IService<UserRole> {
    List<UserRole> listByUsername(String username);
    List<UserRole> listByRoleIds(List<String> roleIds);
    List<UserRole> listByRoleId(String roleId);

    void deleteByUsername(String username);
}
