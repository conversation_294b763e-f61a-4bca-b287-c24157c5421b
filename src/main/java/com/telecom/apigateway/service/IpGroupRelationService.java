package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.IpGroupRelation;

import java.util.Collection;
import java.util.List;

public interface IpGroupRelationService extends IService<IpGroupRelation> {
    List<IpGroupRelation> list();

    List<IpGroupRelation> getByGroupId(String groupId);

    List<IpGroupRelation> getByGroupIds(Collection<String> groupIds);

    List<IpGroupRelation> getByGroupIds(Collection<String> groupIds, String ip);

    List<IpGroupRelation> getByIp(String ip);
}
