package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.IpGroupRelationMapper;
import com.telecom.apigateway.model.entity.IpGroupRelation;
import com.telecom.apigateway.service.IpGroupRelationService;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Repository
public class IpGroupRelationServiceImpl extends ServiceImpl<IpGroupRelationMapper, IpGroupRelation> implements IpGroupRelationService {
    @Override
    public List<IpGroupRelation> list() {
        return this.lambdaQuery()
                .eq(IpGroupRelation::getDeleted, false)
                .list();
    }

    @Override
    public List<IpGroupRelation> getByGroupId(String groupId) {
        return this.lambdaQuery()
                .eq(IpGroupRelation::getIpGroupId, groupId)
                .eq(IpGroupRelation::getDeleted, false)
                .list();
    }

    @Override
    public List<IpGroupRelation> getByGroupIds(Collection<String> groupIds) {
        if (groupIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(IpGroupRelation::getIpGroupId, groupIds)
                .eq(IpGroupRelation::getDeleted, false)
                .list();
    }

    @Override
    public List<IpGroupRelation> getByGroupIds(Collection<String> groupIds, String ip) {
        if (groupIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(IpGroupRelation::getIpGroupId, groupIds)
                .eq(IpGroupRelation::getIpAddress, ip)
                .eq(IpGroupRelation::getDeleted, false)
                .list();
    }

    @Override
    public List<IpGroupRelation> getByIp(String ip) {
        return this.lambdaQuery()
                .eq(IpGroupRelation::getIpAddress, ip)
                .eq(IpGroupRelation::getDeleted, false)
                .list();
    }
}
