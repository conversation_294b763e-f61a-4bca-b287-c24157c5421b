package com.telecom.apigateway.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.vo.response.ApiStatResponse;
import com.telecom.apigateway.model.vo.response.StatCount;
import com.telecom.apigateway.model.vo.response.StatCountResponse;
import com.telecom.apigateway.service.ApplicationService;
import com.telecom.apigateway.service.LogService;
import com.telecom.apigateway.utils.DateTimeUtils;
import com.telecom.apigateway.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.common.geo.GeoPoint;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedNested;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedReverseNested;
import org.elasticsearch.search.aggregations.bucket.nested.ReverseNestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.GeoBounds;
import org.elasticsearch.search.aggregations.metrics.GeoBoundsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.GeoCentroid;
import org.elasticsearch.search.aggregations.metrics.GeoCentroidAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.ValueCount;
import org.elasticsearch.search.aggregations.metrics.ValueCountAggregationBuilder;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.telecom.apigateway.common.Constant.Api.REQUEST_RESOURCE_TYPE_API;

/**
 * <AUTHOR>
 * @date 2024-08-13
 */
@Slf4j
@Service
public class NginxAccessLogService implements LogService {

    @Resource
    private ApplicationService applicationService;
    @Resource
    private NginxLogEsClient nginxLogEsClient;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    /**
     * 查询访问日志, 指定时间范围内1条
     *
     * @param apiId apiId
     */
    public EsNginxDTO queryOneAccessLog(String apiId, int range) {
        Pair<LocalDateTime, LocalDateTime> timeByRange = DateTimeUtils.rangeTime(range);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(1)
                .start(timeByRange.getLeft())
                .end(timeByRange.getRight())
                .build()
                .addQuery("apiId", apiId)
                .addMultipleQuery("statusCode", Arrays.asList(Constant.Api.NORMAL_STATUS_CODES));
        return nginxLogEsClient.queryOne(queryDTO);
    }


    public ApiStatResponse stat(String apiId) {
        LocalDateTime end = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0).plusDays(1);
        LocalDateTime start = end.minusDays(8);
        ApiStatResponse stat = new ApiStatResponse();
        // *********** 状态码 ***********
        String appId = null;
        List<StatCount> statStatusCode = statStatusCode(appId, apiId, start, end);
        stat.setResultCodes(statStatusCode);
        // // *********** 访问国家/城市分布 ***********
        Pair<List<StatCount>, List<StatCount>> pair = statRegions(appId, apiId, start, end);
        stat.setCountries(pair.getLeft());
        stat.setCities(pair.getRight());
        // *********** 访问者设备 ***********
        List<StatCount> statDevices = statDevice(appId, apiId, start, end);
        stat.setDevices(statDevices);
        // *********** 访问者来源 ***********
        List<StatCount> statSources = statSource(appId, apiId, start, end);
        stat.setSources(statSources);
        // // *********** api 访问量趋势 ***********
        List<StatCount> statVisitTrends = statVisitTrend(appId, apiId, start, end);
        stat.setVisitTrends(statVisitTrends);
        return stat;
    }

    public List<StatCount> statStatusCode(String appId, String apiId, LocalDateTime startTime,
                                          LocalDateTime endTime) {
        List<String> permittedAppIds = getPermittedAppIds(appId);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime)
                .build()
                .addMultipleQuery(!permittedAppIds.isEmpty(), "appId", permittedAppIds)
                .addQuery(StrUtil.isNotBlank(apiId), "apiId", apiId);

        if (appId != null) {
            Optional<Application> byApplicationId = applicationService.getByApplicationId(appId);
            if (byApplicationId.isPresent()) {
                Application application = byApplicationId.get();
                queryDTO.addQuery("domain", application.getHost());
                queryDTO.addQuery("serverPort", application.getPort());
            }
        }

        List<StatCount> list = new ArrayList<>();
        List<? extends Terms.Bucket> aggs = nginxLogEsClient.aggregateQuery(queryDTO, "statusCode");
        if (aggs != null) {
            for (Terms.Bucket bucket : aggs) {
                if (list.size() == 5) {
                    list.add(new StatCount("其他", Math.toIntExact(bucket.getDocCount())));
                } else if (list.size() == 6) {
                    list.get(5).setCount(list.get(5).getCount() + Math.toIntExact(bucket.getDocCount()));
                } else {
                    list.add(new StatCount(bucket.getKeyAsString(),
                            Math.toIntExact(bucket.getDocCount())));
                }
            }
        }
        return list;
    }


    public List<StatCount> statStatusCode(Collection<String> appIds, LocalDateTime startTime,
                                          LocalDateTime endTime) {

        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime)
                .build()
                .addMultipleQuery("appId", appIds);

        List<StatCount> list = new ArrayList<>();
        List<? extends Terms.Bucket> aggs = nginxLogEsClient.aggregateQuery(queryDTO, "statusCode");
        if (aggs != null) {
            for (Terms.Bucket bucket : aggs) {
                if (list.size() == 5) {
                    list.add(new StatCount("其他", Math.toIntExact(bucket.getDocCount())));
                } else if (list.size() == 6) {
                    list.get(5).setCount(list.get(5).getCount() + Math.toIntExact(bucket.getDocCount()));
                } else {
                    list.add(new StatCount(bucket.getKeyAsString(),
                            Math.toIntExact(bucket.getDocCount())));
                }
            }
        }
        return list;
    }

    private List<StatCount> statDevice(String appId, String apiId, LocalDateTime startTime,
                                       LocalDateTime endTime) {
        List<String> permittedAppIds = getPermittedAppIds(appId);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(10)
                .start(startTime)
                .end(endTime)
                .build()
                .addQuery(StringUtils.isNotBlank(apiId), "apiId", apiId)
                .addMultipleQuery(!permittedAppIds.isEmpty(), "appId", permittedAppIds);
        List<StatCount> list = new ArrayList<>();
        List<? extends Terms.Bucket> aggs = nginxLogEsClient.aggregateQuery(queryDTO, "device");
        if (aggs != null) {
            for (Terms.Bucket bucket : aggs) {
                int count = Math.toIntExact(bucket.getDocCount());
                String deviceName = bucket.getKeyAsString();
                list.add(StatCount.builder().label(deviceName).count(count).build());
            }
        }
        return list;
    }

    private List<StatCount> statDevice(Collection<String> appId, LocalDateTime startTime,
                                       LocalDateTime endTime) {
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(10)
                .start(startTime)
                .end(endTime)
                .build()
                .addMultipleQuery("appId", appId);
        List<StatCount> list = new ArrayList<>();
        List<? extends Terms.Bucket> aggs = nginxLogEsClient.aggregateQuery(queryDTO, "device");
        if (aggs != null) {
            for (Terms.Bucket bucket : aggs) {
                int count = Math.toIntExact(bucket.getDocCount());
                String deviceName = bucket.getKeyAsString();
                list.add(StatCount.builder().label(deviceName).count(count).build());
            }
        }
        return list;
    }

    private List<StatCount> statVisitTrend(String appId, String apiId, LocalDateTime startTime,
                                           LocalDateTime endTime) {
        List<String> permittedAppIds = getPermittedAppIds(appId);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime)
                .build()
                .addQuery(StringUtils.isNotBlank(apiId), "apiId", apiId)
                .addMultipleQuery(!permittedAppIds.isEmpty(), "appId", permittedAppIds);
        List<? extends Histogram.Bucket> aggs = nginxLogEsClient.aggregateByDate(queryDTO);
        // 转换为 Map，以日期为键，文档数量为值
        Map<String, Integer> dateCountMap = aggs == null ? new HashMap<>() :
                aggs.stream()
                        .collect(Collectors.toMap(
                                MultiBucketsAggregation.Bucket::getKeyAsString,
                                bucket -> Math.toIntExact(bucket.getDocCount())
                        ));
        // 日期格式化器
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(Constant.DATE_PATTERN);

        // 从起始日期到结束日期，填充结果
        List<StatCount> resultList = new ArrayList<>();
        LocalDateTime rollTime = startTime;
        while (rollTime.isBefore(endTime)) {
            String formattedDate = rollTime.format(dateFormatter);
            int count = dateCountMap.getOrDefault(formattedDate, 0);
            resultList.add(new StatCount(formattedDate, count));
            rollTime = rollTime.plusDays(1);
        }
        return resultList;
    }

    private List<StatCount> statVisitTrend(Collection<String> appId, LocalDateTime startTime,
                                           LocalDateTime endTime) {
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime)
                .build()
                .addMultipleQuery("appId", appId);
        List<? extends Histogram.Bucket> aggs = nginxLogEsClient.aggregateByDate(queryDTO);
        // 转换为 Map，以日期为键，文档数量为值
        Map<String, Integer> dateCountMap = aggs == null ? new HashMap<>() :
                aggs.stream()
                        .collect(Collectors.toMap(
                                MultiBucketsAggregation.Bucket::getKeyAsString,
                                bucket -> Math.toIntExact(bucket.getDocCount())
                        ));
        // 日期格式化器
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(Constant.DATE_PATTERN);

        // 从起始日期到结束日期，填充结果
        List<StatCount> resultList = new ArrayList<>();
        LocalDateTime rollTime = startTime;
        while (rollTime.isBefore(endTime)) {
            String formattedDate = rollTime.format(dateFormatter);
            int count = dateCountMap.getOrDefault(formattedDate, 0);
            resultList.add(new StatCount(formattedDate, count));
            rollTime = rollTime.plusDays(1);
        }
        return resultList;
    }

    public Pair<List<StatCount>, List<StatCount>> statRegions(String appId, String apiId,
                                                              LocalDateTime startTime,
                                                              LocalDateTime endTime) {
        List<String> permittedAppIds = getPermittedAppIds(appId);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(10)
                .start(startTime)
                .end(endTime)
                .build()
                .addQuery(Objects.nonNull(apiId), "apiId", apiId)
                .addMultipleQuery(!permittedAppIds.isEmpty(), "appId", permittedAppIds);

        List<StatCount> countryList = statCountryIp(queryDTO);
        List<StatCount> cityList = statChinaCityIp(queryDTO);
        return Pair.of(countryList, cityList);
    }

    public List<StatCount> statCountryIp(EsQueryDTO queryDTO) {
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        nativeSearchQuery.addAggregation(AggregationBuilders.nested("clientIpInfo_nested", "clientIpInfo")
                .subAggregation(
                        AggregationBuilders.terms("by_country")
                                .field("clientIpInfo.country")
                                .size(100)
                                .subAggregation(
                                        new ReverseNestedAggregationBuilder("to_root")
                                                .subAggregation(
                                                        AggregationBuilders.cardinality("unique_client_ip")
                                                                .field("clientIp")
                                                )
                                )
                ));

        // 执行查询
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);

        return Optional.ofNullable(searchHits.getAggregations())
                .map(AggregationsContainer::aggregations) // Object
                .map(aggs -> (Aggregations) aggs)
                .map(aggs -> (ParsedNested) aggs.get("clientIpInfo_nested"))
                .map(nested -> (ParsedStringTerms) nested.getAggregations().get("by_country"))
                .map(terms -> terms.getBuckets().stream()
                        .map(bucket -> {
                            String country = IpUtils.getTranslatedRegion(bucket.getKeyAsString());
                            ParsedReverseNested parsedReverseNested = bucket.getAggregations().get("to_root");
                            ParsedCardinality uniqueClientIp = parsedReverseNested.getAggregations().get(
                                    "unique_client_ip");
                            long uniqueClientIpCount = uniqueClientIp.getValue();
                            return new StatCount(country, Math.toIntExact(uniqueClientIpCount));
                        })
                        .sorted(Comparator.comparingLong(StatCount::getCount).reversed())
                        .limit(5)
                        .collect(Collectors.toList())
                )
                .orElse(Collections.emptyList());
    }

    /**
     * 统计城市(中国) top 10
     */
    public List<StatCount> statChinaCityIp(EsQueryDTO queryDTO) {
        // 构建 Aggregations
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        nativeSearchQuery.addAggregation(AggregationBuilders.nested("clientIpInfo_nested", "clientIpInfo")
                .subAggregation(
                        AggregationBuilders.terms("by_city")
                                .field("clientIpInfo.city")
                                .size(100)
                                .subAggregation(
                                        new ReverseNestedAggregationBuilder("to_root")
                                                .subAggregation(
                                                        AggregationBuilders.cardinality("unique_client_ip")
                                                                .field("clientIp")
                                                )
                                )
                ));

        // 执行查询
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);

        return Optional.ofNullable(searchHits.getAggregations())
                .map(AggregationsContainer::aggregations) // Object
                .map(aggs -> (Aggregations) aggs)
                .map(aggs -> (ParsedNested) aggs.get("clientIpInfo_nested"))
                .map(nested -> (ParsedStringTerms) nested.getAggregations().get("by_city"))
                .map(terms -> terms.getBuckets().stream()
                        .map(bucket -> {
                            String chinaCity = IpUtils.getTranslatedRegion(bucket.getKeyAsString());
                            ParsedReverseNested parsedReverseNested = bucket.getAggregations().get("to_root");
                            ParsedCardinality uniqueClientIp = parsedReverseNested.getAggregations().get(
                                    "unique_client_ip");
                            long uniqueClientIpCount = uniqueClientIp.getValue();
                            return new StatCount(chinaCity, Math.toIntExact(uniqueClientIpCount));
                        })
                        .sorted(Comparator.comparingLong(StatCount::getCount).reversed())
                        .limit(5)
                        .collect(Collectors.toList())
                )
                .orElse(Collections.emptyList());
    }


    public Pair<List<StatCount>, List<StatCount>> statRegions(Collection<String> appId,
                                                              LocalDateTime startTime,
                                                              LocalDateTime endTime) {
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(5)
                .start(startTime)
                .end(endTime)
                .build()
                .addMultipleQuery("appId", appId);

        List<StatCount> countryList = statCountryIp(queryDTO);
        List<StatCount> cityList = statChinaCityIp(queryDTO);
        return Pair.of(countryList, cityList);

        // List<StatCount> countryList = new ArrayList<>();
        // List<StatCount> cityList = new ArrayList<>();
        //
        // // // 先按IP聚合,再按国家聚合
        // // TermsAggregationBuilder ipAgg = AggregationBuilders
        // //         .terms("by_ip")
        // //         .field("clientIp")
        // //         .subAggregation(AggregationBuilders
        // //                 .terms("by_country")
        // //                 .field("clientIpInfo.country"));
        //
        // // 内层聚合：对嵌套 国家 聚合
        // TermsAggregationBuilder countryAgg = AggregationBuilders.terms("by_country")
        //         .field("clientIpInfo.country");
        //
        // // 嵌套聚合：进入 comments
        // NestedAggregationBuilder nestedAgg = AggregationBuilders.nested("nested_clientInfo", "clientIpInfo")
        //         .subAggregation(countryAgg);
        //
        // // 外层聚合：按 ip 分组
        // TermsAggregationBuilder ipAgg = AggregationBuilders.terms("by_clientIp")
        //         .field("clientIp")
        //         .subAggregation(nestedAgg);  // 嵌套聚合作为子聚合添加
        //
        // List<? extends Terms.Bucket> ipBuckets = nginxLogEsClient.aggregateQuery(queryDTO, ipAgg, "by_ip");
        //
        // // 统计每个国家的唯一IP数
        // Map<String, Integer> countryCount = new HashMap<>();
        // if (ipBuckets != null) {
        //     for (Terms.Bucket ipBucket : ipBuckets) {
        //         Terms countryTerms = ipBucket.getAggregations().get("by_country");
        //         for (Terms.Bucket countryBucket : countryTerms.getBuckets()) {
        //             String country = countryBucket.getKeyAsString();
        //             countryCount.merge(country, 1, Integer::sum);
        //         }
        //     }
        // }
        //
        // // 转换为StatCount列表并排序取top5
        // countryList = countryCount.entrySet().stream()
        //         .map(entry -> new StatCount(entry.getKey(), entry.getValue()))
        //         .sorted((a, b) -> b.getCount() - a.getCount())
        //         .limit(5)
        //         .collect(Collectors.toList());
        //
        // // 城市统计逻辑类似, 但是只统计中国的城市
        // queryDTO.setQueryCount(10);
        // queryDTO.addQuery("clientIpInfo.country", "中国");
        // // TermsAggregationBuilder ipCityAgg = AggregationBuilders
        // //         .terms("by_ip")
        // //         .field("clientIp")
        // //         .subAggregation(AggregationBuilders
        // //                 .terms("by_city")
        // //                 .field("clientIpInfo.city"));
        //
        // // 内层聚合：对嵌套 城市 聚合
        // TermsAggregationBuilder cityAgg = AggregationBuilders.terms("by_city")
        //         .field("clientIpInfo.city");
        // // 嵌套聚合：进入 comments
        // NestedAggregationBuilder nestedCityAgg = AggregationBuilders.nested("nested_clientInfo", "clientIpInfo")
        //         .subAggregation(cityAgg);
        // // 外层聚合：按 ip 分组
        // TermsAggregationBuilder ipCityAgg = AggregationBuilders.terms("by_clientIp")
        //         .field("clientIp")
        //         .subAggregation(nestedCityAgg);  // 嵌套聚合作为子聚合添加
        //
        // List<? extends Terms.Bucket> ipCityBuckets = nginxLogEsClient.aggregateQuery(queryDTO, ipCityAgg, "by_ip");
        //
        // Map<String, Integer> cityCount = new HashMap<>();
        // if (ipCityBuckets != null) {
        //     for (Terms.Bucket ipBucket : ipCityBuckets) {
        //         Terms cityTerms = ipBucket.getAggregations().get("by_city");
        //         for (Terms.Bucket cityBucket : cityTerms.getBuckets()) {
        //             String city = cityBucket.getKeyAsString();
        //             cityCount.merge(city, 1, Integer::sum);
        //         }
        //     }
        // }
        //
        // cityList = cityCount.entrySet().stream()
        //         .map(entry -> new StatCount(entry.getKey(), entry.getValue()))
        //         .sorted((a, b) -> b.getCount() - a.getCount())
        //         .limit(10)
        //         .collect(Collectors.toList());
        //
        // return Pair.of(countryList, cityList);
    }

    private List<StatCount> statSource(String appId, String apiId, LocalDateTime startTime,
                                       LocalDateTime endTime) {
        List<String> permittedAppIds = getPermittedAppIds(appId);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(10)
                .start(startTime)
                .end(endTime)
                .build()
                .addQuery(StringUtils.isNotBlank(apiId), "apiId", apiId)
                .addMultipleQuery(!permittedAppIds.isEmpty(), "appId", permittedAppIds);
        List<StatCount> list = new ArrayList<>();
        List<? extends Terms.Bucket> aggs = nginxLogEsClient.aggregateQuery(queryDTO, "clientIp");
        if (aggs != null) {
            for (Terms.Bucket bucket : aggs) {
                String clientIp = bucket.getKeyAsString();
                int count = Math.toIntExact(bucket.getDocCount());
                list.add(new StatCount(clientIp, count));
            }
        }
        return list;
    }

    private List<StatCount> statSource(Collection<String> appId, LocalDateTime startTime,
                                       LocalDateTime endTime) {
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(10)
                .start(startTime)
                .end(endTime)
                .build()
                .addMultipleQuery("appId", appId);
        List<StatCount> list = new ArrayList<>();
        List<? extends Terms.Bucket> aggs = nginxLogEsClient.aggregateQuery(queryDTO, "clientIp");
        if (aggs != null) {
            for (Terms.Bucket bucket : aggs) {
                String clientIp = bucket.getKeyAsString();
                int count = Math.toIntExact(bucket.getDocCount());
                list.add(new StatCount(clientIp, count));
            }
        }
        return list;
    }

    /**
     * 更新 appId
     */
    public void updateAppId(Application application) {
        String baseApiAppId = "";
        Optional<Application> appOp = applicationService.getByApplicationId(application.getApplicationId());
        if (!appOp.isPresent()) {
            return;
        }
        application = appOp.get();
        if (application.getType() == ApplicationTypeEnum.BASE_API) {
            baseApiAppId = application.getApplicationId();
        } else {
            Optional<Application> baseApplication =
                    applicationService.getBaseApplication(application.getApplicationId());
            if (baseApplication.isPresent()) {
                baseApiAppId = baseApplication.get().getApplicationId();
            }
        }

        if (StrUtil.isNotBlank(baseApiAppId)) {
            EsQueryDTO queryDTO = EsQueryDTO.builder()
                    .queryCount(5000)
                    .build()
                    .addQuery("domain", application.getHost())
                    .addQuery("serverPort", application.getPort())
                    .addFuzzyQuery("uri", application.getUri() + "*");
            Map<String, Object> updateFields = new HashMap<>();
            updateFields.put("appId", baseApiAppId);
            nginxLogEsClient.updateByQueryId(queryDTO, updateFields);
        }
    }

    public int count(String appId, LocalDateTime startTime, LocalDateTime endTime) {
        List<String> permittedAppIds = getPermittedAppIds(appId);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime)
                .build()
                .addMultipleQuery(!permittedAppIds.isEmpty(), "appId", permittedAppIds);
        return NumberUtil.parseInt(String.valueOf(nginxLogEsClient.queryCount(queryDTO)));
    }

    public long count(EsQueryDTO esQueryDTO) {
        return NumberUtil.parseInt(String.valueOf(nginxLogEsClient.queryCount(esQueryDTO)));
    }

    public List<StatCountResponse> statTrend(String appId, LocalDateTime startTime, LocalDateTime endTime) {
        boolean isLessThanDay = ChronoUnit.HOURS.between(startTime, endTime) <= 24;
        if (!isLessThanDay) {
            startTime = startTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
        } else {
            startTime = startTime.withMinute(0).withSecond(0).withNano(0);
            // 处理时间左闭右开
            endTime = endTime.plusHours(1).withMinute(0).withSecond(0).withNano(0);
        }
        // 时间范围小于等于 24小时?
        // 构建查询对象
        List<String> permittedAppIds = getPermittedAppIds(appId);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime)
                .build()
                .addMultipleQuery(!permittedAppIds.isEmpty(), "appId", permittedAppIds);

        // 获取聚合结果
        List<? extends Histogram.Bucket> aggs = isLessThanDay ? nginxLogEsClient.aggregateByHour(queryDTO) :
                nginxLogEsClient.aggregateByDate(queryDTO);

        // 转换为 Map，以时间为键，文档数量为值
        Map<String, Integer> dateCountMap = aggs == null ? new HashMap<>() :
                aggs.stream()
                        .collect(Collectors.toMap(
                                MultiBucketsAggregation.Bucket::getKeyAsString,
                                bucket -> Math.toIntExact(bucket.getDocCount())
                        ));

        // 时间格式化器
        DateTimeFormatter dateFormatter = isLessThanDay ? DateTimeFormatter.ofPattern(Constant.DATE_TIME_PATTERN) :
                DateTimeFormatter.ofPattern(Constant.DATE_PATTERN);

        // 从起始时间到结束时间，填充结果
        List<StatCountResponse> resultList = new ArrayList<>();

        LocalDateTime rollTime = startTime;
        while (rollTime.isBefore(endTime)) {
            String formattedDate = rollTime.format(dateFormatter);
            int count = dateCountMap.getOrDefault(formattedDate, 0);
            formattedDate = isLessThanDay ? formattedDate.split(" ")[1] : formattedDate.split(" ")[0];
            resultList.add(new StatCountResponse(formattedDate, count));
            rollTime = isLessThanDay ? rollTime.plusHours(1) : rollTime.plusDays(1);
        }

        return resultList;
    }

    public ApiStatResponse statByAppIds(Collection<String> applicationIds) {
        LocalDateTime end = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0).plusDays(1);
        LocalDateTime start = end.minusDays(8);
        ApiStatResponse stat = new ApiStatResponse();
        // *********** 状态码 ***********
        List<StatCount> statStatusCode = statStatusCode(applicationIds, start, end);
        stat.setResultCodes(statStatusCode);
        // // *********** 访问国家/城市分布 ***********
        Pair<List<StatCount>, List<StatCount>> pair = statRegions(applicationIds, start, end);
        stat.setCountries(pair.getLeft());
        stat.setCities(pair.getRight());
        // *********** 访问者设备 ***********
        List<StatCount> statDevices = statDevice(applicationIds, start, end);
        stat.setDevices(statDevices);
        // *********** 访问者来源 ***********
        List<StatCount> statSources = statSource(applicationIds, start, end);
        stat.setSources(statSources);
        // // *********** api 访问量趋势 ***********
        List<StatCount> statVisitTrends = statVisitTrend(applicationIds, start, end);
        stat.setVisitTrends(statVisitTrends);
        return stat;
    }

    public Double queryInterceptAttackPercent() {
        return this.queryInterceptAttackPercent(null);
    }

    public Double queryInterceptAttackPercent(List<String> permissionList) {
        // TODO 到底用不用0点
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusDays(7);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(start)
                .end(end)
                .build()
                .addQuery("requestResourceType", REQUEST_RESOURCE_TYPE_API)
                .addMultipleQuery(CollectionUtils.isNotEmpty(permissionList), "appId", permissionList);

        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();

        // 添加聚合
        TermsAggregationBuilder crsAgg = AggregationBuilders
                .terms("crsReject")
                .field("crsDetectStatus")
                .order(BucketOrder.count(false));

        TermsAggregationBuilder wafAgg = AggregationBuilders
                .terms("wafReject")
                .field("wafDetectStatus")
                .order(BucketOrder.count(false));

        // 添加总数聚合
        ValueCountAggregationBuilder totalAgg = AggregationBuilders
                .count("total")
                .field("_id");

        nativeSearchQuery.addAggregation(crsAgg);
        nativeSearchQuery.addAggregation(wafAgg);
        nativeSearchQuery.addAggregation(totalAgg);
        nativeSearchQuery.setMaxResults(1);

        // 执行查询
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);
        Aggregations aggregations = (Aggregations) Objects.requireNonNull(searchHits.getAggregations())
                .aggregations();

        // 获取结果
        long crsRejectCount = 0L;
        long wafRejectCount = 0L;
        long totalCount = 0L;

        Terms crsTerms = aggregations.get("crsReject");
        Terms wafTerms = aggregations.get("wafReject");
        ValueCount totalCountValue = aggregations.get("total");

        // 获取总数
        totalCount = totalCountValue.getValue();

        // 获取 REJECT 的数量
        crsRejectCount = crsTerms.getBuckets().stream()
                .filter(bucket -> "REJECT".equals(bucket.getKeyAsString()))
                .findFirst()
                .map(Terms.Bucket::getDocCount)
                .orElse(0L);

        wafRejectCount = wafTerms.getBuckets().stream()
                .filter(bucket -> "REJECT".equals(bucket.getKeyAsString()))
                .findFirst()
                .map(Terms.Bucket::getDocCount)
                .orElse(0L);

        double interceptAttackPercent = totalCount == 0 ? 0d :
                (double) (crsRejectCount + wafRejectCount) / totalCount;
        interceptAttackPercent = Math.round(interceptAttackPercent * 10000d) / 10000d;
        return interceptAttackPercent;
    }

    public List<StatCountResponse> statAttackTrend(String appId, LocalDateTime startTime, LocalDateTime endTime) {
        // 构建查询对象
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime)
                .queryCount(10000)
                .build()
                .addQuery("appId", appId)
                .addExistQuery("riskRules");

        // 获取聚合结果
        List<? extends Histogram.Bucket> aggs = nginxLogEsClient.aggregateByDate(queryDTO);

        // 转换为 Map，以日期为键，文档数量为值
        Map<String, Integer> dateCountMap = aggs == null ? new HashMap<>() :
                aggs.stream()
                        .collect(Collectors.toMap(
                                MultiBucketsAggregation.Bucket::getKeyAsString,
                                bucket -> Math.toIntExact(bucket.getDocCount())
                        ));

        // 日期格式化器
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(Constant.DATE_PATTERN);

        // 从起始日期到结束日期，填充结果
        List<StatCountResponse> resultList = new ArrayList<>();
        LocalDateTime rollTime = startTime;
        while (rollTime.isBefore(endTime)) {
            String formattedDate = rollTime.format(dateFormatter);
            int count = dateCountMap.getOrDefault(formattedDate, 0);
            resultList.add(new StatCountResponse(formattedDate, count));
            rollTime = rollTime.plusDays(1);
        }

        return resultList;
    }

    /**
     * 分组统计, 组和个数
     *
     * @param queryDTO   查询条件
     * @param groupField 分组域
     */
    public List<StatCount> groupCount(EsQueryDTO queryDTO, String groupField) {
        List<? extends Terms.Bucket> buckets = nginxLogEsClient.aggregateQuery(queryDTO, groupField);
        if (buckets == null) {
            return Collections.emptyList();
        }

        List<StatCount> statCounts = new ArrayList<>();

        for (Terms.Bucket bucket : buckets) {
            statCounts.add(new StatCount(bucket.getKeyAsString(), Math.toIntExact(bucket.getDocCount())));
        }
        return statCounts;
    }

    /**
     * 按天统计数据
     */
    public List<StatCount> groupByDate(EsQueryDTO queryDTO) {
        // 获取聚合结果
        List<? extends Histogram.Bucket> aggs = nginxLogEsClient.aggregateByDate(queryDTO);

        // 转换为 Map，以日期为键，文档数量为值
        Map<String, Integer> dateCountMap = aggs == null ? new HashMap<>() :
                aggs.stream()
                        .collect(Collectors.toMap(
                                MultiBucketsAggregation.Bucket::getKeyAsString,
                                bucket -> Math.toIntExact(bucket.getDocCount())
                        ));
        // 日期格式化器
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(Constant.DATE_PATTERN);

        // 从起始日期到结束日期，填充结果

        List<StatCount> trendList = new ArrayList<>();
        LocalDateTime rollTime = queryDTO.getStart();
        while (rollTime.isBefore(queryDTO.getEnd())) {
            String formattedDate = rollTime.format(dateFormatter);
            int count = dateCountMap.getOrDefault(formattedDate, 0);
            trendList.add(new StatCount(formattedDate, count));
            rollTime = rollTime.plusDays(1);
        }
        return trendList;
    }

    public int statVisitCount(String appId, String apiId, LocalDateTime startTime, LocalDateTime endTime) {
        List<String> permittedAppIds = getPermittedAppIds(appId);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime)
                .queryCount(10000)
                .build()
                .addMultipleQuery(!permittedAppIds.isEmpty(), "appId", permittedAppIds)
                .addQuery(StrUtil.isNotBlank(apiId), "apiId", apiId);
        return groupCount(queryDTO, "clientIp").size();
    }

    /**
     * 地理位置聚合统计
     *
     * @param queryDTO 查询条件
     * @return 地理位置统计结果列表
     */
    public List<Map<String, Object>> groupGeoCount(EsQueryDTO queryDTO) {
        // 构建复合聚合
        // TermsAggregationBuilder cityAgg = AggregationBuilders
        //         .terms("by_city")
        //         .field("clientIpInfo.city")
        //         .size(1000)
        //         .subAggregation(AggregationBuilders
        //                 .geoBounds("location_bounds")
        //                 .field("clientIpInfo.location"))
        //         .subAggregation(AggregationBuilders
        //                 .geoCentroid("location_centroid")
        //                 .field("clientIpInfo.location"));

        // 1. geo_bounds 聚合
        GeoBoundsAggregationBuilder geoBoundsAgg = AggregationBuilders
                .geoBounds("location_bounds")
                .field("clientIpInfo.location");
        // 2. geo_centroid 聚合
        GeoCentroidAggregationBuilder geoCentroidAgg = AggregationBuilders
                .geoCentroid("location_centroid")
                .field("clientIpInfo.location");
        // 3. terms 聚合：按 city 分桶
        TermsAggregationBuilder cityAgg = AggregationBuilders
                .terms("by_city")
                .field("clientIpInfo.city")
                .size(1000)
                .subAggregation(geoBoundsAgg)
                .subAggregation(geoCentroidAgg);
        // 4. 外层 nested 聚合
        NestedAggregationBuilder nestedCityAgg = AggregationBuilders
                .nested("clientIpInfo_nested", "clientIpInfo")
                .subAggregation(cityAgg);

        // 执行聚合查询
        // List<? extends Terms.Bucket> cityBuckets = nginxLogEsClient.aggregateQuery(queryDTO, nestedCityAgg,
        // "by_city");
        List<? extends Terms.Bucket> cityBuckets;

        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        nativeSearchQuery.addAggregation(nestedCityAgg);
        nativeSearchQuery.setMaxResults(1);
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);
        if (searchHits.hasAggregations()) {
            AggregationsContainer<?> container = searchHits.getAggregations();
            Aggregations aggregations = (Aggregations) container.aggregations();
            // 解析聚合结果
            ParsedNested nested = aggregations.get("clientIpInfo_nested");
            ParsedTerms parsedTerms = nested.getAggregations().get("by_city");
            cityBuckets = parsedTerms.getBuckets();
        } else {
            return Collections.emptyList();
        }

        if (cityBuckets == null) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> result = new ArrayList<>();

        // 处理聚合结果
        for (Terms.Bucket cityBucket : cityBuckets) {
            String city = cityBucket.getKeyAsString();
            long count = cityBucket.getDocCount();

            // 获取地理位置中心点
            GeoCentroid centroid = cityBucket.getAggregations().get("location_centroid");
            GeoPoint centerPoint = centroid.centroid();

            // 获取地理边界
            GeoBounds bounds = cityBucket.getAggregations().get("location_bounds");

            // 只添加有完整地理信息的记录
            if (StrUtil.isNotBlank(city) && centerPoint != null) {
                Map<String, Object> geoInfo = new HashMap<>();
                geoInfo.put("city", city);
                geoInfo.put("longitude", centerPoint.getLon());
                geoInfo.put("latitude", centerPoint.getLat());
                geoInfo.put("count", count);

                // 可选：如果需要边界信息
                if (bounds != null && bounds.topLeft() != null && bounds.bottomRight() != null) {
                    Map<String, Object> boundsInfo = new HashMap<>();

                    Map<String, Object> topLeft = new HashMap<>();
                    topLeft.put("lat", bounds.topLeft().getLat());
                    topLeft.put("lon", bounds.topLeft().getLon());

                    Map<String, Object> bottomRight = new HashMap<>();
                    bottomRight.put("lat", bounds.bottomRight().getLat());
                    bottomRight.put("lon", bounds.bottomRight().getLon());

                    boundsInfo.put("top_left", topLeft);
                    boundsInfo.put("bottom_right", bottomRight);
                    geoInfo.put("bounds", boundsInfo);
                }

                result.add(geoInfo);
            }
        }

        return result;
    }

    private List<String> getPermittedAppIds(String appId) {
        if (StrUtil.isBlank(appId)) {
            return Collections.emptyList();
        }
        List<String> permittedAppIds = new ArrayList<>();
        if ("all".equalsIgnoreCase(appId)) {
            permittedAppIds = StpUtil.getPermissionList();
        } else {
            boolean validPermission = true;
            permittedAppIds = applicationService.getIdsWithChildren(Collections.singletonList(appId), validPermission);
        }
        if (permittedAppIds.isEmpty()) {
            permittedAppIds.add("-1");
        }
        return permittedAppIds;
    }

    public void deleteByApiId(String apiId) {
        if (StrUtil.isBlank(apiId)) return;

        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(10000)
                .build()
                .addQuery("apiId", apiId);
        long deleted = nginxLogEsClient.deleteByQuery(queryDTO);
        log.info("[API][DELETE] {}:{}", apiId, deleted);
    }
    /**
     * 根据应用ID删除访问日志
     */
    public void deleteByAppId(String appId) {
        if (StrUtil.isBlank(appId)) return;

        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(10000)
                .build()
                .addQuery("appId", appId);
        nginxLogEsClient.deleteByQuery(queryDTO);
    }

}
