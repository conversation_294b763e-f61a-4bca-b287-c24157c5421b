package com.telecom.apigateway.service.impl;

import com.telecom.apigateway.model.dto.NginxErrorLogDTO;
import com.telecom.apigateway.service.LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024-08-13
 */
@Slf4j
@Service
public class NginxErrorLogService implements LogService {

    /**
     * 解析错误日志
     */
    private static final Pattern LOG_PATTERN = Pattern.compile(
            "(\\d{4}/\\d{2}/\\d{2} \\d{2}:\\d{2}:\\d{2}) \\[(\\w+)] (\\d+)#(\\d+): \\*\\d+ (.+?), client: (.+?), " +
                    "server: (.+?), request: \"(.+?)\", host: \"(.+?)\"");

    public List<NginxErrorLogDTO> queryErrorLog(String uri, Integer range, Integer number) {
        return null;
    }

    private NginxErrorLogDTO parseLog(String logStr) {
        Matcher matcher = LOG_PATTERN.matcher(logStr);
        if (matcher.matches()) {
            NginxErrorLogDTO entry = new NginxErrorLogDTO();
            entry.setTime(matcher.group(1));
            entry.setLevel(matcher.group(2));
            // entry.setProcessId(Integer.parseInt(matcher.group(3)));
            // entry.setThreadId(Integer.parseInt(matcher.group(4)));
            entry.setDesc(matcher.group(5));
            entry.setClientIp(matcher.group(6));
            // entry.(matcher.group(7));
            entry.setRequestInfo(matcher.group(8));
            // entry.setHost(matcher.group(9));
            return entry;
        } else {
            log.error("日志解析失败:{}", log);
            throw new IllegalArgumentException("Log entry does not match expected format");
        }
    }

}
