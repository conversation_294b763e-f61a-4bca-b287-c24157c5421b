package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.RegionMapper;
import com.telecom.apigateway.model.entity.Region;
import com.telecom.apigateway.service.RegionService;
import org.springframework.stereotype.Service;

@Service
public class RegionServiceImpl extends ServiceImpl<RegionMapper, Region> implements RegionService {
    @Override
    public Region getAreaByCode(String areaCode) {
        return this.lambdaQuery()
                .eq(Region::getCode, areaCode)
                .one();
    }
}
