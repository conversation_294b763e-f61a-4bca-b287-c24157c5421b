package com.telecom.apigateway.service.impl;

import cloud.tianai.captcha.common.util.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.RiskLogNewMapper;
import com.telecom.apigateway.model.entity.RiskLogNew;
import com.telecom.apigateway.service.RiskLogNewService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@Service
public class RiskLogNewServiceImpl extends ServiceImpl<RiskLogNewMapper, RiskLogNew> implements RiskLogNewService {
    @Override
    public List<RiskLogNew> queryLast7DaysRisk(List<String> distinctApplicationIds) {
        return this.lambdaQuery()
                .in(RiskLogNew::getAppId, distinctApplicationIds)
                .ge(RiskLogNew::getLogTime, LocalDateTime.now().minusDays(7))
                .list();
    }

    @Override
    public RiskLogNew getByLogId(String riskId) {
        return this.lambdaQuery()
                .eq(RiskLogNew::getLogId, riskId)
                .one();
    }

    @Override
    public boolean deleteByApiId(String apiId) {
        return lambdaUpdate().eq(RiskLogNew::getApiId, apiId).remove();
    }

    @Override
    public void deleteByAppId(Collection<String> applicationId) {
        if (CollectionUtils.isEmpty(applicationId)) return;
        lambdaUpdate().in(RiskLogNew::getAppId, applicationId).remove();
    }
}
