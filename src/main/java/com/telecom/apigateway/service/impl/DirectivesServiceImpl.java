package com.telecom.apigateway.service.impl;

import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.dto.DirectiveResultDTO;
import com.telecom.apigateway.model.dto.ValidateDirectiveDTO;
import com.telecom.apigateway.model.vo.request.CustomRuleConfigRequest;
import com.telecom.apigateway.model.vo.request.OwaspRuleConfigRequest;
import com.telecom.apigateway.model.vo.request.ValidateDirectivesRequest;
import com.telecom.apigateway.service.DirectivesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class DirectivesServiceImpl implements DirectivesService {

    private final RestTemplate restTemplate;

    @Value("${crs.url}")
    private String baseUrl;

    @Override
    public ValidateDirectiveDTO validateDirectives(ValidateDirectivesRequest request) {
        String url = baseUrl + "/api/directives/validate";
        try {
            ResponseEntity<DirectiveResultDTO<ValidateDirectiveDTO>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    new HttpEntity<>(request),
                    new ParameterizedTypeReference<DirectiveResultDTO<ValidateDirectiveDTO>>() {}
            );
            if (Objects.requireNonNull(response.getBody()).getCode() != 200) {
                throw new BusinessException("验证规则内容失败");
            }
            ValidateDirectiveDTO data = response.getBody().getData();
            data.setDirective(request.getDirectives());
            return data;
        } catch (Exception e) {
            log.error("验证规则内容失败: {}, 请求地址: {}", e.getMessage(), url);
            throw e;
        }
    }

    @Override
    public void configOwaspRule(OwaspRuleConfigRequest request) {
        String url = baseUrl + "/api/directives/config/owasp";
        try {
            ResponseEntity<DirectiveResultDTO<ValidateDirectiveDTO>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    new HttpEntity<>(request),
                    new ParameterizedTypeReference<DirectiveResultDTO<ValidateDirectiveDTO>>() {}
            );
            if (Objects.requireNonNull(response.getBody()).getCode() != 200) {
                throw new BusinessException("配置OWASP规则失败");
            }
        } catch (Exception e) {
            log.error("配置OWASP规则失败: {}, 请求地址: {}", e.getMessage(), url);
            throw new BusinessException("配置OWASP规则失败");
        }
    }

    @Override
    public void configCustomRule(CustomRuleConfigRequest request) {
        String url = baseUrl + "/api/directives/config/custom";
        try {
            ResponseEntity<DirectiveResultDTO<ValidateDirectiveDTO>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    new HttpEntity<>(request),
                    new ParameterizedTypeReference<DirectiveResultDTO<ValidateDirectiveDTO>>() {}
            );
            if (Objects.requireNonNull(response.getBody()).getCode() != 200) {
                throw new BusinessException("配置自定义规则失败");
            }
        } catch (Exception e) {
            log.error("配置自定义规则失败: {}, 请求地址: {}", e.getMessage(), url);
            throw new BusinessException("配置自定义规则失败");
        }
    }
} 