package com.telecom.apigateway.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.*;
import com.telecom.apigateway.model.enums.*;
import com.telecom.apigateway.model.vo.request.BasePageQueryRequest;
import com.telecom.apigateway.model.vo.request.RoleRequest;
import com.telecom.apigateway.model.vo.request.UpdateRoleRequest;
import com.telecom.apigateway.model.vo.response.MenuPermissionResponse;
import com.telecom.apigateway.model.vo.response.RoleDetailResponse;
import com.telecom.apigateway.model.vo.response.RoleResponse;
import com.telecom.apigateway.service.*;
import com.telecom.apigateway.utils.MapUtil;
import com.telecom.apigateway.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RoleBusinessService {
    private final RoleService roleService;
    private final UserRoleService userRoleService;
    private final MenuPermissionService menuPermissionService;
    private final UserInfoService userInfoService;
    private final ApplicationService applicationService;

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.ROLE_MANAGEMENT,
            spelArgs = {"#{#request.name}"}
    )
    public void createRole(RoleRequest request) {
        if (roleService.nameExist(request.getName(), null)) {
            throw new BusinessException("角色名称已存在");
        }

        List<String> dataPermission = request.getDataPermission();
        List<String> menuPermission = request.getMenuPermission();
        Role role = new Role(request.getName(), menuPermission.toArray(new String[0]), dataPermission.toArray(new String[0]));
        roleService.save(role);
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.FULL_UPDATE,
            resourceType = ResourceTypeEnum.ROLE_MANAGEMENT,
            spelArgs = {"#{#request.roleId}", "#{#result}"}
    )
    public Role updateRole(UpdateRoleRequest request) {
        Role role = getRole(request.getRoleId());

        if (roleService.nameExist(request.getName(), request.getRoleId())) {
            throw new BusinessException("角色名称已存在");
        }

        List<String> menuPermission = request.getMenuPermission();
        List<String> dataPermission = request.getDataPermission();
        role.update(request.getName(), menuPermission.toArray(new String[0]), dataPermission.toArray(new String[0]));
        roleService.updateById(role);
        return role;
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.ROLE_MANAGEMENT,
            spelArgs = {"#{#roleId}"}
    )
    public void deleteRole(String roleId) {
        Role role = getRole(roleId);
        // check users
        if (hasUsers(roleId)) {
            throw new BusinessException("该角色下存在用户，无法删除");
        }

        // logic delete role
        role.delete();
        roleService.updateById(role);
    }

    public Page<RoleResponse> pageRoles(BasePageQueryRequest request) {
        Page<Role> rolePage = roleService.pageRoles(Page.of(request.getPageNum(), request.getPageSize()));
        List<Role> roles = rolePage.getRecords();
        if (roles.isEmpty()) {
            return new Page<>(request.getPageNum(), request.getPageSize());
        }
        List<String> roleIds = roles.stream().map(Role::getRoleId).collect(Collectors.toList());
        List<UserRole> userRoles = userRoleService.listByRoleIds(roleIds);
        Map<String, List<UserRole>> userRoleGroup = MapUtil.grouping(UserRole::getRoleId, userRoles);
        Set<String> permissionCodes = roles.stream().map(Role::getMenuPermissions).flatMap(Arrays::stream).collect(Collectors.toSet());
        Set<String> applicationIds = roles.stream().map(Role::getApplicationPermissions).flatMap(Arrays::stream).collect(Collectors.toSet());
        List<MenuPermission> menuPermissions = menuPermissionService.getByCodes(permissionCodes);
        List<Application> applications = applicationService.getApplications(applicationIds);
        Map<String, MenuPermission> menuPermissionMap = MapUtil.toMapByUnionParams(MenuPermission::getCode, menuPermissions);
        Map<String, Application> applicationMap = MapUtil.toMapByUnionParams(Application::getApplicationId, applications);
        Set<String> updateUsernames = roles.stream().map(Role::getUpdateUser).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        Map<String, UserInfo> userInfoMap = MapUtil.toMapByUnionParams(UserInfo::getUsername, userInfoService.listByUsernames(updateUsernames));
        List<RoleResponse> responses = roles.stream().map((role) -> {
            String roleId = role.getRoleId();
            List<UserRole> users = userRoleGroup.getOrDefault(roleId, Collections.emptyList());
            StringBuilder menuPermissionContent = new StringBuilder();
            StringBuilder dataPermissionContent = new StringBuilder();
            for (int i = 0; i < (Math.min(role.getMenuPermissions().length, 2)); i++) {
                String name = menuPermissionMap.getOrDefault(role.getMenuPermissions()[i], new MenuPermission()).getName();
                menuPermissionContent.append(name);
            }
            if (StringUtils.isNotBlank(menuPermissionContent)) {
                menuPermissionContent.append(" 等");
                menuPermissionContent.append(role.getMenuPermissions().length);
                menuPermissionContent.append("个页面");
            } else {
                menuPermissionContent.append("无页面权限");
            }
            for (int i = 0; i < (Math.min(role.getApplicationPermissions().length, 2)); i++) {
                String applicationName = Optional.ofNullable(applicationMap.get(role.getApplicationPermissions()[i])).map(Application::getName).orElse("");
                dataPermissionContent.append(applicationName);
            }
            if (StringUtils.isNotBlank(dataPermissionContent)) {
                dataPermissionContent.append(" 等");
                dataPermissionContent.append(role.getApplicationPermissions().length);
                dataPermissionContent.append("个应用");
            } else {
                dataPermissionContent.append("无应用权限");
            }
            RoleResponse roleResponse = new RoleResponse();
            roleResponse.setRoleId(roleId);
            roleResponse.setName(role.getName());
            roleResponse.setMenuPermissionContent("admin".equals(roleId) ? "管理员拥有所有页面权限" : menuPermissionContent.toString());
            roleResponse.setDataPermissionContent("admin".equals(roleId) ? "管理员拥有所有应用权限" : dataPermissionContent.toString());
            roleResponse.setUserCount(users.size());
            roleResponse.setUpdateUser(Optional.ofNullable(userInfoMap.get(role.getUpdateUser())).map(UserInfo::getRealName).orElse(""));
            roleResponse.setUpdateTime(role.getUpdateTime());
            return roleResponse;
        }).collect(Collectors.toList());

        return PageUtils.convertPage(rolePage, responses);
    }

    public List<Role> listByRoleIds(Collection<String> roleIds) {
        if (roleIds.isEmpty()) {
            return Collections.emptyList();
        }
        return roleService.lambdaQuery()
                .in(Role::getRoleId, roleIds)
                .eq(Role::getDeleted, false)
                .list();
    }

    private boolean hasUsers(String roleId) {
        return !userRoleService.listByRoleId(roleId).isEmpty();
    }

    public List<MenuPermissionResponse> getMenuPermissions() {
        List<MenuPermission> menuPermissions = menuPermissionService.getDisplayPermissionsByRoleId();
        Map<String, List<MenuPermission>> grouping = MapUtil.grouping(MenuPermission::getCategory, menuPermissions);
        return grouping.keySet().stream().map(menuPermission -> {
                    List<MenuPermission> subMenu = grouping.get(menuPermission);
                    List<MenuPermissionResponse.CategoryItem> subMenus = subMenu.stream().map((item) -> {
                                MenuPermissionResponse.CategoryItem categoryItem = new MenuPermissionResponse.CategoryItem();
                                categoryItem.setPermissionId(item.getCode());
                                categoryItem.setPermissionName(item.getName());
                                categoryItem.setSort(item.getMenuSort());
                                return categoryItem;
                            }).sorted(Comparator.comparing(MenuPermissionResponse.CategoryItem::getSort))
                            .collect(Collectors.toList());
                    MenuPermissionResponse response = new MenuPermissionResponse();
                    response.setCategory(subMenu.get(0).getCategory());
                    response.setSort(subMenu.get(0).getCategorySort());
                    response.setSubMenus(subMenus);
                    return response;
                }).sorted(Comparator.comparing(MenuPermissionResponse::getSort))
                .collect(Collectors.toList());
    }

    private Role getRole(String roleId) {
        Optional<Role> roleOpt = roleService.optByRoleId(roleId);
        if (!roleOpt.isPresent()) {
            throw new BusinessException("角色不存在");
        }
        return roleOpt.get();
    }

    public RoleDetailResponse getDetail(String roleId) {
        Role role = getRole(roleId);
        RoleDetailResponse roleDetailResponse = new RoleDetailResponse();
        roleDetailResponse.setRoleId(role.getRoleId());
        roleDetailResponse.setName(role.getName());
        roleDetailResponse.setMenuPermissions(Arrays.asList(role.getMenuPermissions()));
        roleDetailResponse.setDataPermissions(Arrays.asList(role.getApplicationPermissions()));
        return roleDetailResponse;
    }
}