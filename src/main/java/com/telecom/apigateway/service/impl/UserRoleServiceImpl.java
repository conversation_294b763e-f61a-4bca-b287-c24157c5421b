package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.UserRoleMapper;
import com.telecom.apigateway.model.entity.UserRole;
import com.telecom.apigateway.service.UserRoleService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements UserRoleService {
    @Override
    public List<UserRole> listByUsername(String username) {
        return this.lambdaQuery()
                .eq(UserRole::getUsername, username)
                .eq(UserRole::getDeleted, false)
                .list();
    }

    @Override
    public List<UserRole> listByRoleIds(List<String> roleIds) {
        if (roleIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(UserRole::getRoleId, roleIds)
                .eq(UserRole::getDeleted, false)
                .list();
    }

    @Override
    public List<UserRole> listByRoleId(String roleId) {
        return this.lambdaQuery()
                .eq(UserRole::getRoleId, roleId)
                .eq(UserRole::getDeleted, false)
                .list();
    }

    @Override
    public void deleteByUsername(String username) {
        this.lambdaUpdate()
                .eq(UserRole::getUsername, username)
                .set(UserRole::getDeleted, true)
                .update();
    }
}
