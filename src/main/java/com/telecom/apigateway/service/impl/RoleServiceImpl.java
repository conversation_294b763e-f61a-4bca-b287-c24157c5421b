package com.telecom.apigateway.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.mapper.RoleMapper;
import com.telecom.apigateway.model.entity.Role;
import com.telecom.apigateway.model.entity.RolePermission;
import com.telecom.apigateway.model.vo.request.RoleRequest;
import com.telecom.apigateway.service.RoleService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    @Override
    public Page<Role> pageRoles(Page<Role> page) {
        return this.lambdaQuery()
                .eq(Role::getDeleted, false)
                .orderByDesc(Role::getUpdateTime)
                .page(page);
    }

    @Override
    public List<Role> listByRoleIds(Collection<String> roleIds) {
        if (roleIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(Role::getRoleId, roleIds)
                .eq(Role::getDeleted, false)
                .list();
    }

    @Override
    public Optional<Role> optByRoleId(String roleId) {
        return this.lambdaQuery()
                .eq(Role::getRoleId, roleId)
                .eq(Role::getDeleted, false)
                .oneOpt();
    }

    @Override
    public boolean nameExist(String roleName, String roleId) {
        return this.lambdaQuery()
                .ne(StringUtils.isNotBlank(roleId), Role::getRoleId, roleId)
                .eq(Role::getName, roleName)
                .eq(Role::getDeleted, false)
                .exists();
    }

    private boolean hasUsers(Long roleId) {
        return lambdaQuery()
                .eq(Role::getId, roleId)
                .eq(Role::getDeleted, false)
                .exists();
    }

    @Override
    public List<Role> list() {
        return this.lambdaQuery()
                .eq(Role::getDeleted, false)
                .list();
    }
}