package com.telecom.apigateway.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.UserMapper;
import com.telecom.apigateway.model.entity.User;
import com.telecom.apigateway.model.vo.request.QueryUserRequest;
import com.telecom.apigateway.model.vo.response.QueryUserResponse;
import com.telecom.apigateway.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    @Override
    public Optional<User> getByUsername(String username) {
        return this.lambdaQuery()
                .eq(User::getUsername, username)
                .eq(User::getDeleted, false)
                .oneOpt();
    }

    @Override
    public Optional<User> getByUsernameOrPhone(String usernameOrPhone) {
        return this.lambdaQuery()
                .and(i -> i.eq(User::getUsername, usernameOrPhone).or().eq(User::getPhone, usernameOrPhone))
                .eq(User::getDeleted, false)
                .oneOpt();
    }

    @Override
    public boolean checkUsername(String username) {
        return this.lambdaQuery()
                .eq(User::getUsername, username)
                .eq(User::getDeleted, false)
                .exists();
    }

    @Override
    public boolean checkUsernameAndPhone(String username, String phone) {
        return this.lambdaQuery()
                .and(query -> query
                        .or(i -> i.eq(User::getUsername, username))
                        .or(i -> i.eq(User::getPhone, phone))
                )
                .eq(User::getDeleted, false)
                .exists();
    }

    @Override
    public boolean codePasswordAndSave(User user) {
        String pwd = BCrypt.hashpw(user.getPassword());
        user.setPassword(pwd);
        return this.save(user);
    }

    @Override
    public boolean update(User user) {
        String pwd = BCrypt.hashpw(user.getPassword());
        user.setPassword(pwd);
        return this.updateById(user);
    }

    @Override
    public List<User> list() {
        return baseQuery().list();
    }

    @Override
    public Page<QueryUserResponse> queryUsers(Page<?> page, QueryUserRequest request) {
        return baseMapper.queryUsers(page, request);
    }

    @Override
    public List<String> queryAllDepartments() {
        return baseMapper.queryAllDepartments();
    }

    private LambdaQueryChainWrapper<User> baseQuery() {
        return this.lambdaQuery()
                .eq(User::getDeleted, false);
    }
}
