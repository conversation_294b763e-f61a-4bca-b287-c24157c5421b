package com.telecom.apigateway.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.common.DeleteLogAnnotation;
import com.telecom.apigateway.common.InsertLogAnnotation;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.UpdateLogAnnotation;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.mapper.ApplicationCorrectPolicyMapper;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.dto.PolicyConditionDTO;
import com.telecom.apigateway.model.dto.UrlEndpoint;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.ApplicationCorrectPolicy;
import com.telecom.apigateway.model.entity.RiskLogNew;
import com.telecom.apigateway.model.enums.*;
import com.telecom.apigateway.model.vo.request.AddApplicationCorrectPolicyRequest;
import com.telecom.apigateway.model.vo.request.ApplicationCorrectPolicyQueryRequest;
import com.telecom.apigateway.model.vo.request.PolicyAuditRequest;
import com.telecom.apigateway.model.vo.response.ApplicationCorrectPolicyResponse;
import com.telecom.apigateway.model.vo.response.PolicyAuditResponse;
import com.telecom.apigateway.service.ApiInfoService;
import com.telecom.apigateway.service.ApplicationCorrectPolicyService;
import com.telecom.apigateway.service.ApplicationService;
import com.telecom.apigateway.service.RiskLogNewService;
import com.telecom.apigateway.service.SensitiveLogService;
import com.telecom.apigateway.service.UserInfoService;
import com.telecom.apigateway.utils.ApiUrlUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 应用修正策略服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApplicationCorrectPolicyServiceImpl extends ServiceImpl<ApplicationCorrectPolicyMapper, ApplicationCorrectPolicy>
        implements ApplicationCorrectPolicyService {

    private final ApplicationService applicationService;
    private final ApiInfoService apiInfoService;
    private final UserInfoService userInfoService;
    private final NginxLogEsClient nginxLogEsClient;
    private final RiskLogNewService riskLogNewService;
    private final SensitiveLogService sensitiveLogService;

    @Override
    @InsertLogAnnotation(
            resourceType = ResourceTypeEnum.APPLICATION_MERGE,
            displayName = "#request.policyName"
    )
    public String addPolicy(String userId, AddApplicationCorrectPolicyRequest request) {
        validPolicyNameExisted(request.getPolicyName(), false, null);
        validatePolicyConditions(request);

        String policyId = IdUtil.fastSimpleUUID();

        ApplicationCorrectPolicy policy = genarateApplicationCorrectPolicy(userId, request, policyId);

        this.save(policy);
        return policyId;
    }

    @Override
    public Page<ApplicationCorrectPolicyResponse> queryPage(ApplicationCorrectPolicyQueryRequest request) {
        Page<ApplicationCorrectPolicyResponse> page = new Page<>(request.getPageNum(), request.getPageSize());

        String status = Optional.ofNullable(request.getStatus()).map(CorrectPolicyStatusEnum::getCode).orElse(null);
        String action = Optional.ofNullable(request.getAction()).map(CorrectPolicyActionEnum::getCode).orElse(null);

        Page<ApplicationCorrectPolicyResponse> pages = this.baseMapper.queryPage(page, request.getPolicyName(), status, action);
        List<ApplicationCorrectPolicyResponse> records = pages.getRecords();
        records.forEach((record) -> {
            String conditionText = record.getConditions()
                    .stream()
                    .map(PolicyConditionDTO::getFullUrl)
                    .collect(Collectors.joining(", "));
            record.setConditionText(conditionText);
        });
        return pages;
    }

    @Override
    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.APPLICATION_MERGE,
            description = "{resourceName}: 启用策略 #{@applicationCorrectPolicyServiceImpl.getPolicyName(#policyId)}"
    )
    public void enablePolicy(String userId, String policyId) {
        ApplicationCorrectPolicy policy = getPolicyByIdAndValidate(policyId);
        policy.enable();
        executePolicy(policy);
        this.updateById(policy);
    }

    @Override
    @Transactional
    public void batchEnablePolicy(String userId, List<String> policyIds) {
        policyIds.forEach(policyId -> enablePolicy(userId, policyId));
    }

    @Override
    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.APPLICATION_MERGE,
            description = "{resourceName}: 禁用策略 #{@applicationCorrectPolicyServiceImpl.getPolicyName(#policyId)}"
    )
    public void disablePolicy(String userId, String policyId) {
        ApplicationCorrectPolicy policy = getPolicyByIdAndValidate(policyId);
        policy.disable();
        this.updateById(policy);

//        rollbackPolicy(policy);
    }

    @Override
    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.APPLICATION_MERGE,
            description = "{resourceName}: 批量禁用策略 #{@applicationCorrectPolicyServiceImpl.getPolicyNames(#policyIds)}"
    )
    public void batchDisablePolicy(String userId, List<String> policyIds) {
        policyIds.forEach(policyId -> disablePolicy(userId, policyId));
    }

    @Override
    @Transactional
    @DeleteLogAnnotation(
            resourceType = ResourceTypeEnum.APPLICATION_MERGE,
            displayName = "@applicationCorrectPolicyServiceImpl.getPolicyName(#policyId)"
    )
    public void deletePolicy(String userId, String policyId) {
        ApplicationCorrectPolicy policy = getPolicyByIdAndValidate(policyId);

        // 启用过的策略禁止删除
        if (CorrectPolicyStatusEnum.ENABLED.equals(policy.getStatus()) || policy.getEverEnabled()) {
            throw new BusinessException("启用过的策略不允许删除");
        }

        policy.delete();
        this.updateById(policy);
    }

    @Override
    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.APPLICATION_MERGE,
            description = "{resourceName}: 批量删除策略 #{@applicationCorrectPolicyServiceImpl.getPolicyNames(#policyIds)}"
    )
    public void batchDeletePolicy(String userId, List<String> policyIds) {
        policyIds.forEach(policyId -> deletePolicy(userId, policyId));
    }

    @Override
    public ApplicationCorrectPolicyResponse getPolicyDetail(String policyId) {
        ApplicationCorrectPolicy policy = getPolicyByIdAndValidate(policyId);
        Optional<Application> optApp = applicationService.getByApplicationId(policy.getRelateAppId());
        return optApp.map(application -> new ApplicationCorrectPolicyResponse(policy, application))
                .orElseGet(() -> new ApplicationCorrectPolicyResponse(policy));
    }

    @Override
    @Transactional
    @UpdateLogAnnotation(
            resourceType = ResourceTypeEnum.APPLICATION_MERGE,
            displayName = "#request.policyName",
            queryMethod = "@applicationCorrectPolicyServiceImpl.getPolicyDetail(#policyId)"
    )
    public void updatePolicy(String userId, String policyId, AddApplicationCorrectPolicyRequest request) {
        ApplicationCorrectPolicy policy = getPolicyByIdAndValidate(policyId);

        // 如果策略曾经启用过，不允许编辑
        if (policy.getEverEnabled()) {
            throw new BusinessException("策略曾经启用过，不允许编辑");
        }
        validPolicyNameExisted(request.getPolicyName(), true, policyId);
        validatePolicyConditions(request);

        policy.update(
                request.getPolicyName(),
                request.getAction(),
                request.getConditions(),
                request.getGroupId()
        );

        this.updateById(policy);
    }

    private ApplicationCorrectPolicy getPolicyByIdAndValidate(String policyId) {
        ApplicationCorrectPolicy policy = this.lambdaQuery()
                .eq(ApplicationCorrectPolicy::getPolicyId, policyId)
                .eq(ApplicationCorrectPolicy::getDeleted, false)
                .oneOpt()
                .orElseThrow(() -> new BusinessException("策略不存在"));
        String realName = userInfoService.getRealName(policy.getCreateUser());
        policy.setCreateUser(realName);
        return policy;
    }

    /**
     * 验证策略条件
     */
    private void validatePolicyConditions(AddApplicationCorrectPolicyRequest request) {
        if (CollectionUtils.isEmpty(request.getConditions())) {
            throw new BusinessException("策略条件不能为空");
        }

        // 验证条件的合理性
        for (PolicyConditionDTO condition : request.getConditions()) {
            String host = condition.getHost();
            String port = condition.getPort();
            if (StringUtils.isBlank(host) || StringUtils.isBlank(port)) {
                throw new BusinessException("每个条件至少需要设置IP/域名或端口");
            }

            // 验证端口格式
            if (!StringUtils.isEmpty(port)) {
                int portNum = Integer.parseInt(port.trim());
                if (portNum < 1 || portNum > 65535) {
                    throw new BusinessException("端口范围必须在1-65535之间");
                }
            }
        }

        // 根据策略动作验证特定条件
        CorrectPolicyActionEnum action = request.getAction();
        if (CorrectPolicyActionEnum.MERGE_TO_ONE_APP.equals(action)) {
            validateMergeApplicationConditions(request);
        }
    }

    /**
     * 验证合并应用策略的条件
     */
    private void validateMergeApplicationConditions(AddApplicationCorrectPolicyRequest request) {
        String belongGroupId = request.getGroupId();
        applicationService.getGroupByApplicationId(belongGroupId)
                .orElseThrow(() -> new BusinessException("所属分组不存在"));

        String applicationName = request.getApplicationName();
        if (StringUtils.isBlank(applicationName)) {
            throw new BusinessException("合并为一个应用时，需要设置合并后的应用名称");
        }
        boolean existed = applicationService.checkExistName(applicationName);
        if (existed) {
            throw new BusinessException("应用名称已存在");
        }
    }

    /**
     * 执行策略时新建一个应用，并绑定id，但是不需要处理历史数据
     */
    private void executePolicy(ApplicationCorrectPolicy policy) {
        // 排除资产不需要额外处理，在网关处理时，如果有新增的app，进行处理即可
        if (policy.getAction() == CorrectPolicyActionEnum.MERGE_TO_ONE_APP) {
            // 先校验分组是否还有效
            applicationService.getGroupByApplicationId(policy.getGroupId())
                    .orElseThrow(() -> new BusinessException("所属分组不存在，请重新选择"));
            // 校验名称是否重复
            if (applicationService.checkExistName(policy.getMergedName())) {
                throw new BusinessException("应用名称已存在");
            }
            Application application = new Application(policy);
            // 后续如果有符合条件的应用，则更新urlEndpoints
            applicationService.save(application);
            // 回填策略关联应用id
            policy.setRelateAppId(application.getApplicationId());
        }
    }

    /**
     * 回滚策略逻辑
     */
//    private void rollbackPolicy(ApplicationCorrectPolicy policy) {
//        if (policy.getAction() == CorrectPolicyActionEnum.EXCLUDE_FROM_ASSETS) {
//            rollbackExcludeFromAssetsPolicy(policy);
//        } else if (policy.getAction() == CorrectPolicyActionEnum.MERGE_TO_ONE_APP) {
//            rollbackMergeApplicationsPolicy(policy);
//        }
//
//        log.info("策略回滚完成: {}, 动作: {}", policy.getPolicyName(), policy.getAction());
//    }

    /**
     * 执行"不计入资产"策略
     */
    private void executeExcludeFromAssetsPolicy(ApplicationCorrectPolicy policy) {
        List<PolicyConditionDTO> conditions = policy.getConditions();
        List<Application> matchedApps = findMatchedApplications(conditions);

        if (!CollectionUtils.isEmpty(matchedApps)) {
            // 标记应用为不计入资产
            matchedApps.forEach(app -> app.markAsExcludedFromAssets(policy.getPolicyId()));

            applicationService.updateBatchById(matchedApps);
            log.info("标记 {} 个应用不计入资产", matchedApps.size());
        }
    }

    /**
     * 执行"合并应用"策略
     */
    private void executeMergeApplicationsPolicy(ApplicationCorrectPolicy policy) {
        List<PolicyConditionDTO> conditions = policy.getConditions();
        List<Application> matchedApps = findMatchedApplications(conditions);

        if (!CollectionUtils.isEmpty(matchedApps)) {
            Optional<Application> optApp = applicationService.getByApplicationId(policy.getRelateAppId());
            if (!optApp.isPresent()) {
                throw new BusinessException("应用不存在");
            }
            Application targetApplication = optApp.get();

            // 收集所有匹配应用的ID
            List<String> matchedAppIds = matchedApps.stream()
                    .map(Application::getApplicationId)
                    .collect(Collectors.toList());

            log.info("开始执行合并应用策略，策略ID：{}，目标应用ID：{}，匹配应用数量：{}",
                    policy.getPolicyId(), targetApplication.getApplicationId(), matchedApps.size());

            // 1. 获取所有需要合并的API
            List<ApiInfo> allApisToMerge = apiInfoService.getByApplicationIds(matchedAppIds, false);
            if (!CollectionUtils.isEmpty(allApisToMerge)) {
                // 2. 更新API的应用ID为目标应用ID
                updateApisApplicationId(allApisToMerge, targetApplication.getApplicationId());

                // 3. 更新API的主应用ID
                apiInfoService.batchUpdateMainApplicationId(matchedAppIds, targetApplication.getApplicationId());

                log.info("更新API应用ID完成，影响API数量：{}", allApisToMerge.size());
            }

            // 4. 更新风险日志的应用ID
            boolean riskLogUpdateResult = riskLogNewService.updateRiskLogsAppId(matchedAppIds, targetApplication.getApplicationId());
            if (riskLogUpdateResult) {
                log.info("更新风险日志应用ID完成");
            }

            // 5. 更新ES日志的应用ID
            updateEsLogsAppId(matchedAppIds, targetApplication.getApplicationId());

            // 6. 更新涉敏日志的应用ID（如果有API需要更新）
            if (!CollectionUtils.isEmpty(allApisToMerge)) {
                sensitiveLogService.updateAppIdByApiId(allApisToMerge);
                log.info("更新涉敏日志应用ID完成");
            }

            // 7. 更新目标应用的URL端点，合并所有匹配应用的端点
            updateTargetApplicationEndpoints(targetApplication, matchedApps);

            // 8. 标记匹配的应用为已合并状态
            markApplicationsAsMerged(matchedApps, policy.getPolicyId(), targetApplication.getApplicationId());

            log.info("合并应用策略执行完成，策略ID：{}，合并应用数：{}",
                    policy.getPolicyId(), matchedApps.size());
        }
    }
//
//    /**
//     * 回滚"不计入资产"策略
//     */
//    private void rollbackExcludeFromAssetsPolicy(ApplicationCorrectPolicy policy) {
//        List<Application> affectedApps = applicationService.lambdaQuery()
//                .eq(Application::getCorrectPolicyId, policy.getPolicyId())
//                .eq(Application::getExcludedFromAssets, true)
//                .list();
//
//        affectedApps.forEach(Application::restoreToAssets);
//
//        applicationService.updateBatchById(affectedApps);
//        log.info("回滚 {} 个应用的资产排除状态", affectedApps.size());
//    }
//
//    /**
//     * 回滚"合并应用"策略
//     */
//    private void rollbackMergeApplicationsPolicy(ApplicationCorrectPolicy policy) {
//        // 找到所有相关应用（包括主应用和被合并的应用）
//        List<Application> allAffectedApps = applicationService.lambdaQuery()
//                .eq(Application::getCorrectPolicyId, policy.getPolicyId())
//                .list();
//
//        // 收集需要回滚的基础分组映射关系
//        Map<String, String> baseAppIdMappings = new HashMap<>();
//
//        // 恢复所有应用状态，并收集基础分组映射
//        for (Application app : allAffectedApps) {
//            // 获取应用对应的基础分组ID
//            Optional<Application> baseApp = applicationService.getBaseApplication(app.getApplicationId());
//            if (baseApp.isPresent()) {
//                String baseAppId = baseApp.get().getApplicationId();
//                // 记录：当前基础分组ID -> 应该恢复到的基础分组ID（就是自己）
//                baseAppIdMappings.put(baseAppId, baseAppId);
//            }
//
////            app.restoreFromMerge(); 现在没回滚了，下一个。
//        }
//
//        applicationService.updateBatchById(allAffectedApps);
//
//        // 回滚基础分组下的API主应用ID
//        if (!baseAppIdMappings.isEmpty()) {
//            List<String> baseAppIds = new ArrayList<>(baseAppIdMappings.keySet());
//            apiInfoService.rollbackMainApplicationId(baseAppIds);
//
//            // 回滚ES日志的appId
//            rollbackEsLogsAppId(baseAppIdMappings);
//
//            // 回滚风险日志的appId
//            rollbackRiskLogsAppId(baseAppIdMappings);
//
//        }
//
//        log.info("回滚合并应用策略成功，策略ID：{}，回滚应用数：{}",
//                policy.getPolicyId(), allAffectedApps.size());
//    }

    /**
     * 根据条件查找匹配的应用
     */
    private List<Application> findMatchedApplications(List<PolicyConditionDTO> conditions) {
        List<Application> allApps = applicationService.listMatchApplication();

        return allApps.stream()
                .filter(app -> matchesConditions(app, conditions))
                .collect(Collectors.toList());
    }

    /**
     * 检查应用是否匹配条件（OR关系）
     */
    private boolean matchesConditions(Application app, List<PolicyConditionDTO> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            return false;
        }

        return conditions.stream().anyMatch(condition -> matchesCondition(app, condition));
    }

    /**
     * 检查应用是否匹配单个条件
     */
    private boolean matchesCondition(Application app, PolicyConditionDTO condition) {
        boolean portMatches = false;
        boolean uriMatches = true;

        List<UrlEndpoint> urlEndpoints = app.getUrlEndpoints();
        // 按理来说非应用合并的 app 有且只会有一个 urlEndpoint，所以这里直接取数组第一个对象
        UrlEndpoint urlEndpoint = urlEndpoints.get(0);

        // 检查主机匹配（支持通配符）
        String host = condition.getHost();
        boolean hostMatches = matchesWithWildcard(urlEndpoint.getHost(), host);

        // 检查端口匹配
        if (StringUtils.isNotBlank(condition.getPort())) {
            portMatches = condition.getPort().equals(urlEndpoint.getPort());
        }

        // 检查URI匹配（支持占位符和通配符）
        if (StringUtils.isNotBlank(condition.getUri())) {
            String appUri = urlEndpoint.getUri();
            if (StringUtils.isBlank(appUri)) {
                uriMatches = false;
            } else {
                // 处理URI匹配
                uriMatches = matchesUri(appUri, condition.getUri());
            }
        }

        // 如果所有条件都设置了，需要都匹配
        if (StringUtils.isNotBlank(condition.getPort()) && StringUtils.isNotBlank(condition.getUri())) {
            return hostMatches && portMatches && uriMatches;
        }
        // 如果只设置了端口和主机，需要都匹配
        else if (StringUtils.isNotBlank(condition.getPort())) {
            return hostMatches && portMatches;
        }
        // 如果只设置了URI和主机，需要都匹配
        else if (StringUtils.isNotBlank(condition.getUri())) {
            return hostMatches && uriMatches;
        }

        // 如果只设置了主机，匹配主机即可
        return hostMatches;
    }

    /**
     * 通配符匹配
     */
    private boolean matchesWithWildcard(String text, String pattern) {
        if (text == null || pattern == null) {
            return false;
        }

        // 将通配符转换为正则表达式
        String regex = pattern.replace("*", ".*").replace("?", ".");
        return Pattern.matches(regex, text);
    }

    /**
     * URI匹配，支持占位符和通配符
     * 占位符格式: /api/{value}/xxx
     * 通配符格式: /api/* 或 /api/**
     */
    private boolean matchesUri(String appUri, String patternUri) {
        // 处理 /** 通配符（匹配任意多级路径）
        if (patternUri.endsWith("/**")) {
            String prefix = patternUri.substring(0, patternUri.length() - 3);
            return appUri.startsWith(prefix);
        }

        // 处理 /* 通配符（匹配单级路径）
        if (patternUri.endsWith("/*")) {
            String prefix = patternUri.substring(0, patternUri.length() - 2);
            // 确保不匹配多级路径
            if (!appUri.startsWith(prefix)) {
                return false;
            }
            String remaining = appUri.substring(prefix.length());
            return !remaining.contains("/") || remaining.equals("/");
        }

        // 处理占位符 /api/{value}/xxx
        if (patternUri.contains("{") && patternUri.contains("}")) {
            // 使用项目中已有的 ApiUrlUtils.isMatch 方法
            return ApiUrlUtils.isMatch(patternUri, appUri);
        }

        // 精确匹配
        return patternUri.equals(appUri);
    }


    @Override
    public PolicyAuditResponse auditAssets(PolicyAuditRequest request) {
        List<Application> matchedApps = findMatchedApplications(request.getConditions());

        PolicyAuditResponse response = new PolicyAuditResponse();
        response.setTotalApplications(matchedApps.size());

        // 计算影响的API数量和应用摘要
        List<PolicyAuditResponse.ApplicationSummary> summaries = matchedApps.stream()
                .map(app -> {
                    int apiCount = getApiCountByApplicationId(app.getApplicationId());
                    return new PolicyAuditResponse.ApplicationSummary(app, apiCount);
                })
                .collect(Collectors.toList());

        response.setAffectedApplications(summaries);
        response.setTotalApis(summaries.stream().mapToInt(PolicyAuditResponse.ApplicationSummary::getApiCount).sum());

        return response;
    }

    @Override
    public String getPolicyName(String policyId) {
        return this.lambdaQuery()
                .eq(ApplicationCorrectPolicy::getPolicyId, policyId)
                .eq(ApplicationCorrectPolicy::getDeleted, false)
                .oneOpt()
                .map(ApplicationCorrectPolicy::getPolicyName)
                .orElse("已删除策略");
    }

    @Override
    public List<String> getPolicyNames(List<String> policyIds) {
        if (policyIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(ApplicationCorrectPolicy::getPolicyId, policyIds)
                .eq(ApplicationCorrectPolicy::getDeleted, false)
                .list()
                .stream()
                .map(ApplicationCorrectPolicy::getPolicyName)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public void mergeApplication(ApplicationCorrectPolicy merge) {
        CorrectPolicyActionEnum action = merge.getAction();
        if (CorrectPolicyActionEnum.EXCLUDE_FROM_ASSETS.equals(action)) {
            executeExcludeFromAssetsPolicy(merge);
            return;
        }
        executeMergeApplicationsPolicy(merge);
    }

    /**
     * 获取应用下的API数量
     */
    private int getApiCountByApplicationId(String applicationId) {
        return apiInfoService.getByApplicationId(applicationId).size();
    }

    private void validPolicyNameExisted(String policyName, boolean excludeSelf, String policyId) {
        if (policyName.trim().isEmpty()) {
            throw new BusinessException("策略名称不能为空");
        }
        boolean exists = this.lambdaQuery()
                .eq(ApplicationCorrectPolicy::getPolicyName, policyName)
                .ne(excludeSelf && StringUtils.isNotBlank(policyId), ApplicationCorrectPolicy::getPolicyId, policyId)
                .eq(ApplicationCorrectPolicy::getDeleted, false)
                .exists();

        if (exists) {
            throw new BusinessException("策略名称已存在");
        }
    }

    /**
     * 批量更新ES日志的appId
     *
     * @param oldAppIds 原基础分组ID列表
     * @param newAppId  新的主应用基础分组ID
     */
    private void updateEsLogsAppId(List<String> oldAppIds, String newAppId) {
        if (CollectionUtils.isEmpty(oldAppIds) || StringUtils.isBlank(newAppId)) {
            return;
        }

        log.info("开始更新ES日志appId，原appId列表：{}，新appId：{}", oldAppIds, newAppId);

        Map<String, Object> updateFields = new HashMap<>();
        updateFields.put("appId", newAppId);

        // 构建查询条件：appId在原列表中的日志
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .build()
                .addMultipleQuery("appId", oldAppIds);

        long updatedCount = nginxLogEsClient.updateByQueryId(queryDTO, updateFields);
    }

    /**
     * 回滚ES日志的appId
     *
     * @param appIdMappings 基础分组ID映射关系 (当前ID -> 应该恢复的ID)
     */
    private void rollbackEsLogsAppId(Map<String, String> appIdMappings) {
        if (MapUtils.isEmpty(appIdMappings)) {
            return;
        }

        log.info("开始回滚ES日志appId，映射关系：{}", appIdMappings);
        // 对于回滚，每个基础分组的日志都恢复到指向自己
        for (Map.Entry<String, String> entry : appIdMappings.entrySet()) {
            String currentAppId = entry.getKey();
            String targetAppId = entry.getValue(); // 应该恢复的ID（通常就是自己）

            if (!currentAppId.equals(targetAppId)) {
                // 只有当前ID和目标ID不同时才需要更新
                Map<String, Object> updateFields = new HashMap<>();
                updateFields.put("appId", targetAppId);

                EsQueryDTO queryDTO = EsQueryDTO.builder()
                        .build()
                        .addQuery("appId", currentAppId);

                long updatedCount = nginxLogEsClient.updateByQueryId(queryDTO, updateFields);
                log.info("ES日志appId回滚完成，{}->{}，更新记录数：{}", currentAppId, targetAppId, updatedCount);
            }
        }
    }

    /**
     * 回滚风险日志的appId
     *
     * @param appIdMappings 基础分组ID映射关系 (当前ID -> 应该恢复的ID)
     */
    private void rollbackRiskLogsAppId(Map<String, String> appIdMappings) {
        if (MapUtils.isEmpty(appIdMappings)) {
            return;
        }

        log.info("开始回滚风险日志appId，映射关系：{}", appIdMappings);

        // 对于回滚，每个基础分组的日志都恢复到指向自己
        for (Map.Entry<String, String> entry : appIdMappings.entrySet()) {
            String currentAppId = entry.getKey();
            String targetAppId = entry.getValue(); // 应该恢复的ID（通常就是自己）

            if (!currentAppId.equals(targetAppId)) {
                // 只有当前ID和目标ID不同时才需要更新
                boolean result = riskLogNewService.lambdaUpdate()
                        .eq(RiskLogNew::getAppId, currentAppId)
                        .set(RiskLogNew::getAppId, targetAppId)
                        .set(RiskLogNew::getUpdateTime, LocalDateTime.now())
                        .set(RiskLogNew::getUpdateUser, StpUtil.getLoginIdAsString())
                        .update();

                if (result) {
                    log.info("风险日志appId回滚完成，{}->{}", currentAppId, targetAppId);
                } else {
                    log.warn("风险日志appId回滚无匹配记录，{}->{}", currentAppId, targetAppId);
                }
            }
        }
    }

    private static ApplicationCorrectPolicy genarateApplicationCorrectPolicy(String userId, AddApplicationCorrectPolicyRequest request, String policyId) {
        return new ApplicationCorrectPolicy(
                policyId,
                request.getPolicyName(),
                request.getAction(),
                request.getConditions(),
                userId,
                request.getGroupId(),
                request.getApplicationName()
        );
    }

    /**
     * 更新API的应用ID
     */
    private void updateApisApplicationId(List<ApiInfo> apis, String newApplicationId) {
        if (CollectionUtils.isEmpty(apis) || StringUtils.isBlank(newApplicationId)) {
            return;
        }

        // 批量更新API的应用ID
        apis.forEach(api -> api.setAppId(newApplicationId));
        apiInfoService.updateBatchById(apis);

        log.info("批量更新API应用ID完成，影响API数量：{}", apis.size());
    }

    /**
     * 更新目标应用的URL端点，合并所有匹配应用的端点
     */
    private void updateTargetApplicationEndpoints(Application targetApplication, List<Application> matchedApps) {
        if (CollectionUtils.isEmpty(matchedApps)) {
            return;
        }

        // 收集所有匹配应用的URL端点
        Set<UrlEndpoint> allEndpoints = new HashSet<>();

        // 添加目标应用现有的端点
        if (!CollectionUtils.isEmpty(targetApplication.getUrlEndpoints())) {
            allEndpoints.addAll(targetApplication.getUrlEndpoints());
        }

        // 添加所有匹配应用的端点
        matchedApps.forEach(app -> {
            if (!CollectionUtils.isEmpty(app.getUrlEndpoints())) {
                allEndpoints.addAll(app.getUrlEndpoints());
            }
        });

        // 更新目标应用的端点
        targetApplication.setUrlEndpoints(new ArrayList<>(allEndpoints));
        applicationService.updateById(targetApplication);

        log.info("更新目标应用URL端点完成，合并端点数量：{}", allEndpoints.size());
    }

    /**
     * 标记匹配的应用为已合并状态
     */
    private void markApplicationsAsMerged(List<Application> matchedApps, String policyId, String targetApplicationId) {
        if (CollectionUtils.isEmpty(matchedApps)) {
            return;
        }

        // 标记应用为已合并状态
        matchedApps.forEach(app -> {
            app.setCorrectPolicyId(policyId);
            // 这里可以添加其他合并状态的标记
        });

        applicationService.updateBatchById(matchedApps);

        log.info("标记应用为已合并状态完成，影响应用数量：{}", matchedApps.size());
    }
}