package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.TagMapper;
import com.telecom.apigateway.model.entity.Tag;
import com.telecom.apigateway.model.vo.request.QueryTagRequest;
import com.telecom.apigateway.service.TagService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {
    @Override
    public String addTag(Tag tag) {
        this.getBaseMapper().insert(tag);
        return tag.getTagId();
    }

    @Override
    public Page<Tag> getTagsLikeName(QueryTagRequest query) {
        String name = query.getName();
        return this.lambdaQuery()
                .like(StringUtils.isNotEmpty(name), Tag::getTagName, name)
                .eq(Tag::getDeleted, false)
                .page(Page.of(query.getPageNum(), query.getPageSize()));
    }

    @Override
    public String deleteByTagId(@NotNull String tagId) {
        this.lambdaUpdate()
                .eq(Tag::getTagId, tagId)
                .eq(Tag::getDeleted, false)
                .set(Tag::getDeleted, true)
                .set(Tag::getUpdateTime, LocalDateTime.now())
                .update();
        return tagId;
    }

    @Override
    public Optional<Tag> getOpt(String tagId) {
        return this.lambdaQuery()
                .eq(Tag::getDeleted, false)
                .eq(Tag::getTagId, tagId)
                .oneOpt();
    }

    @Override
    public void batchDeleteByTagIds(List<String> tagIds) {
        this.lambdaUpdate()
                .in(Tag::getTagId, tagIds)
                .eq(Tag::getDeleted, false)
                .set(Tag::getDeleted, true)
                .set(Tag::getUpdateTime, LocalDateTime.now())
                .update();
    }
}