package com.telecom.apigateway.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.DeleteLogAnnotation;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.UpdateLogAnnotation;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.*;
import com.telecom.apigateway.model.enums.AccountStatusEnum;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.model.vo.response.*;
import com.telecom.apigateway.service.*;
import com.telecom.apigateway.utils.MapUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class UserBusinessService {
    private final UserInfoService userInfoService;
    private final MenuPermissionService menuPermissionService;
    private final RoleService roleService;
    private final UserRoleService userRoleService;
    private final UserService userService;

    public UserLoginResponse whoAmI() {
        String username = StpUtil.getLoginIdAsString();
        User user = getUser(username);
        UserInfo userInfo = getUserInfo(username);

        List<UserRole> userRoles = userRoleService.listByUsername(userInfo.getUsername());
        List<String> originRoleIds = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
        List<String> roleIds = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
        List<Role> roles;
        List<MenuPermission> menuPermissions;
        if (roleIds.contains("admin")) {
            roles = roleService.list();
            roleIds = roles.stream().map(Role::getRoleId).collect(Collectors.toList());
            menuPermissions = menuPermissionService.list();
        } else {
            roles = roleService.listByRoleIds(roleIds);
            Set<String> permissionCodes = roles.stream().map(Role::getMenuPermissions).flatMap(Arrays::stream).collect(Collectors.toSet());
            menuPermissions = menuPermissionService.getAllPermissionByCodes(permissionCodes);
        }

        List<String> leafRouteNames = menuPermissions.stream().map(MenuPermission::getRouteName).collect(Collectors.toList());
        List<String> categoryNames = menuPermissions.stream().map(MenuPermission::getCategoryName).distinct().collect(Collectors.toList());
        List<String> accessRoutes = Stream.concat(leafRouteNames.stream(), categoryNames.stream()).distinct().collect(Collectors.toList());

        UserLoginResponse.UserInfoResponse userInfoResponse = new UserLoginResponse.UserInfoResponse();
        userInfoResponse.setUserName(userInfo.getUsername());
        userInfoResponse.setRealName(userInfo.getRealName());
        userInfoResponse.setDepartment(userInfo.getDepartment());
        userInfoResponse.setPhone(userInfo.getPhone());
        userInfoResponse.setEmail(userInfo.getEmail());
        userInfoResponse.setValidEndTime(userInfo.getValidEndTime());
        userInfoResponse.setRole(roles.stream().filter((i) -> originRoleIds.contains(i.getRoleId())).map(RoleLabelResponse::new).collect(Collectors.toList()));



        UserLoginResponse userLoginResponse = new UserLoginResponse();
        userLoginResponse.setUserInfo(userInfoResponse);
        userLoginResponse.setRoles(roleIds);
        userLoginResponse.setPermissionCodes(menuPermissions.stream().map(MenuPermission::getCode).collect(Collectors.toList()));
        userLoginResponse.setAccessRoutes(accessRoutes);
        userLoginResponse.setHomeName(menuPermissions.stream().filter((i) -> i.getCategorySort() > 0 && StringUtils.isBlank(i.getParentCode())).findFirst().map(MenuPermission::getRoutePath).orElse(""));
        userLoginResponse.setType(roleIds.contains("admin") ? "admin" : "user");
        userLoginResponse.setChangePassword(user.getChangePassword());
        return userLoginResponse;
    }

    public void logout() {
        if (StpUtil.isLogin()) {
            StpUtil.logout();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.USER_MANAGEMENT,
            spelArgs = {"#{#request.realName + '(' + #request.username + ')'}"}
    )
    public void createUser(CreateUserRequest request) {
        String username = request.getUsername();
        String phone = request.getPhone();

        if (userService.checkUsernameAndPhone(username, phone)) {
            throw new BusinessException("用户名或手机号已存在");
        }
        User user = new User(username, phone);
        userService.codePasswordAndSave(user);

        UserInfo userInfo = new UserInfo();
        userInfo.setUsername(username);
        userInfo.setRealName(request.getRealName());
        userInfo.setDepartment(request.getDepartment());
        userInfo.setPhone(phone);
        userInfo.setEmail(request.getEmail());
        userInfo.setValidStartTime(request.getValidStartTime());
        userInfo.setValidEndTime(request.getValidEndTime());
        userInfo.setUpdateUser(StpUtil.getLoginIdAsString());
        userInfo.setCreateTime(LocalDateTime.now());
        userInfo.setUpdateTime(LocalDateTime.now());
        userInfoService.save(userInfo);

        List<UserRole> userRoles = request.getRoleIds().stream().map(roleId -> {
            UserRole userRole = new UserRole();
            userRole.setUsername(userInfo.getUsername());
            userRole.setRoleId(roleId);
            return userRole;
        }).collect(Collectors.toList());
        userRoleService.saveBatch(userRoles);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.USER_MANAGEMENT,
            description = "重置 #{#username} 的密码"
    )
    public void resetPassword(String username) {
        User user = getUser(username);
        user.resetPassword();
        userService.update(user);
    }

    @Transactional(rollbackFor = Exception.class)
    @DeleteLogAnnotation(
            resourceType = ResourceTypeEnum.USER_MANAGEMENT,
            displayName = "@userBusinessService.getRealName(#username)"
    )
    public void deleteUser(String username) {
        checkUser(username);
        User user = getUser(username);
        user.delete();
        userService.updateById(user);

        UserInfo userInfo = getUserInfo(username);
        userInfo.delete();
        userInfoService.updateById(userInfo);

        userRoleService.deleteByUsername(username);
    }

    private UserInfo getUserInfo(String username) {
        Optional<UserInfo> optUser = userInfoService.getByUsername(username);
        if (!optUser.isPresent()) {
            throw new BusinessException("User not found");
        }
        return optUser.get();
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.USER_MANAGEMENT,
            description = "封禁用户 #{#username}"
    )
    public void lockUser(String username) {
        checkUser(username);
        User user = getUser(username);
        user.setAccountStatus(AccountStatusEnum.LOCKED.getValue());
        userService.updateById(user);
        StpUtil.logout(username);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.USER_MANAGEMENT,
            description = "解封用户 #{#username}"
    )
    public void unlockUser(String username) {
        checkUser(username);
        User user = getUser(username);
        updateAccountStatus(user, AccountStatusEnum.ACTIVE);
    }



    private User getUser(String username) {
        Optional<User> optUser = userService.getByUsername(username);
        if (!optUser.isPresent()) {
            throw new BusinessException("User not found");
        }
        return optUser.get();
    }

    private void checkUser(String username) {
        if ("admin".equals(username)) {
            throw new BusinessException("无法对超级管理员进行相关操作");
        }
        if (username.equals(StpUtil.getLoginIdAsString())) {
            throw new BusinessException("无法对当前登录用户进行相关操作");
        }
    }

    public List<BaseLabelResponse> overview() {
        List<User> list = userService.list();
        List<UserInfo> userInfoList = userInfoService.list();
        List<UserInfo> expiredUser = userInfoList.stream().filter((info) -> LocalDateTime.now().isAfter(info.getValidEndTime())).collect(Collectors.toList());
        Map<String, UserInfo> expiredUserMap = MapUtil.toMapByUnionParams(UserInfo::getUsername, expiredUser);

        long countExpiringUser = userInfoList.stream().filter((info) -> LocalDateTime.now().isBefore(info.getValidEndTime()) && LocalDateTime.now().plusDays(30).isAfter(info.getValidEndTime())).count();
        long countActiveUser = list.stream().filter((i) -> !expiredUserMap.containsKey(i.getUsername()) && AccountStatusEnum.ACTIVE.getValue().equals(i.getAccountStatus())).count();
        long countLockedUser = list.stream().filter((i) -> !expiredUserMap.containsKey(i.getUsername()) && AccountStatusEnum.LOCKED.getValue().equals(i.getAccountStatus())).count();

        List<BaseLabelResponse> resp = new ArrayList<>();
        resp.add(new BaseLabelResponse("用户总数(个)", list.size()));
        resp.add(new BaseLabelResponse("正常用户数(个)", (int) (countActiveUser - countExpiringUser)));
        resp.add(new BaseLabelResponse("临期用户数(个)", (int) countExpiringUser));
        resp.add(new BaseLabelResponse("禁用用户数(个)", (int) countLockedUser));

        return resp;
    }

    public List<BaseLabelResponse> status() {
        List<User> list = userService.list();
        List<UserInfo> userInfoList = userInfoService.list();
        List<UserInfo> expiredUser = userInfoList.stream().filter((info) -> LocalDateTime.now().isAfter(info.getValidEndTime())).collect(Collectors.toList());
        Map<String, UserInfo> expiredUserMap = MapUtil.toMapByUnionParams(UserInfo::getUsername, expiredUser);

        long countExpiringUser = userInfoList.stream().filter((info) -> LocalDateTime.now().isBefore(info.getValidEndTime()) && LocalDateTime.now().plusDays(30).isAfter(info.getValidEndTime())).count();
        long countActiveUser = list.stream().filter((i) -> !expiredUserMap.containsKey(i.getUsername()) && AccountStatusEnum.ACTIVE.getValue().equals(i.getAccountStatus())).count();
        long countLockedUser = list.stream().filter((i) -> !expiredUserMap.containsKey(i.getUsername()) && AccountStatusEnum.LOCKED.getValue().equals(i.getAccountStatus())).count();

        List<BaseLabelResponse> resp = new ArrayList<>();
        resp.add(new BaseLabelResponse("正常",(int) (countActiveUser - countExpiringUser)));
        resp.add(new BaseLabelResponse("临期", (int) countExpiringUser));
        resp.add(new BaseLabelResponse("禁用", (int) countLockedUser));
        resp.add(new BaseLabelResponse("失效", expiredUser.size()));

        return resp;
    }

    public Page<QueryUserResponse> query(QueryUserRequest request) {
        return userService.queryUsers(Page.of(request.getPageNum(), request.getPageSize()), request);
    }

    public List<String> department() {
        return userService.queryAllDepartments();
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.USER_MANAGEMENT,
            description = "用户 {currentUser} 修改密码"
    )
    public String updatePwd(UpdatePwdRequest request) {
        String username = StpUtil.getLoginIdAsString();
        User user = getUser(username);
        if (!user.getChangePassword()) {
            String oldPassword = request.getOldPassword();
            Assert.hasText(oldPassword, "Old password is required");
            boolean checkPwd = BCrypt.checkpw(oldPassword, user.getPassword());
            if (!checkPwd) {
                throw new BusinessException("Password error");
            }
        }
        user.updatePassword(request.getNewPassword());
        userService.update(user);
        StpUtil.logout();
        return username;
    }

    private User checkPassword(String username, String password) {
        User user = userService.getByUsername(username).orElseThrow(() -> new BusinessException("User not found"));
        boolean checkPwd = BCrypt.checkpw(password, user.getPassword());
        if (!checkPwd) {
            throw new BusinessException("Password error");
        }
        return user;
    }

    public UserDetailResponse getByUsername(String username) {
        User user = getUser(username);
        UserInfo userInfo = getUserInfo(username);
        List<UserRole> userRoles = userRoleService.listByUsername(username);
        UserDetailResponse queryUserResponse = new UserDetailResponse();
        queryUserResponse.setUsername(user.getUsername());
        queryUserResponse.setRoleIds(userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList()));
        queryUserResponse.setEmail(userInfo.getEmail());
        queryUserResponse.setPhone(userInfo.getPhone());
        queryUserResponse.setDepartment(userInfo.getDepartment());
        queryUserResponse.setRealName(userInfo.getRealName());
        queryUserResponse.setValidStartTime(userInfo.getValidStartTime());
        queryUserResponse.setValidEndTime(userInfo.getValidEndTime());
        queryUserResponse.setLoginMode("PASSWORD");
        return queryUserResponse;
    }

    @Transactional(rollbackFor = Exception.class)
    @UpdateLogAnnotation(
            resourceType = ResourceTypeEnum.USER_MANAGEMENT,
            displayName = "@userBusinessService.getRealName(#request.username)",
            queryMethod = "@userBusinessService.getByUsername(#request.username)"
    )
    public void updateUser(UpdateUserRequest request) {
        String username = request.getUsername();
        checkUser(username);
        User user = getUser(username);
        UserInfo userInfo = getUserInfo(username);

        user.setPhone(request.getPhone());
        userService.updateById(user);

        userInfo.setDepartment(request.getDepartment());
        userInfo.setEmail(request.getEmail());
        userInfo.setValidStartTime(request.getValidStartTime());
        userInfo.setValidEndTime(request.getValidEndTime());
        userInfo.setUpdateUser(StpUtil.getLoginIdAsString());
        userInfo.setPhone(request.getPhone());
        userInfoService.updateById(userInfo);

        userRoleService.deleteByUsername(username);
        List<UserRole> userRoles = request.getRoleIds().stream().map(roleId -> {
            UserRole userRole = new UserRole();
            userRole.setUsername(username);
            userRole.setRoleId(roleId);
            return userRole;
        }).collect(Collectors.toList());
        userRoleService.saveBatch(userRoles);
    }

    public String getRealName(String username) {
        return userInfoService.getByUsername(username).map(UserInfo::getRealName).orElse(username);
    }

    public void updateAccountStatus(User user, AccountStatusEnum status) {
        user.setAccountStatus(status.getValue());
        userService.updateById(user);
    }
}
