package com.telecom.apigateway.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.ApiTagRelation;
import com.telecom.apigateway.model.entity.Tag;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.model.vo.response.AddTagResponse;
import com.telecom.apigateway.model.vo.response.TagOptionResponse;
import com.telecom.apigateway.model.vo.response.TagResponse;
import com.telecom.apigateway.service.ApiTagRelationService;
import com.telecom.apigateway.service.TagBusinessService;
import com.telecom.apigateway.service.TagService;
import com.telecom.apigateway.utils.PageUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.telecom.apigateway.common.ResultCodeEnum.TAG_HAS_RELATION_API;
import static com.telecom.apigateway.common.ResultCodeEnum.TAG_NOT_EXISTED;

@Service
@AllArgsConstructor
public class TagBusinessServiceImpl implements TagBusinessService {
    private final TagService tagService;
    private final ApiTagRelationService apiTagRelationService;

    @Override
    public AddTagResponse addTag(String userId, AddOrUpdateTag req) {
        String tagName = req.getTagName().trim();
        if (StringUtils.isBlank(tagName)) {
            throw new BusinessException("标签名称不能为空");
        }
        String tagId = IdUtil.simpleUUID();
        Tag tag = new Tag(tagId, userId, tagName);
        String returnTagId = tagService.addTag(tag);
        return new AddTagResponse(returnTagId);
    }

    @Override
    public Page<TagResponse> getTagsLikeName(QueryTagRequest request) {
        Page<Tag> pages = tagService.getTagsLikeName(request);
        List<TagResponse> responses = pages.getRecords().stream().map(TagResponse::covertTagResponse).collect(Collectors.toList());
        return PageUtils.convertPage(pages, responses);
    }

    @Override
    public Page<TagResponse> getTags(BasePageQueryRequest request) {
        Page<Tag> pages = tagService.getTagsLikeName(new QueryTagRequest(request));
        List<TagResponse> responses = pages.getRecords().stream().map(TagResponse::covertTagResponse).collect(Collectors.toList());
        return PageUtils.convertPage(pages, responses);
    }

    @Override
    public void deleteTag(DeleteTagRequest request) {
        String tagId = request.getTagId().trim();
        List<ApiTagRelation> relations = apiTagRelationService.listByTagId(tagId);
        if (!relations.isEmpty()) {
            throw new BusinessException(TAG_HAS_RELATION_API);
        }
        tagService.deleteByTagId(tagId);
    }

    @Override
    public void deleteTags(BatchDeleteTagRequest request) {
        // TODO 或许要鉴权
        List<String> tagIds = request.getTagIds();
        List<ApiTagRelation> relations = apiTagRelationService.listByTagIds(tagIds);
        if (!relations.isEmpty()) {
            throw new BusinessException(TAG_HAS_RELATION_API);
        }
        tagService.batchDeleteByTagIds(tagIds);
    }

    @Override
    public void updateTag(AddOrUpdateTag tag) {
        Tag existedTag = getExistedTag(tag.getTagId());
        existedTag.updateName(tag.getTagName().trim());
        tagService.updateById(existedTag);
    }

    @Override
    public void unlinkCurrentTagAllApi(DeleteTagRequest request) {
        String tagId = request.getTagId().trim();
        getExistedTag(tagId);
        apiTagRelationService.deleteByTagId(tagId);
    }

    @Override
    public void linkApi(String userId, BatchLinkTagRequest request) {
        // TODO check api existed
        String tagId = request.getTagId().trim();
        getExistedTag(tagId);

        List<ApiTagRelation> apiTagRelations = apiTagRelationService.listByTagId(tagId);
        Set<String> relatedApiIds = apiTagRelations
                .stream()
                .map(ApiTagRelation::getApiId)
                .collect(Collectors.toSet());
        Set<String> notRelatedApiIdSet = request.getApiIds()
                .stream()
                .filter((item) -> !relatedApiIds.contains(item))
                .collect(Collectors.toSet());

        List<ApiTagRelation> relations = notRelatedApiIdSet
                .stream()
                .map((apiId) -> new ApiTagRelation(tagId, apiId, userId))
                .collect(Collectors.toList());

        apiTagRelationService.saveBatch(relations);
    }

    @Override
    public List<TagOptionResponse> getOption() {
        List<Tag> tags = tagService.lambdaQuery()
                .eq(Tag::getDeleted, false)
                .list();
        return tags.stream().map(TagOptionResponse::new).collect(Collectors.toList());
    }

    private Tag getExistedTag(String tagId) {
        Optional<Tag> tagOpt = tagService.getOpt(tagId);
        if (!tagOpt.isPresent()) {
            throw new BusinessException(TAG_NOT_EXISTED);
        }
        return tagOpt.get();
    }
}
