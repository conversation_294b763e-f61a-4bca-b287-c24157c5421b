package com.telecom.apigateway.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.dto.CheckCveRuleDTO;
import com.telecom.apigateway.model.dto.ValidateDirectiveDTO;
import com.telecom.apigateway.model.dto.ValidationFilenameDTO;
import com.telecom.apigateway.model.entity.Rule;
import com.telecom.apigateway.model.entity.RuleConfig;
import com.telecom.apigateway.model.enums.*;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.model.vo.response.QueryCveRuleResponse;
import com.telecom.apigateway.service.CveRuleBusinessService;
import com.telecom.apigateway.service.DirectivesService;
import com.telecom.apigateway.service.RuleConfigService;
import com.telecom.apigateway.service.RuleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CveRuleBusinessServiceImpl implements CveRuleBusinessService {
    private final RuleService ruleService;
    private final RuleConfigService ruleConfigService;
    private final DirectivesService directivesService;

    private static final String ALLOWED_EXTENSION = "poc";

    @Override
    public List<CheckCveRuleDTO> checkCveRule(MultipartFile[] files) {
        if (Objects.isNull(files) || files.length == 0) {
            throw new BusinessException("未上传文件");
        }

        // check file name repeat
        Set<String> fileNames = Arrays.stream(files).map(MultipartFile::getOriginalFilename).collect(Collectors.toSet());
        if (fileNames.size() != files.length) {
            throw new BusinessException("文件名称重复");
        }

        List<CheckCveRuleDTO> checkCveRuleDTOS = new ArrayList<>();
        for (MultipartFile file : files) {
            // 检查文件后缀名
            if (!isValidFileExtension(file)) {
                log.error("文件[{}]格式不正确，只支持.poc文件", file.getOriginalFilename());
                throw new BusinessException("文件格式不正确");
            }

            // 检查文件名称格式
            ValidationFilenameDTO validationFilenameDTO = ValidationFilenameDTO.validateAndParse(file.getOriginalFilename());
            if (!validationFilenameDTO.getIsValid()) {
                log.error("文件[{}]名称格式不正确", file.getOriginalFilename());
                throw new BusinessException("文件名称格式不正确");
            }

            ValidateDirectiveDTO validateDirectiveDTO = checkFileContent(file);
            CheckCveRuleDTO dto = new CheckCveRuleDTO(file.getOriginalFilename(), validationFilenameDTO, validateDirectiveDTO);
            checkCveRuleDTOS.add(dto);
        }
        return checkCveRuleDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.PROTECTION_CONFIG,
            description = "导入CVE规则 #{#result}"
    )
    public List<String> saveCveRule(MultipartFile[] files) {
        List<CheckCveRuleDTO> checkResults = checkCveRule(files);
        List<Rule> rules = new ArrayList<>();
        List<RuleConfig> ruleConfigs = new ArrayList<>();

        // check rule id exists
        List<ValidateDirectiveDTO> validateDirectiveDTOS = checkResults.stream().map(CheckCveRuleDTO::getValidateDirectiveDTO).collect(Collectors.toList());
        Set<String> ruleId = validateDirectiveDTOS.stream().map(ValidateDirectiveDTO::getRuleId).collect(Collectors.toSet());
        List<Rule> existedRules = ruleService.lambdaQuery().in(Rule::getRuleId, ruleId).list();
        if (!existedRules.isEmpty()) {
            throw new BusinessException("规则ID已存在");
        }
        for (CheckCveRuleDTO dto : checkResults) {
            Rule rule = generateRule(dto);
            rules.add(rule);

            RuleConfig ruleConfig = generateRuleConfig(dto, rule);
            ruleConfigs.add(ruleConfig);
        }

        // save rules and ruleConfigs
        ruleService.saveBatch(rules);
        ruleConfigService.saveBatch(ruleConfigs);
        return rules.stream().map(Rule::getModule).collect(Collectors.toList());
    }

    @Override
    public Page<QueryCveRuleResponse> queryCveRule(QueryCveRuleRequest request) {
        String cveName = StringUtils.trimToEmpty(request.getCveName());
        String status = request.getStatus();
        int pageNum = request.getPageNum() == null ? 1 : request.getPageNum();
        int pageSize = request.getPageSize() == null ? 10 : request.getPageSize();

        return ruleService.queryCveRuleWithConfig(
                Page.of(pageNum, pageSize),
                cveName,
                status,
                CategoryEnum.CUSTOM.getCode()
        );
    }

    @Override
    public String queryCveRuleDetail(String cveId) {
        Optional<RuleConfig> ruleConfig = ruleConfigService.optByRuleId(cveId);
        if (!ruleConfig.isPresent()) {
            throw new BusinessException("规则不存在");
        }
        return ruleConfig.get().getDirectives();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.PROTECTION_CONFIG,
            description = "配置CVE规则 #{@cveRuleBusinessServiceImpl.getCveName(#request.cveId)} 为 #{#request.configType.getText()}"
    )
    public void configCveRule(ConfigCveRuleRequest request) {
        String cveId = request.getCveId();
        Optional<RuleConfig> ruleConfigOpt = ruleConfigService.optByRuleId(cveId);
        if (!ruleConfigOpt.isPresent()) {
            throw new BusinessException("规则不存在");
        }
        RuleConfig ruleConfig = ruleConfigOpt.get();

        // call directives service to config rule
        configRuleToCrs(ruleConfig, request.getConfigType());

        // call api success, update rule config
        ruleConfig.setType(request.getConfigType().name());
        ruleConfigService.updateById(ruleConfig);

        // 更新时间
        ruleService.updateTime(cveId);
    }

    @Override
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.PROTECTION_CONFIG,
            spelArgs = {"#{#result}"}
    )
    public List<String> deleteCveRule(DeleteCveRuleRequest request) {
        List<String> cveIds = request.getCveId();
        if (cveIds.isEmpty()) {
            throw new BusinessException("未选择规则");
        }
        List<RuleConfig> existedConfig = ruleConfigService.lambdaQuery()
                .eq(RuleConfig::getCategory, CategoryEnum.CUSTOM.getCode())
                .in(RuleConfig::getRuleId, cveIds)
                .list();
        if (existedConfig.isEmpty()) {
            return Collections.emptyList();
        }
        boolean hasEnableConfig = existedConfig.stream().anyMatch(i -> !ConfigTypeEnum.getEnum(i.getType()).equals(ConfigTypeEnum.DISABLE));
        if (hasEnableConfig) {
            throw new BusinessException("存在启用状态的规则，请先禁用规则");
        }
        ruleService.lambdaUpdate().in(Rule::getRuleId, cveIds).eq(Rule::getCategory, CategoryEnum.CUSTOM.getCode()).remove();
        ruleConfigService.lambdaUpdate().in(RuleConfig::getRuleId, cveIds).eq(RuleConfig::getCategory, CategoryEnum.CUSTOM.getCode()).remove();
        return existedConfig.stream().map(RuleConfig::getFilename).distinct().collect(Collectors.toList());
    }

    @Override
    public void batchConfigCveRule(BatchConfigCveRuleRequest request) {
        List<String> cveIds = request.getCveIds();
        if (cveIds.isEmpty()) {
            throw new BusinessException("未选择规则");
        }
        ConfigTypeEnum configType = request.getConfigType();
        List<RuleConfig> existedConfig = ruleConfigService.lambdaQuery()
                .eq(RuleConfig::getCategory, CategoryEnum.CUSTOM.getCode())
                .in(RuleConfig::getRuleId, cveIds)
                .ne(RuleConfig::getType, configType.name())
                .list();
        if (existedConfig.isEmpty()) {
            return;
        }

        // 接口调用事务问题，先循环处理
        for (RuleConfig ruleConfig : existedConfig) {
            configRuleToCrs(ruleConfig, configType);
            ruleConfig.setType(request.getConfigType().name());
            ruleConfigService.updateById(ruleConfig);
        }
    }

    @Override
    public String getCveName(String cveId) {
        return ruleService.getName(cveId);
    }

    /**
     * 检查CVE规则行是否有效
     */
    private ValidateDirectiveDTO isValidCveRule(String ruleContent) {
        return directivesService.validateDirectives(new ValidateDirectivesRequest(ruleContent));
    }

    /**
     * 检查文件后缀名是否为.poc
     */
    private boolean isValidFileExtension(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (StringUtils.isBlank(filename)) {
            return false;
        }
        String extension = StringUtils.substringAfterLast(filename, ".");
        return StringUtils.equalsIgnoreCase(ALLOWED_EXTENSION, extension);
    }

    public ValidateDirectiveDTO checkFileContent(MultipartFile file) {
        String content = getContentFromFile(file);
        // 验证整个内容
        return isValidCveRule(content);
    }

    private String getContentFromFile(MultipartFile file) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {

            // 一次性读取所有内容
            return reader.lines()
                    .collect(Collectors.joining("\n"));
        } catch (IOException e) {
            log.error("读取文件失败: {}", e.getMessage());
            return null;
        }
    }

    private void configRuleToCrs(RuleConfig ruleConfig, ConfigTypeEnum configType) {
        ConfigTypeEnum from = ConfigTypeEnum.getEnum(ruleConfig.getType());
        RuleConfigAction ruleConfigAction = new RuleConfigAction(from, configType);
        CustomRuleConfigRequest ruleConfigRequest = new CustomRuleConfigRequest(ruleConfig.getDirectives(), ruleConfigAction);
        directivesService.configCustomRule(ruleConfigRequest);
    }

    private Rule generateRule(CheckCveRuleDTO dto) {
        ValidationFilenameDTO validationFilenameDTO = dto.getValidationFilenameDTO();
        ValidateDirectiveDTO validateDirectiveDTO = dto.getValidateDirectiveDTO();
        Rule rule = new Rule();
        rule.setRuleId(validateDirectiveDTO.getRuleId());
        rule.setCategory(CategoryEnum.CUSTOM.getCode());
        rule.setAttackType(String.format("%s(%s)", validationFilenameDTO.getCode(), validationFilenameDTO.getDescription()));
        rule.setModule(validationFilenameDTO.getDescription());
        rule.setScoreKeyword(validationFilenameDTO.getIdentifier().toLowerCase() + "_score");
        rule.setType(validationFilenameDTO.getCode());
        return rule;
    }

    private RuleConfig generateRuleConfig(CheckCveRuleDTO dto, Rule rule) {
        String content = dto.getValidateDirectiveDTO().getDirective();
        RuleConfig ruleConfig = new RuleConfig();
        ruleConfig.setConfigId(IdUtil.fastSimpleUUID());
        ruleConfig.setRuleId(rule.getRuleId());
        // 默认为禁用状态
        ruleConfig.setType(ConfigTypeEnum.DISABLE.name());
        ruleConfig.setCategory(CategoryEnum.CUSTOM.getCode());
        ruleConfig.setFilename(dto.getFilename());
        ruleConfig.setDirectives(content);
        return ruleConfig;
    }
}
