package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.ApiTagRelationMapper;
import com.telecom.apigateway.model.entity.ApiTagRelation;
import com.telecom.apigateway.service.ApiTagRelationService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Service
public class ApiTagRelationServiceImpl extends ServiceImpl<ApiTagRelationMapper, ApiTagRelation> implements ApiTagRelationService {
    @Override
    public List<ApiTagRelation> listByTagId(String tagId) {
        return this.lambdaQuery()
                .eq(ApiTagRelation::getTagId, tagId)
                .eq(ApiTagRelation::getDeleted, false)
                .list();
    }

    @Override
    public void deleteByTagId(String tagId) {
        this.lambdaUpdate()
                .eq(ApiTagRelation::getTagId, tagId)
                .eq(ApiTagRelation::getDeleted, false)
                .set(ApiTagRelation::getDeleted, true)
                .set(ApiTagRelation::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public List<ApiTagRelation> listByTagIds(Collection<String> tagIds) {
        if (tagIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(ApiTagRelation::getTagId, tagIds)
                .eq(ApiTagRelation::getDeleted, false)
                .list();
    }
}
