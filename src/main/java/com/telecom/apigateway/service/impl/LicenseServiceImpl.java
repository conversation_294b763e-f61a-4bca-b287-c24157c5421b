package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.LLMLogMapper;
import com.telecom.apigateway.mapper.LicenseMapper;
import com.telecom.apigateway.model.entity.LLMLog;
import com.telecom.apigateway.model.entity.License;
import com.telecom.apigateway.service.LLMLogService;
import com.telecom.apigateway.service.LicenseService;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

/**
 * @program: APIWG-Service
 * @ClassName LicenseServiceImpl
 * @description:
 * @author: Levi
 * @create: 2025-05-22 18:56
 * @Version 1.0
 **/
@Service("licenseService")
public class LicenseServiceImpl  extends ServiceImpl<LicenseMapper, License> implements LicenseService {
}