package com.telecom.apigateway.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.telecom.apigateway.service.UserContextService;
import org.springframework.stereotype.Service;

/**
 * 用户上下文服务实现类
 * 使用 SaToken 获取当前用户信息
 */
@Service
public class UserContextServiceImpl implements UserContextService {
    
    @Override
    public String getCurrentUsername() {
        return StpUtil.getLoginIdAsString();
    }
    
    @Override
    public boolean isLogin() {
        return StpUtil.isLogin();
    }
}
