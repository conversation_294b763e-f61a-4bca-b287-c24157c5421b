package com.telecom.apigateway.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.mapper.LLMLogMapper;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.entity.LLMLog;
import com.telecom.apigateway.model.entity.UserInfo;
import com.telecom.apigateway.model.vo.request.LLMRequest;
import com.telecom.apigateway.model.vo.response.ThreatDetailResponse;
import com.telecom.apigateway.service.*;
import com.telecom.apigateway.utils.IpUtils;
import com.telecom.apigateway.utils.LLMUtil;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * @program: APIWG-Service
 * @ClassName LLMLogServiceIMpl
 * @description:
 * @author: Levi
 * @create: 2025-01-23 21:32
 * @Version 1.0
 **/
@Service("llmLogService")
public class LLMLogServiceImpl extends ServiceImpl<LLMLogMapper, LLMLog> implements LLMLogService {
    @Resource
    private UserService userService;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private ThreatBusinessService threatBusinessService;
    @Resource
    private SensitiveApiService sensitiveApiService;
    @Resource
    private NginxLogEsClient nginxLogEsClient;

    @Override
    public Object theaatAnalysis(LLMRequest request, HttpServletRequest httpRequest) {
        String logId = request.getLogId();
        LLMLog llmLog = new LLMLog();
        String requestBody = request.getRequestBody();
        Integer llmType = request.getLlmType();
        llmLog.setAnaysisType(llmType);
        llmLog.setLogId(logId);
        //先根据logid取，通过logid取取LogDetail。把LogDetail设置为requestBody。如果没有logid就需要直接传requestBody字段
        if (StrUtil.isNotBlank(logId)) {
            switch (llmType) {
                case 1:
                    //威胁分析
                    ThreatDetailResponse threatDetail = threatBusinessService.getByRiskId(logId);

                    String logDetail = threatDetail.getLogRequest() + "\n\n" + threatDetail.getLogResponse();
                    if (ObjectUtil.isEmpty(logDetail)) {
                        return Result.fail("日志ID为空，请联系系统管理员核实-威胁");
                    }
                    requestBody = logDetail;
                    break;
                case 2:
                    //敏感分析
//                     SensitiveApiDetailResponse detail = sensitiveApiService.getDetail(logId);
//                     String apilog = detail.getLog();
//                     requestBody=apilog;

                    // 最近的一条涉敏的详情 原始日志，从es中查询
                    EsQueryDTO queryDTO = EsQueryDTO.builder()
                            .build()
                            .addQuery("_id", logId)
                            .orderBy("logTime", SortOrder.DESC);
                    EsNginxDTO nglog = nginxLogEsClient.queryOne(queryDTO);
                    if (ObjectUtil.isEmpty(nglog)) {
                        return Result.fail("日志ID为空，请联系系统管理员核实-涉敏");
                    }
                    String responseData = nglog.getResponseData();
                    requestBody = responseData;
                    break;

            }


        } else if (StrUtil.isNotBlank(requestBody)) {

        } else {
            return Result.fail("调用大模型出错，请联系系统管理员核实-参数");
        }
        llmLog.setAnaysisRequest(requestBody);
        String loginIdAsString = StpUtil.getLoginIdAsString();
        UserInfo userInfo = userInfoService.getByUsername(loginIdAsString).get();
        String clientIp = IpUtils.getClientIp(httpRequest);
        String s = "默认返回";

        llmLog.setAnaysisRequest(requestBody);

        llmLog.setUseUser(loginIdAsString + "-" + userInfo.getRealName());

        llmLog.setClientIp(clientIp);
        llmLog.setStatus(1);
        llmLog.setCreateTime(new Date());
        llmLog.setUpdateTime(new Date());
        this.save(llmLog);
        try {
            s = LLMUtil.threatAnalysis(request, requestBody);
        } catch (Exception e) {
            String message = e.getMessage();
            llmLog.setMsg(message);
            llmLog.setStatus(100);
            llmLog.setUpdateTime(new Date());
            this.saveOrUpdate(llmLog);
            return Result.fail("调用大模型出错，详情看日志，已经记录");
        }
        llmLog.setStatus(2);
        llmLog.setAnaysisResponse(s);
        llmLog.setUpdateTime(new Date());
        this.saveOrUpdate(llmLog);
        return Result.success(s);
    }
}