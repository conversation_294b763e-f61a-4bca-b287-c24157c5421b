package com.telecom.apigateway.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.OperationLogMapper;
import com.telecom.apigateway.model.entity.OperationLog;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.QueryOperationLogRequest;
import com.telecom.apigateway.model.vo.response.OperationLogResponse;
import com.telecom.apigateway.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
@Slf4j
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements OperationLogService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void recordLog(OperationLog operationLog) {
        this.save(operationLog);
        log.info("操作日志: [{}] 用户: {}, 操作: {}, 描述: {}, IP: {}",
                operationLog.getCreateTime(),
                operationLog.getOperationUser(),
                operationLog.getOperationType(),
                operationLog.getFormatDescription(),
                operationLog.getLoginIP());
    }

    @Override
    public void createLog(String username, OperationTypeEnum operationType, ResourceTypeEnum resourceType, String description, String ip) {
        OperationLog operationLog = OperationLog.builder()
                .logId(IdUtil.fastSimpleUUID())
                .operationType(operationType)
                .resourceType(resourceType)
                .formatDescription(description)
                .createTime(LocalDateTime.now())
                .operationUser(username)
                .loginIP(ip)
                .build();

        this.recordLog(operationLog);
    }

    @Override
    public Page<OperationLogResponse> queryPage(QueryOperationLogRequest request) {
        Page<OperationLogResponse> page = new Page<>(request.getPageNum(), request.getPageSize());

        String startTimeStr = null;
        String endTimeStr = null;

        if (request.getStartTime() != null) {
            startTimeStr = request.getStartTime().format(DATE_TIME_FORMATTER);
        }

        if (request.getEndTime() != null) {
            endTimeStr = request.getEndTime().format(DATE_TIME_FORMATTER);
        }

        return this.baseMapper.queryPage(
                page,
                request.getUsername(),
                request.getOperationType(),
                startTimeStr,
                endTimeStr,
                request.getIp()
        );
    }
}
