package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.PermissionMapper;
import com.telecom.apigateway.model.entity.MenuPermission;
import com.telecom.apigateway.service.MenuPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class MenuPermissionServiceImpl extends ServiceImpl<PermissionMapper, MenuPermission> implements MenuPermissionService {

//    private final RolePermissionService rolePermissionService;

    @Override
    public List<MenuPermission> getPermissionsByRoleId(Long roleId) {
//        // 1. 获取角色-权限关联关系
//        List<RolePermission> rolePermissions = rolePermissionService.lambdaQuery()
//                .eq(RolePermission::getRoleId, roleId)
//                .list();
//
//        if (rolePermissions.isEmpty()) {
//            return Collections.emptyList();
//        }
//
//        // 2. 获取权限列表
//        List<Long> permissionIds = rolePermissions.stream()
//                .map(RolePermission::getPermissionId)
//                .collect(Collectors.toList());
//
//        return this.lambdaQuery()
//                .in(Permission::getId, permissionIds)
//                .eq(Permission::getDeleted, false)
//                .orderByAsc(Permission::getSort)
//                .list();
        return Collections.emptyList();
    }

    @Override
    public List<MenuPermission> getByCodes(Collection<String> permissionCodes) {
        if (permissionCodes.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(MenuPermission::getCode, permissionCodes)
                .eq(MenuPermission::getDeleted, false)
                .orderByAsc(MenuPermission::getCategorySort)
                .orderByAsc(MenuPermission::getMenuSort)
                .list();
    }

    @Override
    public List<MenuPermission> getByParentCodes(Collection<String> parentCode) {
        if (parentCode.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(MenuPermission::getParentCode, parentCode)
                .eq(MenuPermission::getDeleted, false)
                .list();
    }

    @Override
    public List<MenuPermission> getAllPermissionByCodes(Collection<String> permissionCodes) {
        if (permissionCodes.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .and(query -> query.in(MenuPermission::getCode, permissionCodes)
                        .or()
                        .in(MenuPermission::getParentCode, permissionCodes))
                .eq(MenuPermission::getDeleted, false)
                .orderByAsc(MenuPermission::getCategorySort)
                .orderByAsc(MenuPermission::getMenuSort)
                .list();
    }

    @Override
    public List<MenuPermission> getDisplayPermissionsByRoleId() {
        return this.lambdaQuery()
                .isNull(MenuPermission::getParentCode)
                .eq(MenuPermission::getDeleted, false)
                .orderByAsc(MenuPermission::getCategorySort)
                .orderByAsc(MenuPermission::getMenuSort)
                .list();
    }
}