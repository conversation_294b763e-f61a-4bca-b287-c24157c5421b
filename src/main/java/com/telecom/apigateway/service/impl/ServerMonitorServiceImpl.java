package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.client.ServerMonitorEsClient;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.mapper.ServerNodeMapper;
import com.telecom.apigateway.model.dto.es.ServerMonitorDTO;
import com.telecom.apigateway.model.entity.ServerNode;
import com.telecom.apigateway.model.enums.*;
import com.telecom.apigateway.model.vo.ServerMonitorVO;
import com.telecom.apigateway.model.vo.request.UpdateServerNodeRequest;
import com.telecom.apigateway.service.ServerMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务器监控服务实现
 */
@Slf4j
@Service
public class ServerMonitorServiceImpl extends ServiceImpl<ServerNodeMapper, ServerNode> implements ServerMonitorService {

    @Autowired
    private ServerMonitorEsClient serverMonitorEsClient;

    @Override
    public boolean addServerNode(ServerNode serverNode) {
        serverNode.setCreateTime(LocalDateTime.now());
        serverNode.setUpdateTime(LocalDateTime.now());
        return this.save(serverNode);
    }

    @Override
    public boolean updateServerNode(UpdateServerNodeRequest request) {
        ServerNode serverNode = getServerNode(request.getHostId());
        ((ServerMonitorServiceImpl) AopContext.currentProxy()).update(serverNode, request);
        return true;
    }

    @Override
    public boolean deleteServerNodeById(Long id) {
        return this.removeById(id);
    }

    @Override
    public List<ServerNode> getAllServerNodes() {
        return this.list(new LambdaQueryWrapper<ServerNode>().orderByDesc(ServerNode::getUpdateTime));
    }

    @Override
    public List<ServerMonitorVO> getAllServerMonitorInfo() {
        // 1. 从ES获取所有有监控数据的主机ID
        List<String> esHostIds = serverMonitorEsClient.getAllHostIds();
        if (esHostIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 从数据库获取已存在的节点信息
        List<ServerNode> existingNodes = this.list();
        Map<String, ServerNode> existingNodeMap = existingNodes.stream()
                .collect(Collectors.toMap(ServerNode::getHostId, node -> node, (a, b) -> a));

        // 3. 自动注册未在数据库中存在的节点
        List<ServerNode> newNodes = new ArrayList<>();
        for (String hostId : esHostIds) {
            if (!existingNodeMap.containsKey(hostId)) {
                // 获取节点最新数据，用于提取基本信息
                ServerMonitorDTO latestData = serverMonitorEsClient.getLatestByHostId(hostId);
                if (latestData != null && latestData.getHost() != null) {
                    ServerNode newNode = new ServerNode();
                    newNode.setHostId(hostId);
                    newNode.setRemarks(latestData.getHost().getHostname()); // 使用hostname作为默认备注
                    newNode.setHostName(latestData.getHost().getHostname());
                    
                    // 设置CPU总数和内存总量（如果有）
                    if (latestData.getCpu() != null && latestData.getCpu().getCounts() != null) {
                        newNode.setCpuTotal(latestData.getCpu().getCounts());
                    }
                    if (latestData.getMemory() != null && latestData.getMemory().getTotal() != null) {
                        newNode.setMemoryTotal(latestData.getMemory().getTotal());
                    }
                    
                    newNode.setCreateTime(LocalDateTime.now());
                    newNode.setUpdateTime(LocalDateTime.now());
                    
                    newNodes.add(newNode);
                    existingNodeMap.put(hostId, newNode);
                }
            }
        }
        
        // 批量保存新节点
        if (!newNodes.isEmpty()) {
            this.saveBatch(newNodes);
            log.info("自动注册了{}个新节点", newNodes.size());
        }

        // 4. 批量获取每个节点的前两条监控数据（用于计算网络速率）
        Map<String, List<ServerMonitorDTO>> monitorDataMap = serverMonitorEsClient.getLatestTwoByHostIds(esHostIds);

        // 5. 组装返回数据
        List<ServerMonitorVO> resultList = new ArrayList<>();
        for (String hostId : esHostIds) {
            ServerNode node = existingNodeMap.get(hostId);
            List<ServerMonitorDTO> dataList = monitorDataMap.get(hostId);
            
            // 如果找不到监控数据，跳过
            if (node == null || dataList == null || dataList.isEmpty()) {
                continue;
            }

            ServerMonitorVO vo = convertToVO(node, dataList);
            resultList.add(vo);
        }

        return resultList;
    }

    @Override
    public ServerMonitorVO getServerMonitorInfoByHostId(String hostId) {
        // 1. 查询节点基本信息
        ServerNode node = getServerNode(hostId);

        // 2. 获取最近两条监控数据
        List<ServerMonitorDTO> dataList = serverMonitorEsClient.getLatestTwoByHostId(hostId);
        if (dataList.isEmpty()) {
            return null;
        }
        
        // 3. 如果节点不存在于数据库中，自动注册
        if (node == null) {
            ServerMonitorDTO latestData = dataList.get(0);
            if (latestData != null && latestData.getHost() != null) {
                node = new ServerNode();
                node.setHostId(hostId);
                node.setHostName(latestData.getHost().getHostname());
                node.setRemarks(latestData.getHost().getHostname()); // 使用hostname作为默认备注
                
                // 设置CPU总数和内存总量（如果有）
                if (latestData.getCpu() != null && latestData.getCpu().getCounts() != null) {
                    node.setCpuTotal(latestData.getCpu().getCounts());
                }
                if (latestData.getMemory() != null && latestData.getMemory().getTotal() != null) {
                    node.setMemoryTotal(latestData.getMemory().getTotal());
                }
                
                node.setCreateTime(LocalDateTime.now());
                node.setUpdateTime(LocalDateTime.now());
                
                this.save(node);
                log.info("自动注册新节点: {}", hostId);
            } else {
                return null;
            }
        }

        // 4. 组装返回数据
        return convertToVO(node, dataList);
    }

    public ServerNode getServerNode(String hostId) {
        return this.getOne(new LambdaQueryWrapper<ServerNode>()
                .eq(ServerNode::getHostId, hostId));
    }
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.FULL_UPDATE,
            resourceType = ResourceTypeEnum.SERVER_MONITOR,
            spelArgs = {"#{#node.hostName}", "#{#result}", }
    )
    public ServerNode update(ServerNode node, UpdateServerNodeRequest request) {
        node.setHostName(request.getServerName());
        node.setRemarks(request.getRemarks());
        node.setUpdateTime(LocalDateTime.now());
        this.updateById(node);
        return node;
    }

    /**
     * 将数据库和ES数据转换为VO对象
     *
     * @param node 服务器节点数据
     * @param dataList 监控数据列表（降序排列，最多2条）
     * @return VO对象
     */
    private ServerMonitorVO convertToVO(ServerNode node, List<ServerMonitorDTO> dataList) {
        ServerMonitorVO vo = new ServerMonitorVO();
        
        // 设置基础信息
        vo.setHostId(node.getHostId());
        vo.setRemarks(node.getRemarks());
        vo.setCpuTotal(node.getCpuTotal());
        vo.setMemoryTotal(node.getMemoryTotal());
        vo.setHostName(node.getHostName());
        
        // 获取最新的监控数据
        ServerMonitorDTO latestData = dataList.get(0);
        
        // 设置CPU、内存、负载信息
        vo.setCpuPercent(latestData.getCpu().getPercent());
        vo.setMemoryPercent(latestData.getMemory().getUsedPercent());
        vo.setLoad5(latestData.getLoad().getLoad5());
        
        // 设置时间戳
        if (latestData.getTimestamp() != null) {
            vo.setTimestamp(LocalDateTime.ofInstant(Instant.parse(latestData.getTimestamp()), ZoneId.systemDefault()));
        }
        
        // 计算网络速率
        calculateNetworkRate(vo, dataList);
        
        return vo;
    }

    /**
     * 计算网络速率（KB/s）
     *
     * @param vo VO对象
     * @param dataList 监控数据列表（按时间降序排列，最多2条）
     */
    private void calculateNetworkRate(ServerMonitorVO vo, List<ServerMonitorDTO> dataList) {
        // 默认设置为0
        vo.setIngressKbps(0.0);
        vo.setEgressKbps(0.0);
        
        // 需要至少两条数据才能计算速率
        if (dataList.size() < 2) {
            return;
        }
        
        ServerMonitorDTO latest = dataList.get(0);
        ServerMonitorDTO previous = dataList.get(1);
        
        // 确保数据完整
        if (latest.getNetwork() == null || latest.getNetwork().getIngress() == null ||
            latest.getNetwork().getEgress() == null || previous.getNetwork() == null ||
            previous.getNetwork().getIngress() == null || previous.getNetwork().getEgress() == null ||
            latest.getTimestamp() == null || previous.getTimestamp() == null) {
            return;
        }
        
        // 计算时间差（秒）
        long timeDiffMs = Instant.parse(latest.getTimestamp()).toEpochMilli() - Instant.parse(previous.getTimestamp()).toEpochMilli();
        double timeDiffSec = timeDiffMs / 1000.0;
        
        // 防止除零错误
        if (timeDiffSec <= 0) {
            return;
        }
        
        // 计算字节差
        long ingressBytesDiff = latest.getNetwork().getIngress().getBytes() - previous.getNetwork().getIngress().getBytes();
        long egressBytesDiff = latest.getNetwork().getEgress().getBytes() - previous.getNetwork().getEgress().getBytes();
        
        // 计算速率（KB/s）= 字节差 / 时间差（秒）/ 1024
        double ingressKbps = ingressBytesDiff / timeDiffSec / 1024.0;
        double egressKbps = egressBytesDiff / timeDiffSec / 1024.0;
        
        // 设置结果（保证非负）
        vo.setIngressKbps(Math.max(0, ingressKbps));
        vo.setEgressKbps(Math.max(0, egressKbps));
    }
} 