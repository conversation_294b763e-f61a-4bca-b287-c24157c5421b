package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.IpGroupMapper;
import com.telecom.apigateway.model.entity.IpGroup;
import com.telecom.apigateway.service.IpGroupService;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Repository
public class IpGroupServiceImpl extends ServiceImpl<IpGroupMapper, IpGroup> implements IpGroupService {
    @Override
    public List<IpGroup> list() {
        return this.lambdaQuery()
                .eq(IpGroup::getDeleted, false)
                .list();
    }

    @Override
    public Optional<IpGroup> getOptByGroupId(String groupId) {
        return this.lambdaQuery()
                .eq(IpGroup::getGroupId, groupId)
                .eq(IpGroup::getDeleted, false)
                .oneOpt();
    }

    @Override
    public List<IpGroup> getByGroupIds(List<String> groupIds) {
        if (groupIds != null && !groupIds.isEmpty()) {
            return this.lambdaQuery()
                    .in(IpGroup::getGroupId, groupIds)
                    .eq(IpGroup::getDeleted, false)
                    .list();
        }
        return Collections.emptyList();
    }
}
