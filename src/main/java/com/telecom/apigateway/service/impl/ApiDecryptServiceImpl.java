package com.telecom.apigateway.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.ApiDecryptMapper;
import com.telecom.apigateway.model.entity.ApiDecrypt;
import com.telecom.apigateway.service.ApiDecryptService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class ApiDecryptServiceImpl extends ServiceImpl<ApiDecryptMapper, ApiDecrypt> implements ApiDecryptService {
    @Override
    public Optional<ApiDecrypt> getByApiId(String apiId) {
        return this.lambdaQuery().eq(ApiDecrypt::getApiId, apiId).oneOpt();
    }

    @Override
    public List<ApiDecrypt> getByApiIds(List<String> apiIds) {
        if (apiIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().in(ApiDecrypt::getApiId, apiIds).list();
    }

    @Override
    public void updateApiId(List<String> fromApiIds, String toApiId) {
        if (CollUtil.isEmpty(fromApiIds) || StrUtil.isBlank(toApiId)) {
            return;
        }
        lambdaUpdate()
                .set(ApiDecrypt::getApiId, toApiId)
                .in(ApiDecrypt::getApiId, fromApiIds)
                .update();
    }

    @Override
    public void deleteByApiId(String apiId) {
        this.lambdaUpdate().eq(ApiDecrypt::getApiId, apiId).remove();
    }
}
