package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.ApiMergeMapper;
import com.telecom.apigateway.model.entity.ApiMerge;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class ApiMergeService extends ServiceImpl<ApiMergeMapper, ApiMerge> {

    public ApiMerge saveOne(ApiMerge apiMerge) {
        this.save(apiMerge);
        return apiMerge;
    }

    public ApiMerge updateOne(ApiMerge entity) {
        this.updateById(entity);
        return entity;
    }

    public ApiMerge detail(String id) {
        ApiMerge byId = getById(id);
        return byId;
    }

    public void updateStatusById(String id, boolean enable) {
        LocalDateTime now = LocalDateTime.now();
        lambdaUpdate()
                .set(ApiMerge::getEnable, enable)
                .set(ApiMerge::getUpdateUser, StpUtil.getLoginIdAsString())
                .set(ApiMerge::getUpdateTime, now)
                .set(enable, ApiMerge::getEnableTime, now)
                .eq(ApiMerge::getId, id)
                .update();
    }

    public boolean canUpdate(String id) {
        // 启用一次后,  无论如何都再也不能编辑了,  是这个意思吧
        ApiMerge byId = getById(id);

        return byId.getEditable();
    }

    public void deleteOne(String id) {
        lambdaUpdate()
                .set(ApiMerge::getDeleted, true)
                .set(ApiMerge::getUpdateUser, StpUtil.getLoginIdAsString())
                .set(ApiMerge::getUpdateTime, LocalDateTime.now())
                .eq(ApiMerge::getId, id)
                .update();
    }

    public ApiMerge getByName(String name) {
        return lambdaQuery()
                .eq(ApiMerge::getName, name.trim())
                .eq(ApiMerge::getDeleted, false)
                .last("limit 1")
                .one();
    }

    public List<ApiMerge> getByIds(List<String> ids) {
        return lambdaQuery()
                .eq(ApiMerge::getDeleted, false)
                .in(ApiMerge::getId, ids)
                .list();
    }
}
