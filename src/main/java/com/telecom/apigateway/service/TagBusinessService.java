package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.model.vo.response.AddTagResponse;
import com.telecom.apigateway.model.vo.response.TagOptionResponse;
import com.telecom.apigateway.model.vo.response.TagResponse;

import java.util.List;

public interface TagBusinessService {
    AddTagResponse addTag(String userId, AddOrUpdateTag tag);

    Page<TagResponse> getTagsLikeName(QueryTagRequest request);

    Page<TagResponse> getTags(BasePageQueryRequest request);

    void deleteTag(DeleteTagRequest request);

    void deleteTags(BatchDeleteTagRequest request);

    void updateTag(AddOrUpdateTag tag);

    void unlinkCurrentTagAllApi(DeleteTagRequest request);

    void linkApi(String userId, BatchLinkTagRequest request);

    List<TagOptionResponse> getOption();
}
