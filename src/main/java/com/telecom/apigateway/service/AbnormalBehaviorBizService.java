package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.DeleteLogAnnotation;
import com.telecom.apigateway.common.InsertLogAnnotation;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.UpdateLogAnnotation;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRule;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleCondition;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleTrigger;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.Rule;
import com.telecom.apigateway.model.entity.SensitiveRule;
import com.telecom.apigateway.model.entity.UserInfo;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.AbRuleQueryRequest;
import com.telecom.apigateway.model.vo.request.AddAbRuleRequest;
import com.telecom.apigateway.model.vo.request.QueryAbRequest;
import com.telecom.apigateway.model.vo.request.UpdateAbRuleRequest;
import com.telecom.apigateway.model.vo.request.UpdateSystemAbRuleRequest;
import com.telecom.apigateway.model.vo.response.QueryAbResponse;
import com.telecom.apigateway.model.vo.response.QueryCustomAbrResponse;
import com.telecom.apigateway.utils.IpUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Service
public class AbnormalBehaviorBizService {

    @Resource
    private AbnormalBehaviorRuleService abRuleService;
    @Resource
    private AbnormalBehaviorRuleTriggerService abrTriggerService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private ApplicationService applicationService;
    @Resource
    private SensitiveRuleService sensitiveRuleService;
    @Resource
    private RuleService ruleService;
    @Resource
    private UserInfoService userInfoService;

    public Page<QueryAbResponse> pageList(QueryAbRequest ruleRequest) {
        ruleRequest.format();

        Set<String> assetIds = new HashSet<>();
        List<ApiInfo> apis = apiInfoService.list();
        List<Application> apps = applicationService.lambdaQuery().isNull(Application::getParentId).list();

        if (StrUtil.isNotBlank(ruleRequest.getAssetName())) {
            List<String> apiIds = apis.stream().filter(
                    api -> api.getName().contains(ruleRequest.getAssetName())
            ).map(ApiInfo::getId).collect(Collectors.toList());
            assetIds.addAll(apiIds);

            List<String> appIds = apps.stream()
                    .filter(app -> app.getName().contains(ruleRequest.getAssetName()))
                    .map(Application::getApplicationId).collect(Collectors.toList());
            assetIds.addAll(appIds);
            ruleRequest.setAssetId(assetIds);
        }
        if (assetIds.isEmpty()) {
            assetIds.add("-1");
        }

        Page<AbnormalBehaviorRuleTrigger> abrtPage = abrTriggerService.queryPage(ruleRequest);
        List<AbnormalBehaviorRuleTrigger> abrtList = abrtPage.getRecords();

        Page<QueryAbResponse> pageData = new Page<>();
        pageData.setTotal(abrtPage.getTotal());
        pageData.setSize(abrtPage.getSize());
        pageData.setCurrent(abrtPage.getCurrent());
        pageData.setPages(abrtPage.getPages());
        pageData.setRecords(abrtEntity2Dto(abrtList, apis, apps));
        return pageData;
    }

    private List<QueryAbResponse> abrtEntity2Dto(List<AbnormalBehaviorRuleTrigger> abrtList,
                                                 List<ApiInfo> apis,
                                                 List<Application> apps) {
        return abrtList.stream().map(abrt -> {
            QueryAbResponse response = new QueryAbResponse();
            response.setId(abrt.getId());
            response.setClientIp(abrt.getClientIp());
            response.setClientPort(abrt.getClientPort());
            response.setClientRegion(IpUtils.getRegion(abrt.getClientIp(), "-"));
            response.setRuleName(abrt.getRuleName());
            response.setAbnormalType(abrt.getAbnormalType());
            response.setAbnormalDetail(abrt.getAbnormalDetail());
            response.setPolicy(abrt.getPolicy());
            response.setPolicyDetail(abrt.getPolicyDetail());
            response.setCreateTime(abrt.getCreateTime());
            response.setCreateTime(abrt.getCreateTime());
            response.setIsActive(abrt.getValid());

            if (abrt.getAssetType() == AbnormalBehaviorRuleEnum.AssetType.API) {
                Optional<ApiInfo> matched =
                        apis.stream().filter(api -> api.getId().equals(abrt.getAssetId())).findFirst();
                matched.ifPresent(apiInfo -> response.setAsset(
                        new QueryAbResponse.Asset(
                                AbnormalBehaviorRuleEnum.AssetType.API,
                                apiInfo.getId(),
                                apiInfo.getName(),
                                apiInfo.getHttpMethod(),
                                apiInfo.getUrl()
                        )));
            }
            if (abrt.getAssetType() == AbnormalBehaviorRuleEnum.AssetType.APPLICATION) {
                Optional<Application> matched =
                        apps.stream().filter(app -> app.getApplicationId().equals(abrt.getAssetId())).findFirst();
                matched.ifPresent(application -> response.setAsset(
                        new QueryAbResponse.Asset(
                                AbnormalBehaviorRuleEnum.AssetType.APPLICATION,
                                application.getApplicationId(),
                                application.getName(),
                                "", // 请求方式 no
                                application.getHttpHost()
                        )));
            }
            return response;
        }).collect(Collectors.toList());
    }

    @InsertLogAnnotation(
            resourceType = ResourceTypeEnum.ABNORMAL_BEHAVIOR,
            displayName = "#ruleRequest.name"
    )
    public String addRule(AddAbRuleRequest ruleRequest) {
        ruleRequest.format();
        validRule(ruleRequest);
        AbnormalBehaviorRule rule = ruleRequest.toRule();
        abRuleService.save(rule);
        return rule.getId();
    }

    private void validRule(AddAbRuleRequest ruleRequest) {
        ruleRequest.valid();
        // 验证请求参数应用是否有效
        if (ruleRequest.getAssetType() == AbnormalBehaviorRuleEnum.AssetType.APPLICATION) {
            List<Application> rootApps = applicationService.lambdaQuery()
                    .isNull(Application::getParentId)
                    .eq(Application::getDeleted, false)
                    .list();
            for (String applicationId : ruleRequest.getAssetId()) {
                Optional<Application> rootApp = rootApps.stream()
                        .filter(app -> app.getApplicationId().equals(applicationId))
                        .findFirst();
                if (!rootApp.isPresent()) {
                    throw new BusinessException(ResultCodeEnum.ABNORMAL_BEHAVIOR_RULE_CHECK_ERROR, "不能选择非根应用");
                }
            }
        }
    }

    private void validUpdateRule(UpdateAbRuleRequest ruleRequest) {
        validRule(ruleRequest);
        AbnormalBehaviorRule rule = abRuleService.getById(ruleRequest.getId());
        if (rule == null) {
            throw new BusinessException(ResultCodeEnum.ABNORMAL_BEHAVIOR_RULE_CHECK_ERROR, "规则不存在");
        }
        if (ruleRequest.getAbnormalType() != AbnormalBehaviorRuleEnum.AbnormalType.VISIT
                && ruleRequest.getPolicy() == AbnormalBehaviorRuleEnum.Policy.BLOCK) {
            throw new BusinessException(ResultCodeEnum.ABNORMAL_BEHAVIOR_RULE_CHECK_ERROR, "非访问规则无法选择拦截");
        }
        if (ruleRequest.getPolicy() == AbnormalBehaviorRuleEnum.Policy.BANNED
                && (ruleRequest.getPolicyDuration() == null || ruleRequest.getPolicyDuration() <= 0)) {
            throw new BusinessException(ResultCodeEnum.ABNORMAL_BEHAVIOR_RULE_CHECK_ERROR, "封禁策略需要设置封禁时长>0");
        }
    }

    /**
     * 修改规则
     * <p>1. </p>
     */
    @UpdateLogAnnotation(
            resourceType = ResourceTypeEnum.ABNORMAL_BEHAVIOR,
            queryMethod = "@abnormalBehaviorRuleService.getById(#ruleRequest.id)",
            displayName = "#{#result.name}"
    )
    public AbnormalBehaviorRule updateRule(UpdateAbRuleRequest ruleRequest) {
        validUpdateRule(ruleRequest);
        ruleRequest.valid();
        AbnormalBehaviorRule updateRule = ruleRequest.toRule();
        AbnormalBehaviorRule oldRule = abRuleService.getById(updateRule.getId());
        updateRule.setCreateUser(oldRule.getCreateUser());
        abRuleService.updateById(updateRule);
        return abRuleService.getById(updateRule.getId());
    }

    @UpdateLogAnnotation(
            resourceType = ResourceTypeEnum.ABNORMAL_BEHAVIOR,
            queryMethod = "@abnormalBehaviorRuleService.getById(#ruleRequest.id)",
            displayName = "#{#result.name}"
    )
    public AbnormalBehaviorRule updateRule(UpdateSystemAbRuleRequest ruleRequest) {
        AbnormalBehaviorRule rule = abRuleService.getById(ruleRequest.getId());
        if (rule == null) {
            throw new BusinessException(ResultCodeEnum.ABNORMAL_BEHAVIOR_RULE_CHECK_ERROR, "规则不存在");
        }
        if (rule.getAbnormalType() != AbnormalBehaviorRuleEnum.AbnormalType.VISIT
                && ruleRequest.getPolicy() == AbnormalBehaviorRuleEnum.Policy.BLOCK) {
            throw new BusinessException(ResultCodeEnum.ABNORMAL_BEHAVIOR_RULE_CHECK_ERROR, "非[访问]规则无法选择拦截");
        }
        if (ruleRequest.getPolicy() == AbnormalBehaviorRuleEnum.Policy.BANNED
                && (ruleRequest.getPolicyDuration() == null || ruleRequest.getPolicyDuration() <= 0)) {
            throw new BusinessException(ResultCodeEnum.ABNORMAL_BEHAVIOR_RULE_CHECK_ERROR, "封禁策略需要设置封禁时长>0");
        }
        AbnormalBehaviorRule updateRule = ruleRequest.toSystemRule();
        updateRule.setUpdateTime(LocalDateTime.now());
        updateRule.setUpdateUser(StpUtil.getLoginIdAsString());

        AbnormalBehaviorRule oldRule = abRuleService.getById(updateRule.getId());
        updateRule.setCreateUser(oldRule.getCreateUser());
        abRuleService.updateById(updateRule);
        return abRuleService.getById(updateRule.getId());
    }

    /**
     * 禁用策略
     */
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.ABNORMAL_BEHAVIOR,
            description = "{resourceName}: 解封异常行为 #{@abnormalBehaviorRuleTriggerService.getById(#triggerId).ruleName}"
    )
    public void disableTrigger(String triggerId) {
        // AbnormalBehaviorRuleTrigger t = new AbnormalBehaviorRuleTrigger();
        // t.setId(triggerId);
        // t.setValid(false);
        // t.setUpdateTime(LocalDateTime.now());
        // t.setUpdateUser(StpUtil.getLoginIdAsString());
        // t.setRemark("人工解封");
        // abrTriggerService.updateById(t);
        AbnormalBehaviorRuleTrigger abrt = abrTriggerService.getById(triggerId);
        if (abrt == null) {
            return;
        }
        abrTriggerService.lambdaUpdate()
                .set(AbnormalBehaviorRuleTrigger::getValid, false)
                .set(AbnormalBehaviorRuleTrigger::getUpdateTime, LocalDateTime.now())
                .set(AbnormalBehaviorRuleTrigger::getUpdateUser, StpUtil.getLoginIdAsString())
                .set(AbnormalBehaviorRuleTrigger::getRemark, "人工解封")
                .eq(AbnormalBehaviorRuleTrigger::getClientIp, abrt.getClientIp())
                .eq(AbnormalBehaviorRuleTrigger::getValid, true)
                .update();
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.ABNORMAL_BEHAVIOR,
            description = "{resourceName}: 启用规则 #{@abnormalBehaviorRuleService.getRuleNamesByIds(#ruleIds)}"
    )
    public void enableRule(List<String> ruleIds) {
        if (CollUtil.isEmpty(ruleIds)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        abRuleService.lambdaUpdate()
                .set(AbnormalBehaviorRule::getEnable, true)
                .in(AbnormalBehaviorRule::getId, ruleIds)
                .set(AbnormalBehaviorRule::getUpdateTime, now)
                .set(AbnormalBehaviorRule::getUpdateUser, StpUtil.getLoginIdAsString())
                .update();

    }

    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.ABNORMAL_BEHAVIOR,
            description = "{resourceName}: 禁用规则 #{@abnormalBehaviorRuleService.getRuleNamesByIds(#ruleIds)}"
    )
    public void disableRule(List<String> ruleIds) {
        if (CollUtil.isEmpty(ruleIds)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        abRuleService.lambdaUpdate()
                .set(AbnormalBehaviorRule::getEnable, false)
                .set(AbnormalBehaviorRule::getUpdateTime, now)
                .set(AbnormalBehaviorRule::getUpdateUser, StpUtil.getLoginIdAsString())
                .in(AbnormalBehaviorRule::getId, ruleIds)
                .update();
        // abrTriggerService.lambdaUpdate()
        //         .set(AbnormalBehaviorRuleTrigger::getValid, false)
        //         .set(AbnormalBehaviorRuleTrigger::getUpdateUser, StpUtil.getLoginIdAsString())
        //         .set(AbnormalBehaviorRuleTrigger::getUpdateTime, now)
        //         .set(AbnormalBehaviorRuleTrigger::getRemark, "人工禁用")
        //         .in(AbnormalBehaviorRuleTrigger::getRuleId, ruleIds)
        //         .update();
    }

    @Transactional
    @DeleteLogAnnotation(
            resourceType = ResourceTypeEnum.ABNORMAL_BEHAVIOR,
            displayName = "@abnormalBehaviorRuleService.getRuleNamesByIds(#ruleIds)"
    )
    public void deleteRule(List<String> ruleIds) {
        if (CollUtil.isEmpty(ruleIds)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        abRuleService.lambdaUpdate()
                .set(AbnormalBehaviorRule::getIsDeleted, true)
                .set(AbnormalBehaviorRule::getUpdateTime, now)
                .set(AbnormalBehaviorRule::getUpdateUser, StpUtil.getLoginIdAsString())
                .in(AbnormalBehaviorRule::getId, ruleIds)
                .update();
        // abrTriggerService.lambdaUpdate()
        //         .set(AbnormalBehaviorRuleTrigger::getValid, false)
        //         .set(AbnormalBehaviorRuleTrigger::getUpdateUser, StpUtil.getLoginIdAsString())
        //         .set(AbnormalBehaviorRuleTrigger::getUpdateTime, now)
        //         .set(AbnormalBehaviorRuleTrigger::getRemark, "人工删除")
        //         .in(AbnormalBehaviorRuleTrigger::getRuleId, ruleIds)
        //         .update();
    }

    public Page<QueryCustomAbrResponse> pageCustomRule(AbRuleQueryRequest request) {
        LambdaQueryChainWrapper<AbnormalBehaviorRule> query = abRuleService.lambdaQuery();
        if (StrUtil.isNotBlank(request.getName())) {
            query.like(AbnormalBehaviorRule::getName, request.getName().trim());
        }

        List<UserInfo> users = userInfoService.list();
        // map username, realName
        Map<String, String> userMap = users.stream().collect(Collectors.toMap(UserInfo::getUsername,
                UserInfo::getRealName));

        List<Rule> rules = ruleService.list();
        List<SensitiveRule> sensitiveRules = sensitiveRuleService.list();

        Page<AbnormalBehaviorRule> page = query
                .eq(request.getEnable() != null, AbnormalBehaviorRule::getEnable, request.getEnable())
                .in(CollUtil.isNotEmpty(request.getAbnormalType()), AbnormalBehaviorRule::getAbnormalType,
                        request.getAbnormalType())
                .eq(AbnormalBehaviorRule::getSource, AbnormalBehaviorRuleEnum.Source.CUSTOM)
                .eq(AbnormalBehaviorRule::getIsDeleted, false)
                .between(request.getStartTime() != null && request.getEndTime() != null,
                        AbnormalBehaviorRule::getUpdateTime, request.getStartTime(), request.getEndTime())
                // 1-100的整数，数字越小，代表这条规则的执行优先级越高：相同优先级下，创建时间越晚，优先级越高
                .orderByAsc(AbnormalBehaviorRule::getPriority)
                .orderByDesc(AbnormalBehaviorRule::getCreateTime)
                .page(new Page<>(request.getPageNum(), request.getPageSize()));

        Page<QueryCustomAbrResponse> resultPage = new Page<>();
        resultPage.setTotal(page.getTotal());
        resultPage.setPages(page.getPages());
        resultPage.setCurrent(page.getCurrent());
        resultPage.setSize(page.getSize());
        resultPage.setRecords(page.getRecords().stream().map(r ->
                QueryCustomAbrResponse.builder()
                        .id(r.getId())
                        .name(r.getName())
                        .abnormalType(r.getAbnormalType())
                        .assetType(r.getAssetType())
                        .assetIds(r.getAssetIds())
                        .abnormalOperation(r.getAbnormalOperation())
                        .abnormalDuration(r.getAbnormalDuration())
                        .abnormalThreshold(r.getAbnormalThreshold())
                        .condition(r.getCondition())
                        .conditionDesc(getConditionDesc(r, rules, sensitiveRules))
                        .policy(r.getPolicy())
                        .policyDetail(r.getPolicyDetail())
                        .policyDuration(r.getPolicyDuration())
                        .priority(r.getPriority())
                        .enable(r.getEnable())
                        .createTime(r.getCreateTime())
                        .updateTime(r.getUpdateTime())
                        .updateUser(userMap.getOrDefault(r.getUpdateUser(), r.getUpdateUser()))
                        .build()
        ).collect(Collectors.toList()));
        return resultPage;
    }

    public String getConditionDesc(AbnormalBehaviorRule abr, List<Rule> riskRules, List<SensitiveRule> sensitiveRules) {
        StringBuilder desc = new StringBuilder("经过" +
                abr.getAbnormalDuration() +
                "秒内达到" +
                abr.getAbnormalThreshold() +
                "次;");
        if (abr.getCondition() != null) {
            for (AbnormalBehaviorRuleCondition condition : abr.getCondition()) {
                desc.append(condition.getTarget().getDesc())
                        .append("为");
                switch (condition.getTarget()) {
                    case method:
                    case header:
                        desc.append(condition.getOperation().getDesc());
                        desc.append(condition.getValue());
                        break;
                    case sensitiveRule:
                        desc.append(condition.getOperation().getDesc());
                        String[] sensitiveRuleIds = condition.getValue().split(",");
                        for (String sensitiveRuleId : sensitiveRuleIds) {
                            sensitiveRules.stream()
                                    .filter(sensitiveRule -> sensitiveRule.getId().equals(sensitiveRuleId))
                                    .findFirst()
                                    .ifPresent(sensitiveRule -> desc.append(sensitiveRule.getName()).append(","));
                        }
                        break;
                    case riskRule:
                        desc.append(condition.getOperation().getDesc());
                        String[] riskTypes = condition.getValue().split(",");
                        for (String riskType : riskTypes) {
                            riskRules.stream()
                                    .filter(riskRule -> riskRule.getType().equals(riskType))
                                    .findFirst()
                                    .ifPresent(riskRule -> desc.append(riskRule.getAttackType()).append(","));
                        }
                        break;
                    case errorCode:
                        desc.append(condition.getOperation().getDesc());
                        desc.append(condition.getValue()).append(",");
                        break;
                }
            }
        }
        return desc.toString();
    }
}
