package com.telecom.apigateway.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.SensitiveWhiteListMapper;
import com.telecom.apigateway.model.entity.SensitiveWhiteList;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-15
 */
@Service
public class SensitiveWhiteListService extends ServiceImpl<SensitiveWhiteListMapper, SensitiveWhiteList> {
    public void addWhitelistApi(String id, String[] apiIds) {
        // 先删除
        this.lambdaUpdate()
                .eq(SensitiveWhiteList::getSensitiveRuleId, id)
                .remove();
        if (apiIds == null) {
            return;
        }
        // 再批量插入
        for (String apiId : apiIds) {
            SensitiveWhiteList sensitiveWhiteList = new SensitiveWhiteList();
            sensitiveWhiteList.setApiId(apiId);
            sensitiveWhiteList.setSensitiveRuleId(id);
            this.save(sensitiveWhiteList);
        }
    }

    public List<SensitiveWhiteList> getBySensitiveRuleId(String id) {
        return this.lambdaQuery()
                .eq(SensitiveWhiteList::getSensitiveRuleId, id)
                .list();
    }

    public void deleteByApiId(String apiId) {
        this.lambdaUpdate()
                .eq(SensitiveWhiteList::getApiId, apiId)
                .remove();
    }

    public void updateApiId(List<String> fromApiIds, String toApiId) {
        if (CollUtil.isEmpty(fromApiIds) || StrUtil.isBlank(toApiId)) {
            return;
        }
        this.lambdaUpdate()
                .in(SensitiveWhiteList::getApiId, fromApiIds)
                .set(SensitiveWhiteList::getApiId, toApiId)
                .update();
    }
}
