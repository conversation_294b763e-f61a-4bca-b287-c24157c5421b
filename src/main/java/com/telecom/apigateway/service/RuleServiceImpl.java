package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.RuleMapper;
import com.telecom.apigateway.model.entity.Rule;
import com.telecom.apigateway.model.enums.CategoryEnum;
import com.telecom.apigateway.model.vo.response.QueryCveRuleResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
@RequiredArgsConstructor
public class RuleServiceImpl extends ServiceImpl<RuleMapper, Rule> implements RuleService {
    private final RuleMapper ruleMapper;

    @Override
    public Page<QueryCveRuleResponse> queryCveRuleWithConfig(Page<?> page, String cveName, String status, String category) {
        return ruleMapper.queryCveRuleWithConfig(page, cveName, status, category);
    }

    @Override
    public List<Rule> crsRuleList() {
        return this.lambdaQuery()
                .eq(Rule::getCategory, CategoryEnum.SYSTEM.getCode())
                .list();
    }

    @Override
    public void updateTime(String cveId) {
        this.lambdaUpdate()
                .eq(Rule::getRuleId, cveId)
                .set(Rule::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public String getName(String cveId) {
        return this.lambdaQuery()
                .eq(Rule::getRuleId, cveId)
                .list()
                .stream()
                .map(Rule::getModule)
                .findFirst()
                .orElse(cveId);

    }
}
