package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.User;
import com.telecom.apigateway.model.vo.request.QueryUserRequest;
import com.telecom.apigateway.model.vo.response.QueryUserResponse;
import com.telecom.apigateway.model.vo.response.UserLoginResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

public interface UserService extends IService<User> {
    Optional<User> getByUsername(String username);
    Optional<User> getByUsernameOrPhone(String usernameOrPhone);

    boolean checkUsername(String username);

    boolean checkUsernameAndPhone(String username, String phone);

    boolean codePasswordAndSave(User user);

    boolean update(User user);

    List<User> list();

    Page<QueryUserResponse> queryUsers(Page<?> page, QueryUserRequest request);

    List<String> queryAllDepartments();
}
