package com.telecom.apigateway.service;

import cn.hutool.core.util.IdUtil;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.IpGroup;
import com.telecom.apigateway.model.entity.IpGroupRelation;
import com.telecom.apigateway.model.enums.*;
import com.telecom.apigateway.model.vo.request.CreateIpGroupRequest;
import com.telecom.apigateway.model.vo.request.JoinIpGroupRequest;
import com.telecom.apigateway.model.vo.request.UpdateIpGroupRequest;
import com.telecom.apigateway.model.vo.response.IpGroupDetailResponse;
import com.telecom.apigateway.model.vo.response.IpGroupResponse;
import com.telecom.apigateway.utils.MapUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class IpGroupBusinessService {
    private final IpGroupService ipGroupService;
    private final IpGroupRelationService ipGroupRelationService;

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.IP_GROUP,
            spelArgs = {"#{#request.name}"}
    )
    public String createIpGroup(CreateIpGroupRequest request) {
        String groupId = IdUtil.fastSimpleUUID();
        IpGroup ipGroup = new IpGroup(groupId, request.getName());
        ipGroupService.save(ipGroup);

        List<IpGroupRelation> relations = request.getIpGroups().stream().map((ip) -> {
            String relationId = IdUtil.fastSimpleUUID();
            return new IpGroupRelation(relationId, groupId, ip);
        }).collect(Collectors.toList());
        ipGroupRelationService.saveBatch(relations);
        return groupId;
    }

    public List<IpGroupResponse> list() {
        List<IpGroup> groups = ipGroupService.list();
        List<IpGroupRelation> relations = ipGroupRelationService.list();
        Map<String, List<IpGroupRelation>> groupMap = MapUtil.grouping(IpGroupRelation::getIpGroupId, relations);

        return groups.stream().map((group) -> {
            String groupId = group.getGroupId();
            List<IpGroupRelation> ipGroupRelations = groupMap.get(groupId);
            String content = String.format("共%d个个ip", 0);
            if (ipGroupRelations != null) {
                content = String.format("%s等，共%d个", ipGroupRelations.get(0).getIpAddress(), ipGroupRelations.size());
            }

            IpGroupResponse ipGroupResponse = new IpGroupResponse();
            ipGroupResponse.setGroupId(groupId);
            ipGroupResponse.setName(group.getName());
            ipGroupResponse.setContent(content);
            ipGroupResponse.setType(group.getType());
            return ipGroupResponse;
        }).collect(Collectors.toList());
    }

    public IpGroupDetailResponse detail(String groupId) {
        IpGroup ipGroup = getIpGroup(groupId);

        List<IpGroupRelation> relations = ipGroupRelationService.getByGroupId(groupId);

        IpGroupDetailResponse ipGroupDetailResponse = new IpGroupDetailResponse();
        ipGroupDetailResponse.setGroupId(ipGroup.getGroupId());
        ipGroupDetailResponse.setName(ipGroup.getName());
        ipGroupDetailResponse.setIpList(relations.stream().map(IpGroupRelation::getIpAddress).collect(Collectors.toList()));
        ipGroupDetailResponse.setType(ipGroup.getType());

        return ipGroupDetailResponse;
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.FULL_UPDATE,
            resourceType = ResourceTypeEnum.IP_GROUP,
            spelArgs = {"#{#request.groupId}", "#{#request}"}
    )
    public String update(UpdateIpGroupRequest request) {
        String groupId = request.getGroupId();
        IpGroup ipGroup = getIpGroup(groupId);

        if (IpGroupTypeEnum.SYSTEM.equals(ipGroup.getType())) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_NOT_OPERATE);
        }

        String name = request.getName();
        if (!ipGroup.getName().equals(name)) {
            IpGroup update = ipGroup.update(name);
            ipGroupService.updateById(update);
        }
        // TODO 可以优化一下控制数据量增长，下次一定
        deleteRelationsByGroupId(groupId);

        List<IpGroupRelation> newRelations = request.getIpGroups().stream().map((ip) -> {
            String relationId = IdUtil.fastSimpleUUID();
            return new IpGroupRelation(relationId, groupId, ip);
        }).collect(Collectors.toList());
        ipGroupRelationService.saveBatch(newRelations);

        return groupId;
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.IP_GROUP,
            spelArgs = {"#{#groupId}"}
    )
    public void delete(String groupId) {
        IpGroup ipGroup = getIpGroup(groupId);

        if (IpGroupTypeEnum.SYSTEM.equals(ipGroup.getType())) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_NOT_OPERATE);
        }

        deleteRelationsByGroupId(groupId);
        ipGroup.delete();
        ipGroupService.updateById(ipGroup);
    }

    private IpGroup getIpGroup(String groupId) {
        Optional<IpGroup> ipGroupOptional = ipGroupService.getOptByGroupId(groupId);
        if (!ipGroupOptional.isPresent()) {
            throw new BusinessException(ResultCodeEnum.GROUP_NOT_EXISTED);
        }
        return ipGroupOptional.get();
    }

    private void deleteRelationsByGroupId(String groupId) {
        List<IpGroupRelation> relations = ipGroupRelationService.getByGroupId(groupId)
                .stream()
                .map(IpGroupRelation::delete)
                .collect(Collectors.toList());
        ipGroupRelationService.updateBatchById(relations);
    }

    public List<IpGroupResponse> query(String ip) {
        List<IpGroupRelation> relations = ipGroupRelationService.getByIp(ip);
        List<String> groupIds = relations.stream().map(IpGroupRelation::getIpGroupId).collect(Collectors.toList());
        List<IpGroup> groups = ipGroupService.getByGroupIds(groupIds);

        return groups.stream().map((group) -> {
            IpGroupResponse ipGroupResponse = new IpGroupResponse();
            ipGroupResponse.setGroupId(group.getGroupId());
            ipGroupResponse.setName(group.getName());
            ipGroupResponse.setContent(ip);
            ipGroupResponse.setType(group.getType());
            ipGroupResponse.setUpdateTime(group.getUpdateTime());
            return ipGroupResponse;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.IP_GROUP,
            description = "将IP #{#request.ip} 加入到 #{#request.groupId}"
    )
    public void addIp(JoinIpGroupRequest request) {
        // check ip group existed
        List<String> groupIds = request.getGroupId();
        List<IpGroup> ipGroups = ipGroupService.getByGroupIds(groupIds);
        List<String> dbIpGroupIds = ipGroups.stream().map(IpGroup::getGroupId).collect(Collectors.toList());
        if (!new HashSet<>(dbIpGroupIds).containsAll(groupIds) || !new HashSet<>(groupIds).containsAll(dbIpGroupIds)) {
            throw new BusinessException(ResultCodeEnum.IP_GROUP_EXISTED);
        }
        // check ip group type
        if (ipGroups.stream().map(IpGroup::getType).anyMatch(IpGroupTypeEnum.SYSTEM::equals)) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_NOT_OPERATE);
        }

        // delete existed relations
        String ip = request.getIp();
        List<IpGroupRelation> relations = ipGroupRelationService.getByIp(ip);
        relations.forEach(IpGroupRelation::delete);
        ipGroupRelationService.updateBatchById(relations);

        // new ip relations
        List<IpGroupRelation> addRelations = groupIds.stream()
                .map((groupId) -> new IpGroupRelation(IdUtil.fastSimpleUUID(), groupId, ip))
                .collect(Collectors.toList());
        ipGroupRelationService.saveBatch(addRelations);
    }
}
