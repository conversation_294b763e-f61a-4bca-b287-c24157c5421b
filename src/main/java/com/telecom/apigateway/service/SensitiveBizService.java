package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.dto.EsSensitiveRuleDTO;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.SensitiveApi;
import com.telecom.apigateway.model.entity.SensitiveRule;
import com.telecom.apigateway.model.enums.SensitiveStatEnum;
import com.telecom.apigateway.model.vo.response.ApiQueryResponse;
import com.telecom.apigateway.model.vo.response.ApplicationResponse;
import com.telecom.apigateway.model.vo.response.SensitiveApiDetailResponse;
import com.telecom.apigateway.model.vo.response.SensitiveApiTriggerCountResponse;
import com.telecom.apigateway.model.vo.response.SensitiveTrendResponse;
import com.telecom.apigateway.model.vo.response.StatCount;
import com.telecom.apigateway.model.vo.response.StatCountResponse;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import com.telecom.apigateway.utils.DateTimeUtils;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class SensitiveBizService {

    @Resource
    private SensitiveApiService sensitiveApiService;
    @Resource
    private SensitiveRuleService sensitiveRuleService;
    @Resource
    private ApplicationService applicationService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private ApplicationBusinessService applicationBusinessService;
    @Resource
    private NginxAccessLogService nginxAccessLogService;
    @Resource
    private NginxLogEsClient nginxLogEsClient;
    @Resource
    private ApiInfoBizService apiInfoBizService;
    @Resource
    private SensitiveLogService sensitiveLogService;

    public List<?> stat(SensitiveStatEnum statType, String appId, LocalDateTime startTime, LocalDateTime endTime) {
        List<String> permitAppIds = getPermittedAppIds(appId);
        switch (statType) {
            case LEVEL:
            case COUNT:
                return statLevelCount(permitAppIds, endTime);
            case RULE:
                return statRuleCount(permitAppIds, endTime);
            case TREND:
                return statTrend(permitAppIds, startTime, endTime);
            case API:
            case APP:
                return statApiSensitiveCount(permitAppIds, startTime, endTime);
            default:
                return new ArrayList<>();
        }
    }

    private List<SensitiveTrendResponse> statTrend(List<String> permitAppIds,
                                                   LocalDateTime startTime,
                                                   LocalDateTime endTime) {
        List<ApiInfo> apis = apiInfoService.getByAppIdWithoutDataScope(permitAppIds);
        List<String> apiIds = apis.stream().map(ApiInfo::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(apiIds)) {
            return Collections.emptyList();
        }
        boolean isMonth = !startTime.plusDays(31).isAfter(endTime);

        LocalDateTime startTime1 = startTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endTime1 = endTime.plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        List<SensitiveApi> sensitiveApis = sensitiveApiService.lambdaQuery()
                .in(CollUtil.isNotEmpty(apiIds), SensitiveApi::getApiId, apiIds)
                .eq(SensitiveApi::getDeleted, false)
                .exists("select 1 from sensitive_rule sr where sr.is_deleted = false and " +
                        "sensitive_api.sensitive_rule_id = sr.id")
                .orderByAsc(SensitiveApi::getCreateTime)
                .list();

        List<SensitiveTrendResponse> results = new ArrayList<>();

        LocalDateTime currDate = startTime1;
        Set<String> preDayTotalSensitiveApiIds = new HashSet<>();
        while (currDate.isBefore(endTime1)) {
            // 看不懂就完事了
            if (isMonth) {
                if (!isSameDay(currDate, endTime)) {
                    if (!DateTimeUtils.isLastDayOfMonth(currDate)) {
                        currDate = currDate.plusDays(1);
                        continue;
                    }
                }
            }

            LocalDateTime currPeriod = currDate;
            LocalDateTime timeOfPeriodEnd = currPeriod.plusDays(1).minusSeconds(1);
            // 总数
            Set<String> totalSensitiveApiIds = sensitiveApis.stream()
                    // 当天及之前创建
                    .filter(ele -> ele.getCreateTime().isBefore(timeOfPeriodEnd))
                    // 当前周期未消敏
                    .filter(ele -> null == ele.getDealSensitiveTime() ||
                            ele.getDealSensitiveTime().isAfter(timeOfPeriodEnd))
                    .map(SensitiveApi::getApiId).collect(Collectors.toSet());
            int total = totalSensitiveApiIds.size();
            // 新增
            Set<String> finalPreDayTotalSensitiveApiIds = preDayTotalSensitiveApiIds;
            int newCount = sensitiveApis.stream()
                    // 当前周期创建
                    .filter(ele ->
                            (isMonth ? DateTimeUtils.isSameMonth(ele.getCreateTime(), currPeriod) :
                                    isSameDay(ele.getCreateTime(), currPeriod)))
                    // 不包含前一个统计周期
                    .filter(ele -> !finalPreDayTotalSensitiveApiIds.contains(ele.getApiId()))
                    .collect(Collectors.groupingBy(SensitiveApi::getApiId))
                    .size();
            // 脱敏
            // int dealtCount = sensitiveApis.stream()
            //         // 脱敏
            //         .filter(ele -> (ele.getDealSensitiveTime() != null))
            //         .filter(ele ->
            //                 isMonth ? DateTimeUtils.isSameMonth(ele.getCreateTime(), currPeriod) :
            //                         isSameDay(ele.getCreateTime(), currPeriod))
            //         .collect(Collectors.groupingBy(SensitiveApi::getApiId))
            //         .size();
            AtomicInteger dealtCount = new AtomicInteger(0);
            sensitiveApis.stream()
                    // 当前周期之前创建
                    .filter(ele -> ele.getCreateTime().isBefore(timeOfPeriodEnd))
                    .collect(Collectors.groupingBy(SensitiveApi::getApiId))
                    .forEach((apiId, list) -> {
                        // 没有未脱敏的
                        if (list.stream().allMatch(SensitiveApi::getDealt)) {
                            // 当前周期脱敏
                            if (list.stream().allMatch(ele -> (isMonth ?
                                    DateTimeUtils.isSameMonth(ele.getDealSensitiveTime(),
                                            currPeriod) : isSameDay(ele.getDealSensitiveTime(), currPeriod)))) {
                                dealtCount.getAndIncrement();
                            }
                        }
                    });


            results.add(new SensitiveTrendResponse(
                    currPeriod.format(DateTimeFormatter.ofPattern(isMonth ? "MM月" : "dd日")),
                    total,
                    newCount,
                    dealtCount.get())
            );
            currDate = currDate.plusDays(1);
            preDayTotalSensitiveApiIds = totalSensitiveApiIds;
        }


        return results;
    }

    /**
     * 统计 api 涉敏次数
     */
    private List<SensitiveApiTriggerCountResponse> statApiSensitiveCount(List<String> permitAppIds,
                                                                         LocalDateTime startTime,
                                                                         LocalDateTime endTime) {
        List<SensitiveApiTriggerCountResponse> results = new ArrayList<>();

        Map<String, ApiInfo> apiMap = apiInfoService.list()
                .stream().collect(Collectors.toMap(ApiInfo::getId, apiInfo -> apiInfo, (v1, v2) -> v1));

        Map<String, String> appMap = applicationBusinessService.listWithBizName()
                .stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId, ApplicationResponse::getName,
                        (v1, v2) -> v1));

        List<SensitiveApi> sensitiveApis = sensitiveApiService.lambdaQuery()
                .between(SensitiveApi::getDealSensitiveTime, startTime, endTime)
                .eq(SensitiveApi::getDealt, true)
                .list();

        List<String> sensitiveRuleIds =
                sensitiveRuleService.lambdaQuery().eq(SensitiveRule::getIsDeleted, false).list()
                        .stream().map(SensitiveRule::getId).collect(Collectors.toList());

        // group by apiId
        Map<String, List<SensitiveApi>> sensitiveApiMap =
                sensitiveApis.stream().collect(Collectors.groupingBy(SensitiveApi::getApiId));

        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime).build()
                .addExistQuery("sensitiveRules")
                .addMultipleQuery("sensitiveRules.ruleId", sensitiveRuleIds)
                .addMultipleQuery("appId", permitAppIds);

        List<StatCount> statCounts = nginxAccessLogService.groupCount(queryDTO, "apiId");

        for (StatCount statCount : statCounts) {

            ApiInfo apiInfo = apiMap.get(statCount.getLabel());
            if (apiInfo == null) {
                continue;
            }
            SensitiveApiTriggerCountResponse result = new SensitiveApiTriggerCountResponse();
            result.setCount(statCount.getCount());
            result.setApiId(statCount.getLabel());
            result.setApiName(apiMap.get(result.getApiId()).getName());
            result.setAppId(apiMap.get(result.getApiId()).getAppId());
            result.setAppName(appMap.getOrDefault(apiMap.get(result.getApiId()).getAppId(), "未知"));
            List<SensitiveApi> sensitiveApis1 = sensitiveApiMap.get(result.getApiId());
            if (sensitiveApis1 != null) {
                int count = 0;
                for (SensitiveApi sensitiveApi : sensitiveApis1) {
                    count += sensitiveApi.getSensitiveCount();
                }
                result.setCount(result.getCount() - count);
            }
            results.add(result);
        }

        return results.stream()
                .sorted(Comparator.comparingInt(SensitiveApiTriggerCountResponse::getCount).reversed())
                .limit(5).collect(Collectors.toList());
    }

    private List<StatCountResponse> statLevelCount(List<String> appIds, LocalDateTime time) {
        List<StatCountResponse> list = new ArrayList<>();
        list.add(new StatCountResponse("高敏感", 0));
        list.add(new StatCountResponse("中敏感", 0));
        list.add(new StatCountResponse("低敏感", 0));

        List<StatCountResponse> queryList = sensitiveApiService.getBaseMapper().statLevelCount(appIds, time);
        for (StatCountResponse statCountResponse : queryList) {
            for (StatCountResponse response : list) {
                if (response.getLabel().equals(statCountResponse.getLabel())) {
                    response.setCount(statCountResponse.getCount());
                }
            }
        }

        List<String> apiIds =
                apiInfoService.getByAppIdWithoutDataScope(appIds).stream().map(ApiInfo::getId).collect(Collectors.toList());
        List<SensitiveApi> sensitiveApis = sensitiveApiService.lambdaQuery()
                .in(CollUtil.isNotEmpty(apiIds), SensitiveApi::getApiId, apiIds)
                .eq(SensitiveApi::getDeleted, false)
                .exists("select 1 from sensitive_rule sr where sr.is_deleted = false and " +
                        "sensitive_api.sensitive_rule_id = sr.id")
                .orderByAsc(SensitiveApi::getCreateTime)
                .list();
        AtomicInteger dealtCount = new AtomicInteger(0);
        sensitiveApis.stream()
                // 当前周期之前创建
                .filter(ele -> ele.getCreateTime().isBefore(time.withHour(0).withMinute(0)))
                .collect(Collectors.groupingBy(SensitiveApi::getApiId))
                .forEach((apiId, list1) -> {
                    // 没有未脱敏的
                    if (list1.stream().allMatch(SensitiveApi::getDealt)) {
                        // 当前周期脱敏
                        if (list1.stream().allMatch(ele -> (isSameDay(ele.getDealSensitiveTime(), time)))) {
                            dealtCount.getAndIncrement();
                        }
                    }
                });
        list.add(new StatCountResponse("已脱敏", dealtCount.get()));
        return list;
    }

    private List<StatCountResponse> statRuleCount(List<String> appIds, LocalDateTime time) {
        List<StatCountResponse> list = sensitiveApiService.getBaseMapper().statRuleCount(appIds, time);
        // 取前五,后面的加起来啊作为"其他"
        if (list.size() > 5) {
            List<StatCountResponse> otherList = list.subList(5, list.size());
            StatCountResponse other =
                    new StatCountResponse("其他", otherList.stream().mapToInt(StatCountResponse::getCount).sum());
            list = list.subList(0, 5);
            list.add(other);
        }
        return list;
    }


    private List<String> getPermittedAppIds(String appId) {
        List<String> permittedAppIds;
        if (StrUtil.isBlank(appId) || "all".equalsIgnoreCase(appId)) {
            permittedAppIds = StpUtil.getPermissionList();
        } else {
            boolean validPermission = true;
            permittedAppIds = applicationService.getIdsWithChildren(Collections.singletonList(appId), validPermission);
        }
        if (permittedAppIds.isEmpty()) {
            permittedAppIds.add("-1");
        }
        return permittedAppIds;
    }

    private boolean isSameDay(LocalDateTime dateTime1, LocalDateTime dateTime2) {
        return dateTime1.getYear() == dateTime2.getYear() &&
                dateTime1.getMonthValue() == dateTime2.getMonthValue() &&
                dateTime1.getDayOfMonth() == dateTime2.getDayOfMonth();
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateAndResetLevel(SensitiveRule request) {
        sensitiveRuleService.update(request);
        List<String> apiIds = sensitiveApiService.lambdaQuery()
                .eq(SensitiveApi::getSensitiveRuleId, request.getId())
                .list().stream().map(SensitiveApi::getApiId).collect(Collectors.toList());
        if (CollUtil.isEmpty(apiIds)) {
            return;
        }
        for (String apiId : apiIds) {
            sensitiveApiService.resetLevel(apiId);
        }
    }

    public SensitiveApiDetailResponse getDetail(String id) {
        SensitiveApi sensitiveApi = sensitiveApiService.getById(id);
        String apiId = sensitiveApi.getApiId();
        String sensitiveRuleId = sensitiveApi.getSensitiveRuleId();

        SensitiveRule rule = sensitiveRuleService.getById(sensitiveRuleId);

        // 最近的一条涉敏的详情 原始日志
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .build()
                .addQuery("apiId", apiId)
                .addQuery("sensitiveRules.ruleId", rule.getId())
                .addExistQuery("sensitiveRules")
                .orderBy("logTime", SortOrder.DESC);
        EsNginxDTO nglog = nginxLogEsClient.queryOne(queryDTO);
        if (nglog == null) {
            return SensitiveApiDetailResponse.builder().sensitiveApiId(id).build();
        }
        List<EsSensitiveRuleDTO> sensitiveRules = nglog.getSensitiveRules();
        // 筛选出 ruleId 匹配的 content
        Optional<EsSensitiveRuleDTO> first =
                sensitiveRules.stream().filter(item -> item.getRuleId().equals(sensitiveRuleId)).findFirst();
        String sensitiveContent = "";
        if (first.isPresent()) {
            EsSensitiveRuleDTO slog = first.get();
            sensitiveContent = slog.getContent();
        }

        ApiQueryResponse apiQueryResponse = apiInfoBizService.queryById(apiId, true);
        String appName = apiQueryResponse.getAppName();
        String uri = apiQueryResponse.getUri();
        String apiName = apiQueryResponse.getName();

        return SensitiveApiDetailResponse.builder()
                .sensitiveApiId(id)
                .apiId(apiId)
                .uri(uri)
                .apiName(apiName)
                .logId(nglog.getUuid())
                .logRequest(nglog.toHttpRequest())
                .logResponse(nglog.toHttpResponse())
                .keyword(sensitiveContent)
                .sensitiveRuleId(rule.getId())
                .sensitiveRuleName(rule.getName())
                .sensitiveLevel(rule.getLevel())
                .discoverTime(sensitiveApi.getCreateTime())
                .appName(appName)
                .lastSensitiveTime(sensitiveApi.getLastSensitiveTime())
                .sensitiveCount(sensitiveApi.getSensitiveCount())
                .build();
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteRule(String ruleId) {
        sensitiveRuleService.delete(ruleId);
        sensitiveApiService.deleteByRuleId(ruleId);
        sensitiveLogService.deleteByRuleId(ruleId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteRules(List<String> ruleIds) {
        sensitiveRuleService.deleteByIds(ruleIds);
        sensitiveApiService.deleteByRuleIds(ruleIds);
    }
}

