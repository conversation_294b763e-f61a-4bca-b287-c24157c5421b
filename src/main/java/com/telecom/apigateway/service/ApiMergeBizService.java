package com.telecom.apigateway.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.AntPathMatcher;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.entity.ApiMergeCondition;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.AddApiMergeRequest;
import com.telecom.apigateway.model.vo.request.MatchApiMergeRequest;
import com.telecom.apigateway.model.vo.request.QueryApiMergeRequest;
import com.telecom.apigateway.model.vo.request.UpdateApiMergeRequest;
import com.telecom.apigateway.model.vo.response.ApplicationResponse;
import com.telecom.apigateway.model.vo.response.MatchMergeApiResponse;
import com.telecom.apigateway.model.vo.response.QueryApiMergeResponse;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import com.telecom.apigateway.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ApiMergeBizService {

    @Resource
    private ApiMergeService apiMergeService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private ApplicationBusinessService applicationBusinessService;
    @Resource
    private SensitiveLogService sensitiveLogService;
    @Resource
    private SensitiveApiService sensitiveApiService;
    @Resource
    private NginxAccessLogService nginxAccessLogService;
    @Resource
    private AbnormalBehaviorRuleService abrService;
    @Resource
    private AbnormalBehaviorRuleTriggerService abrtService;
    @Resource
    private RiskLogNewService riskLogNewService;
    @Resource
    private ApiDecryptService apiDecryptService;
    @Resource
    private BlocklistService blocklistService;
    @Resource
    private SensitiveWhiteListService sensitiveWhiteListService;

    private static final AntPathMatcher pathMatcher = new AntPathMatcher();

    public QueryApiMergeResponse getByMergeId(String id) {
        ApiMerge byId = apiMergeService.getById(id);
        QueryApiMergeResponse response = entityToResponse(byId);
        return response;
    }

    public Page<QueryApiMergeResponse> queryPage(QueryApiMergeRequest pageRequest) {
        Page<ApiMerge> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        Page<ApiMerge> pageData = apiMergeService.lambdaQuery()
                .like(StrUtil.isNotBlank(pageRequest.getName()), ApiMerge::getName, pageRequest.getName())
                .eq(pageRequest.getPolicy() != null, ApiMerge::getPolicy, pageRequest.getPolicy())
                .eq(pageRequest.getEnable() != null, ApiMerge::getEnable, pageRequest.getEnable())
                .orderByDesc(ApiMerge::getCreateTime)
                .page(page);

        List<QueryApiMergeResponse> list =
                pageData.getRecords().stream().map(this::entityToResponse).collect(Collectors.toList());

        return PageUtils.convertPage(page, list);
    }

    private QueryApiMergeResponse entityToResponse(ApiMerge apiMerge) {
        QueryApiMergeResponse apiMergeResponse = new QueryApiMergeResponse();
        apiMergeResponse.setId(apiMerge.getId());
        apiMergeResponse.setName(apiMerge.getName());
        apiMergeResponse.setApiName(apiMerge.getApiName());
        apiMergeResponse.setPolicy(apiMerge.getPolicy());
        apiMergeResponse.setCondition(apiMerge.getCondition());
        apiMergeResponse.setUrlReg(apiMerge.getUrlReg());
        apiMergeResponse.setHttpMethods(apiMerge.getHttpMethods());
        apiMergeResponse.setEnable(apiMerge.getEnable());
        apiMergeResponse.setCreateUser(apiMerge.getCreateUser());
        apiMergeResponse.setCreateTime(apiMerge.getCreateTime());
        apiMergeResponse.setUpdateUser(apiMerge.getUpdateUser());
        apiMergeResponse.setUpdateTime(apiMerge.getUpdateTime());
        apiMergeResponse.setRemark(apiMerge.getRemark());
        apiMergeResponse.setDetail(apiMerge.getDetail());
        return apiMergeResponse;
    }

    public List<MatchMergeApiResponse> match(MatchApiMergeRequest request) {
        List<ApiMergeCondition> conditions = request.getCondition();
        String urlReg = request.getUrlReg();
        List<String> httpMethods = request.getHttpMethods();

        List<ApplicationResponse> apps = applicationBusinessService.listWithBizName();
        Map<String, String> appMap = apps.stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                ApplicationResponse::getName, (a, b) -> a));

        List<ApiInfo> apis = apiInfoService.listOfNotMerge(request.getAppId());

        if (CollUtil.isNotEmpty(conditions)) {
            return apis.stream().filter(api -> {
                boolean flag = true;
                for (ApiMergeCondition condition : conditions) {
                    switch (condition.getTarget()) {
                        case URI:
                            if (!match(api.getUri(), condition.getValue(), condition.getOperation())) {
                                return false;
                            }
                            break;
                        case HTTP_METHOD:
                            if (!match(api.getHttpMethods(), condition.getValue(), condition.getOperation())) {
                                return false;
                            }
                            break;
                    }
                }
                return flag;
            }).map(api -> {
                MatchMergeApiResponse response = new MatchMergeApiResponse();
                response.setApiId(api.getId());
                response.setApiName(api.getName());
                response.setAppId(api.getAppId());
                response.setAppName(appMap.get(api.getAppId()));
                response.setHttpMethod(api.getHttpMethods());
                response.setUri(api.getUri());
                return response;
            }).collect(Collectors.toList());
        } else if (CollUtil.isNotEmpty(httpMethods) && StrUtil.isNotBlank(urlReg)) {
            URL url;
            try {
                url = new URL(urlReg);
            } catch (MalformedURLException e) {
                throw new BusinessException(ResultCodeEnum.URL_FORMAT_ERROR);
            }
            return apis.stream().filter(api -> {
                boolean flag = true;
                if (!new HashSet<>(httpMethods).containsAll(api.getHttpMethods())) {
                    return false;
                }
                if (!api.getHosts().contains(url.getHost())) {
                    return false;
                }
                if (!api.getPort().equals(url.getPort())) {
                    return false;
                }
                if (!pathMatcher.match(url.getPath(), api.getUri())) {
                    return false;
                }
                return flag;
            }).map(api -> {
                MatchMergeApiResponse response = new MatchMergeApiResponse();
                response.setApiId(api.getId());
                response.setApiName(api.getName());
                response.setAppId(api.getAppId());
                response.setAppName(appMap.get(api.getAppId()));
                response.setHttpMethod(api.getHttpMethods());
                response.setUri(api.getUri());
                return response;
            }).collect(Collectors.toList());
        }
        return null;
    }

    private boolean match(String val1, String val2, ApiMergeEnum.Operation operation) {
        Set<String> val2List = Arrays.stream(val2.split("\n")).map(String::trim).collect(Collectors.toSet());
        switch (operation) {
            case EQUALS:
                return val2List.contains(val1);
            case NOT_EQUALS:
                return !val2List.contains(val1);
            case CONTAINS:
                return val1.contains(val2);
            case WILDCARD:
                String regex = val1.replace(".", "\\.").replace("*", ".*");
                return val2.matches(regex);
            case REGEX:
                return val2.matches(val2);
            case BELONGS_TO_RANGE:
                return val2List.stream().allMatch(e -> {
                    String[] range = e.split("-");
                    int min = Integer.parseInt(range[0]);
                    int max = Integer.parseInt(range[1]);
                    int origVal = Integer.parseInt(val1);
                    return origVal >= min && origVal <= max;
                });
        }
        return false;
    }

    private boolean match(List<String> val1, String val2, ApiMergeEnum.Operation operation) {
        Set<String> val2List = Arrays.stream(val2.split("\n")).map(String::trim).collect(Collectors.toSet());
        switch (operation) {
            case EQUALS:
                return val2List.containsAll(val1);
            case NOT_EQUALS:
                return !val2List.containsAll(val1);
        }
        return false;
    }

    private void checkDuplicateName(String name) {
        if (apiMergeService.getByName(name) != null) {
            throw new BusinessException(ResultCodeEnum.DUPLICATE_MERGE_API);
        }
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#request.name}"}
    )
    public ApiMerge saveOne(AddApiMergeRequest request) {
        checkDuplicateName(request.getName());
        checkDuplicateMatch(request.getUrlReg());
        ApiMerge entity;
        if (request.getPolicy() == ApiMergeEnum.Policy.MERGE) {
            List<ApiInfo> apis = apiInfoService.list();
            URL url;
            try {
                url = new URL(request.getUrlReg());
            } catch (MalformedURLException e) {
                throw new BusinessException(ResultCodeEnum.URL_FORMAT_ERROR);
            }

            Stream<ApiInfo> filterApiStream = apis.stream().filter(api -> {
                boolean flag = true;
                if (!new HashSet<>(request.getHttpMethods()).containsAll(api.getHttpMethods())) {
                    return false;
                }
                if (!api.getHosts().contains(url.getHost())) {
                    return false;
                }
                if (!api.getPort().equals(url.getPort())) {
                    return false;
                }
                if (!pathMatcher.match(url.getPath(), api.getUri())) {
                    return false;
                }
                return flag;
            });

            List<String> appIds = filterApiStream.map(ApiInfo::getAppId).distinct().collect(Collectors.toList());
            if (appIds.size() != 1) {
                throw new BusinessException(ResultCodeEnum.API_MERGE_ERROR, "api 不属于同一个应用");
            }

            List<String> mergeApiIds = apis.stream().filter(api -> {
                boolean flag = true;
                if (!new HashSet<>(request.getHttpMethods()).containsAll(api.getHttpMethods())) {
                    return false;
                }
                if (!api.getHosts().contains(url.getHost())) {
                    return false;
                }
                if (!api.getPort().equals(url.getPort())) {
                    return false;
                }
                if (!pathMatcher.match(url.getPath(), api.getUri())) {
                    return false;
                }
                return flag;
            }).map(ApiInfo::getId).collect(Collectors.toList());
            entity = request.toMergeEntity(mergeApiIds);
            if (mergeApiIds.size() <= 1) {
                throw new BusinessException(ResultCodeEnum.API_MERGE_ERROR, "合并api数量为1");
            }
        } else {
            entity = request.toIgnoreEntity();
        }

        ApiMerge apiMerge = apiMergeService.saveOne(entity);

        return apiMerge;
    }

    private void checkDuplicateMatch(String urlStr) {
        URL url;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new BusinessException("不是有效的 url");
        }

        String uri = url.getPath();
        List<ApiMerge> apiMerges = apiMergeService.list();

        for (ApiMerge apiMerge : apiMerges) {
            String existUrlMatch = apiMerge.getUrlReg();
            String uriReg;
            try {
                URL existUrl = new URL(existUrlMatch);
                uriReg = existUrl.getPath();
            } catch (MalformedURLException e) {
                throw new BusinessException("不是有效的 url");
            }

            if (uri.matches(uriReg)) {
                throw new BusinessException("重复的策略:" + existUrlMatch);
            }
        }
    }

    private void saveMergedApi(ApiMerge apiMerge) {
        List<String> apiIds = apiMerge.getApi();
        List<ApiInfo> apiInfos = apiInfoService.listByIds(apiIds);
        if (apiInfos.stream().anyMatch(ApiInfo::getDeleted)) {
            throw new BusinessException(ResultCodeEnum.API_MERGE_ERROR, "包含已删除的api");
        }
        List<String> appIds = apiInfos.stream().map(ApiInfo::getAppId).distinct().collect(Collectors.toList());
        if (appIds.size() != 1) {
            throw new BusinessException(ResultCodeEnum.API_MERGE_ERROR, "api 不属于同一个应用");
        }
        String appId = appIds.get(0);
        URL url = null;
        try {
            url = new URL(apiMerge.getUrlReg());
        } catch (MalformedURLException e) {
            throw new BusinessException(ResultCodeEnum.URL_FORMAT_ERROR);
        }
        String uri = url.getPath();

        ApiInfo apiInfo = ApiInfo.ofMerge(apiMerge.getId(),
                apiMerge.getApiName(),
                appId,
                apiMerge.getHttpMethods(),
                uri);
        apiInfoService.save(apiInfo);
        apiInfoService.updateMergeId(apiMerge.getApi(), apiMerge.getId());
        log.info("[API][MERGE] saveMergedApi: {}", apiMerge);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.FULL_UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"策略 #{#result.name} 状态变更为 禁用"}
    )
    public ApiMerge updateOne(UpdateApiMergeRequest request) {
        checkCanUpdate(request.getId());
        checkDuplicateName(request.getName());
        checkDuplicateMatch(request.getUrlReg());
        ApiMerge entity = request.toEntity();
        return apiMergeService.updateOne(entity);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"策略 #{#result.name} 状态变更为 启用"}
    )
    public ApiMerge enable(String id) {
        checkCanUpdate(id);
        ApiMerge byId = apiMergeService.getById(id);
        apiMergeService.updateStatusById(id, true);
        // 新增一个api, 修改n个 api 合并状态为合并

        // 没有保证原子性
        saveMergedApi(byId);
        // 涉敏次数重置
        sensitiveApiService.resetCount(byId.getApi());
        // 异常行为次数重置
        abrtService.resetCount(byId.getApi());

        List<String> fromApiIds = byId.getApi();
        String toApiId = byId.getId();
        // 更新涉敏绑定
        sensitiveApiService.updateApiId(fromApiIds, toApiId);
        sensitiveLogService.updateApiId(fromApiIds, toApiId);
        sensitiveWhiteListService.updateApiId(fromApiIds, toApiId);
        // 更新威胁绑定
        riskLogNewService.updateApiId(fromApiIds, toApiId);
        // 加密绑定
        apiDecryptService.updateApiId(fromApiIds, toApiId);
        // 更新异常行为绑定
        abrService.updateApiId(fromApiIds, toApiId);
        abrtService.updateApiId(fromApiIds, toApiId);
        // 黑白名单
        blocklistService.updateApiId(fromApiIds, toApiId);
        // 更新日志
        nginxAccessLogService.updateApiId(fromApiIds, toApiId);
        // 更新
        log.info("[API][MERGE] apis {} => api {}", fromApiIds, toApiId);
        return byId;
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"策略 #{#result.name} 状态变更为 禁用"}
    )
    public ApiMerge disable(String id) {
        checkCanUpdate(id);
        ApiMerge byId = apiMergeService.getById(id);
        apiMergeService.updateStatusById(id, false);
        return byId;
    }

    private void checkCanUpdate(String id) {
        if (!apiMergeService.canUpdate(id)) {
            throw new BusinessException(ResultCodeEnum.CAN_NOT_UPDATE);
        }
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public ApiMerge deleteOne(String id) {
        ApiMerge apiMerge = apiMergeService.detail(id);
        apiMergeService.deleteOne(id);
        // todo 还原恶心人的操作
        return apiMerge;
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public List<ApiMerge> deleteBatch(List<String> ids) {
        ids.forEach(this::deleteOne);
        return apiMergeService.getByIds(ids);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public List<ApiMerge> enableBatch(List<String> ids) {
        ids.forEach(this::enable);
        return apiMergeService.getByIds(ids);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public List<ApiMerge> disableBatch(List<String> ids) {
        ids.forEach(this::disable);
        return apiMergeService.getByIds(ids);
    }

    public static void main(String[] args) throws MalformedURLException {
        URL url = new URL("https:/192.168.22.1:1111/api/v1/{xxx}");
        System.out.println(pathMatcher.match(url.getPath(), "/api/v1/listArticle"));
    }
}
