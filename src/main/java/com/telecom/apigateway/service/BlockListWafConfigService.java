package com.telecom.apigateway.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.telecom.apigateway.common.excption.WafException;
import com.telecom.apigateway.model.dto.WafConfigDTO;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleTrigger;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.Blocklist;
import com.telecom.apigateway.model.entity.IpGroupRelation;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import com.telecom.apigateway.model.enums.BlocklistMatchEnum;
import com.telecom.apigateway.model.vo.request.BlocklistResponse;
import com.telecom.apigateway.model.vo.response.WafConfigQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024-11-13
 */
@Slf4j
@Service
public class BlockListWafConfigService {

    @Resource
    private IpGroupRelationService ipGroupRelationService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private BlocklistService blocklistService;
    @Resource
    private AbnormalBehaviorRuleTriggerService abrtService;

    public WafConfigDTO getConfig(Blocklist blocklist) throws WafException {
        if (blocklist == null) {
            throw new WafException("blocklist must not null");
        }
        String conditionStr = blocklist.getCondition();
        if (StrUtil.isBlank(conditionStr)) {
            throw new WafException("blocklist$condition must not empty");
        }
        JSONArray conditionArray = JSONUtil.parseArray(conditionStr);
        if (conditionArray.isEmpty()) {
            return null;
        }

        String action = "White".equalsIgnoreCase(blocklist.getType()) ? "allow" : "deny";
        int priority = blocklist.getPriority() == null ? 99999 : blocklist.getPriority();
        long timestamp = blocklist.getUpdateTime().atZone(ZoneId.systemDefault()).toEpochSecond();
        WafConfigDTO config = WafConfigDTO.standardConfig(blocklist.getBlockId(), action, priority, timestamp);

        // 预处理条件, 主要处理 api
        List<BlocklistResponse.Condition> conditionList = new ArrayList<>();
        for (int i = 0; i < conditionArray.size(); i++) {
            BlocklistResponse.Condition condition =
                    conditionArray.getJSONObject(i).toBean(BlocklistResponse.Condition.class);
            conditionList.add(condition);
        }

        // 遍历条件,进行构建规则集
        Object[][] appExts = new Object[conditionList.size()][3];
        for (int i = 0; i < conditionList.size(); i++) {
            BlocklistResponse.Condition condition = conditionList.get(i);
            appExts[i] = buildOneRule(condition);
        }

        config.setApp_ext(appExts);
        if ("Active".equals(blocklist.getStatus())) {
            config.setState("on");
        }
        return config;
    }

    /**
     * 构建一条规则
     * <pre>
     * [
     *   field,
     *   [
     *     内容,匹配方式,是否取反
     *   ],
     *   拼接方式
     * ]
     * </pre>
     */
    private Object[] buildOneRule(BlocklistResponse.Condition condition) throws WafException {
        BlocklistMatchEnum.MatchTarget target = BlocklistMatchEnum.MatchTarget.getByCode(condition.getTarget());
        BlocklistMatchEnum.MatchOperation operation =
                BlocklistMatchEnum.MatchOperation.getByCode(condition.getOperation());
        if (target == null || operation == null) {
            log.warn(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 无效的 condition: {}", condition);
            return null;
        }
        return new Object[]{
                target.getWafField(),
                buildAppExtMatch(target, condition),
                "and"
        };
    }

    /**
     * 构建规则中的匹配条件
     * [
     * 内容,匹配方式,是否取反
     * ],
     * header:
     * [匹配的内容, 匹配方式, [请求头key,位置], 是否取反]
     */
    private Object[] buildAppExtMatch(BlocklistMatchEnum.MatchTarget target, BlocklistResponse.Condition condition) throws WafException {
        switch (target) {
            case HEADER:
                return buildAppExtMatchOfHeader(condition);
            case METHOD:
                return buildAppExtMatchOfMethod(condition);
            case HOST:
                return buildAppExtMatchOfHost(condition);
            case API:
                return buildAppExtMatchOfApi(condition);
            case BODY:
                return buildAppExtMatchOfBody(condition);
            case PATH:
                return buildAppExtMatchOfUri(condition);
            case SOURCE_IP:
                return buildAppExtMatchOfSourceIp(condition);
            default:
                throw new WafException("不支持的匹配目标: " + target);
        }
    }

    private Object[] buildAppExtMatchOfSourceIp(BlocklistResponse.Condition condition) {
        BlocklistMatchEnum.MatchOperation operation =
                BlocklistMatchEnum.MatchOperation.getByCode(condition.getOperation());
        Object[] appExtMatch = new Object[3];
        // ==================== 构建内容 ====================
        // ip 组单独处理
        if (operation == BlocklistMatchEnum.MatchOperation.IN_GROUP ||
                operation == BlocklistMatchEnum.MatchOperation.NOT_IN_GROUP) {
            List<String> ipGroupIds = Arrays.asList(condition.getValue().split(","));
            List<IpGroupRelation> ipGroupRelations = ipGroupRelationService.getByGroupIds(ipGroupIds);
            appExtMatch[0] = ipGroupRelations.stream().map(IpGroupRelation::getIpAddress).distinct().toArray();
        } else {
            appExtMatch[0] = condition.getValue();
        }
        appExtMatch[1] = getMatchOptionStr(operation);
        appExtMatch[2] = isNegation(operation);
        return appExtMatch;
    }

    private Object[] buildAppExtMatchOfUri(BlocklistResponse.Condition condition) {
        return buildAppExtMatch(condition);
    }

    /**
     * 请求体格式
     * <pre>
     * [
     *   "posts_all",
     *   [
     *     "爸爸是我",
     *     "in",
     *     false
     *   ],
     *   "and"
     * ],
     * </pre>
     */
    private Object[] buildAppExtMatchOfBody(BlocklistResponse.Condition condition) {
        return buildAppExtMatch(condition);
    }

    /**
     * api 格式
     * <pre>
     *  [
     *    "api",
     *    [
     *        ["/api/v1/api1",""/api/v1/api1"],
     *        "in",
     *        false
     *    ],
     *    "and"
     *  ]
     * </pre>
     */
    private Object[] buildAppExtMatchOfApi(BlocklistResponse.Condition condition) {
        BlocklistMatchEnum.MatchOperation operation =
                BlocklistMatchEnum.MatchOperation.getByCode(condition.getOperation());
        Object[] appExtMatch = new Object[3];
        // ==================== 构建内容 ====================
        String value = condition.getValue();
        String[] apiIds = value.split(",");

        List<ApiInfo> apiInfoList = apiInfoService.lambdaQuery().in(ApiInfo::getId, Arrays.asList(apiIds)).list();
        String[] apis = apiInfoList.stream().map(ApiInfo::toReconizedString).toArray(String[]::new);
        appExtMatch[0] = apis;
        appExtMatch[1] = "list";
        appExtMatch[2] = isNegation(operation);
        return appExtMatch;
    }

    /**
     * host 格式
     * <pre>
     * [
     *   "host",
     *   [
     *     "*************:5460",
     *     "=",
     *     false
     *   ],
     *   "and"
     * ]
     * </pre>
     */
    private Object[] buildAppExtMatchOfHost(BlocklistResponse.Condition condition) {
        return buildAppExtMatch(condition);
    }

    private Object[] buildAppExtMatch(BlocklistResponse.Condition condition) {
        BlocklistMatchEnum.MatchOperation operation =
                BlocklistMatchEnum.MatchOperation.getByCode(condition.getOperation());
        Object[] appExtMatch = new Object[3];
        // ==================== 构建内容 ====================
        appExtMatch[0] = condition.getValue();
        appExtMatch[1] = getMatchOptionStr(operation);
        appExtMatch[2] = isNegation(operation);
        return appExtMatch;
    }

    /**
     * 构建方法匹配规则 method
     * <pre>
     * [
     *   "method",
     *   [
     *     "GET",
     *     "=",
     *     false
     *   ],
     *   "and"
     * ]
     * </pre>
     */
    private Object[] buildAppExtMatchOfMethod(BlocklistResponse.Condition condition) {
        return buildAppExtMatch(condition);
    }

    /**
     * 格式
     * [匹配的内容, 匹配方式, [请求头key,位置], 是否取反]
     */
    private Object[] buildAppExtMatchOfHeader(BlocklistResponse.Condition condition) throws WafException {
        BlocklistMatchEnum.MatchOperation operation =
                BlocklistMatchEnum.MatchOperation.getByCode(condition.getOperation());
        Object[] appExtMatch = new Object[4];
        // ==================== 构建内容 ====================
        // condition.getValue() 判断是否为中文
        if (containsChinese(condition.getValue())) {
            throw new WafException("header 内容不能包含中文");
        }
        appExtMatch[0] = condition.getValue();
        appExtMatch[1] = getMatchOptionStr(operation);
        appExtMatch[2] = new Object[]{condition.getHeaderName(), "all"};
        appExtMatch[3] = isNegation(operation);
        return appExtMatch;
    }

    /**
     * 格式
     * [匹配的内容, 匹配方式, [请求头key,位置], 是否取反]
     */
    private Object[] buildAppExtMatchOfHeader(String headerKey, String headerVal, String operationStr) throws WafException {
        BlocklistMatchEnum.MatchOperation operation =
                BlocklistMatchEnum.MatchOperation.getByCode(operationStr);
        Object[] appExtMatch = new Object[4];
        // ==================== 构建内容 ====================
        // condition.getValue() 判断是否为中文
        if (containsChinese(headerVal)) {
            throw new WafException("header 内容不能包含中文");
        }
        appExtMatch[0] = headerVal;
        appExtMatch[1] = getMatchOptionStr(operation);
        appExtMatch[2] = new Object[]{headerKey, "all"};
        appExtMatch[3] = isNegation(operation);
        return appExtMatch;
    }

    /**
     * 根据 blockId 禁用规则
     *
     * @param operation 匹配方式
     */

    private String getMatchOptionStr(BlocklistMatchEnum.MatchOperation operation) {
        switch (operation) {
            case EQUALS:
            case NOT_EQUALS:
                return "=";
            case IN_GROUP:
            case NOT_IN_GROUP:
                return "list";
            case FUZZY:
            case CONTAINS:
            case NOT_CONTAINS:
                return "in";
            case REGEX:
                return "jio";
        }
        throw new IllegalArgumentException("不支持的 waf 操作类型: " + operation.name());
    }

    /**
     * 是否取反
     */
    private boolean isNegation(BlocklistMatchEnum.MatchOperation operation) {
        switch (operation) {
            case IN_GROUP:
            case EQUALS:
            case CONTAINS:
            case REGEX:
            case FUZZY:
                return false;
            case NOT_EQUALS:
            case NOT_IN_GROUP:
            case NOT_CONTAINS:
                return true;
        }
        throw new IllegalArgumentException("不支持的 waf 操作类型: " + operation.name());
    }

    public static boolean containsChinese(String str) {
        final Pattern CHINESE_PATTERN = Pattern.compile("[\\u4E00-\\u9FFF]");
        if (str == null || str.isEmpty()) {
            return false;
        }
        return CHINESE_PATTERN.matcher(str).find();
    }


    public WafConfigQueryResponse getWafConfigs(Long lastUpdateTime) throws WafException {
        if (lastUpdateTime == null) {
            throw new WafException("lastUpdateTime is null");
        }

        LocalDateTime dateTime = Instant.ofEpochSecond(lastUpdateTime)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        List<WafConfigDTO> wafConfigs = new ArrayList<>();
        Long count = blocklistService.lambdaQuery().gt(Blocklist::getUpdateTime, dateTime).count();
        if (count == 0) {
            return new WafConfigQueryResponse(false, null);
        }
        /*
            ● 白名单优先级大于黑名单
            ● 用户新增/编辑可输入1-100的整数
            ● 数值越小，优先级越高，相同数值，更新时间越晚，优先级越高
         */
        List<Blocklist> blocklistList = blocklistService.lambdaQuery()
                .eq(Blocklist::getDeleted, false)
                .eq(Blocklist::getStatus, "Active")
                .orderByDesc(Blocklist::getType)
                .orderByAsc(Blocklist::getPriority)
                .orderByDesc(Blocklist::getUpdateTime)
                .list();

        long newUpdateTime = System.currentTimeMillis() / 1000;
        for (Blocklist blocklist : blocklistList) {
            WafConfigDTO config = getConfig(blocklist);
            if (config != null) {
                config.setUpdateTime(newUpdateTime);
                wafConfigs.add(config);
            }
        }
        return new WafConfigQueryResponse(true, wafConfigs);
    }

    /**
     * @param lastUpdateTime 时间戳,单位 s
     */
    public WafConfigQueryResponse getAbrtConfigs(Long lastUpdateTime) throws WafException {
        // if (lastUpdateTime == null) {
        //     throw new WafException("lastUpdateTime is null");
        // }
        LocalDateTime now = LocalDateTime.now();
        // // 稍微减少时间，避免请求中间有配置更新
        // lastUpdateTime = lastUpdateTime - 10;
        // LocalDateTime dateTime = Instant.ofEpochSecond(lastUpdateTime)
        //         .atZone(ZoneId.systemDefault())
        //         .toLocalDateTime();
        // List<WafConfigDTO> wafConfigs = new ArrayList<>();
        // Long wafCount = abrtService.lambdaQuery()
        //         .eq(AbnormalBehaviorRuleTrigger::getValid, true)
        //         .ge(AbnormalBehaviorRuleTrigger::getInvalidTime, now)
        //         .count();
        // if (wafCount == 0) {
        //     return new WafConfigQueryResponse(true, Collections.emptyList());
        // }
        //
        // Long count = abrtService.lambdaQuery()
        //         .eq(AbnormalBehaviorRuleTrigger::getValid, true)
        //         .gt(AbnormalBehaviorRuleTrigger::getInvalidTime, now)
        //         .gt(AbnormalBehaviorRuleTrigger::getUpdateTime, dateTime)
        //         .count();
        // if (count == 0) {
        //     return new WafConfigQueryResponse(false, null);
        // }
        /*
            ● 用户新增/编辑可输入1-100的整数
            ● 数值越小，优先级越高，相同数值，更新时间越晚，优先级越高
         */
        List<WafConfigDTO> wafConfigs = new ArrayList<>();
        List<AbnormalBehaviorRuleTrigger> abrtList = abrtService.lambdaQuery()
                .eq(AbnormalBehaviorRuleTrigger::getDeleted, false)
                .eq(AbnormalBehaviorRuleTrigger::getValid, true)
                .gt(AbnormalBehaviorRuleTrigger::getInvalidTime, now)
                .orderByAsc(AbnormalBehaviorRuleTrigger::getPriority)
                .orderByDesc(AbnormalBehaviorRuleTrigger::getCreateTime)
                .list();

        long newUpdateTime = System.currentTimeMillis() / 1000;
        for (AbnormalBehaviorRuleTrigger arbt : abrtList) {
            WafConfigDTO config = getConfig(arbt);
            if (config != null) {
                config.setUpdateTime(newUpdateTime);
                wafConfigs.add(config);
            }
        }
        return new WafConfigQueryResponse(true, wafConfigs);
    }

    private WafConfigDTO getConfig(AbnormalBehaviorRuleTrigger arbt) throws WafException {
        if (arbt == null) {
            throw new WafException("abrt must not null");
        }
        if (arbt.getWafCondition() == null) {
            throw new WafException("blocklist must not null");
        }
        String conditionStr = arbt.getWafCondition();
        if (StrUtil.isBlank(conditionStr)) {
            throw new WafException("arbt#condition must not empty");
        }
        JSONArray conditionArray = JSONUtil.parseArray(conditionStr);
        if (conditionArray.isEmpty()) {
            return null;
        }

        String action = "";
        if (arbt.getPolicy() == AbnormalBehaviorRuleEnum.Policy.WARNING) {
            action = "log";
        } else if (arbt.getPolicy() == AbnormalBehaviorRuleEnum.Policy.BLOCK) {
            action = "deny";
        } else if (arbt.getPolicy() == AbnormalBehaviorRuleEnum.Policy.BANNED) {
            action = "deny";
        }
        int priority = arbt.getPriority() == null ? 99999 : arbt.getPriority();
        long timestamp = arbt.getCreateTime().atZone(ZoneId.systemDefault()).toEpochSecond();
        WafConfigDTO config = WafConfigDTO.standardConfig(arbt.getId(), action, priority, timestamp);

        // 预处理条件, 主要处理 api
        List<BlocklistResponse.Condition> conditionList = new ArrayList<>();
        for (int i = 0; i < conditionArray.size(); i++) {
            BlocklistResponse.Condition condition =
                    conditionArray.getJSONObject(i).toBean(BlocklistResponse.Condition.class);
            conditionList.add(condition);
        }

        // 遍历条件,进行构建规则集
        Object[][] appExts = new Object[conditionList.size()][3];
        for (int i = 0; i < conditionList.size(); i++) {
            BlocklistResponse.Condition condition = conditionList.get(i);
            appExts[i] = buildOneRule(condition);
        }

        config.setApp_ext(appExts);
        config.setState(arbt.getValid() ? "on" : "off");
        return config;
    }
}
