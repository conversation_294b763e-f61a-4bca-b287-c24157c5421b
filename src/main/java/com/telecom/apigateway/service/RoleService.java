package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.Role;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface RoleService extends IService<Role> {

    /**
     * 分页查询角色列表
     *
     * @param page 分页参数
     * @return 角色列表
     */
    Page<Role> pageRoles(Page<Role> page);

    List<Role> listByRoleIds(Collection<String> roleIds);

    Optional<Role> optByRoleId(String roleId);

    boolean nameExist(String roleName, String roleId);

    List<Role> list();
}