package com.telecom.apigateway.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.client.WhoisClient;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.mapper.IpInfoMapper;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.entity.IpInfo;
import com.telecom.apigateway.model.vo.response.IpInfoResponse;
import com.telecom.apigateway.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-11-14
 */
@Slf4j
@Service
public class IpInfoService extends ServiceImpl<IpInfoMapper, IpInfo> {

    @Resource
    private NginxLogEsClient nginxLogEsClient;

    @Resource
    private WhoisClient whoisClient;

    public void add(IpInfo ipInfo) {
        this.save(ipInfo);
    }

    public void fetchIpInfoAndSave(String ip) {
        if (StrUtil.isEmpty(ip)) {
            return;
        }
        try {
            IpInfo query = whoisClient.query(ip);
            query.setIp(ip);
            this.saveOrUpdate(query);
        } catch (Exception e) {
            log.error("whois查询异常", e);
        }
    }

    public String getWhois(String ip) {
        return whoisClient.queryIpInfoByChaitin(ip);
    }

    public IpInfoResponse getInfoById(String ip) {
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusYears(1);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(start)
                .end(end)
                .queryCount(1)
                .build()
                .addQuery("clientIp", ip)
                .orderBy("logTime", SortOrder.DESC);
        EsNginxDTO esNginxDTO = nginxLogEsClient.queryOne(queryDTO);
        if (esNginxDTO == null) {
            return null;
        }
        return getIpInfo(esNginxDTO);
    }

    private static IpInfoResponse getIpInfo(EsNginxDTO esNginxDTO) {
        IpInfoResponse ipInfo = new IpInfoResponse();
        ipInfo.setIp(esNginxDTO.getClientIp());
        IpInfo clientIpInfo = esNginxDTO.getClientIpInfo();
        String country = clientIpInfo.getCountry();
        ipInfo.setCountry(IpUtils.getTranslatedRegion(country));
        if (Constant.INNER_REGION_CODE.equals(country) || Constant.UNKNOWN_REGION_CODE.equals(country)) {
            ipInfo.setProvince("");
            ipInfo.setCity("");
        } else {
            ipInfo.setProvince(clientIpInfo.getProvince());
            ipInfo.setCity(clientIpInfo.getCity());
        }
        String isp = IpUtils.getTranslatedRegion(clientIpInfo.getIsp());
        String person = IpUtils.getTranslatedRegion(clientIpInfo.getPerson());
        String email = IpUtils.getTranslatedRegion(clientIpInfo.getEmail());
        String phone = IpUtils.getTranslatedRegion(clientIpInfo.getPhone());
        ipInfo.setIsp(isp);

        ipInfo.setPerson(person);
        ipInfo.setEmail(email);
        ipInfo.setPhone(phone);
        ipInfo.setLocation(clientIpInfo.getAddr());
        return ipInfo;
    }
}
