package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.Rule;
import com.telecom.apigateway.model.vo.response.QueryCveRuleResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RuleService extends IService<Rule> {
    Page<QueryCveRuleResponse> queryCveRuleWithConfig(
            Page<?> page,
            @Param("cveName") String cveName,
            @Param("status") String status,
            @Param("category") String category
    );

    List<Rule> crsRuleList();

    void updateTime(String cveId);
}
