package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.mapper.SensitiveRuleMapper;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.SensitiveRule;
import com.telecom.apigateway.model.entity.SensitiveWhiteList;
import com.telecom.apigateway.model.entity.UserInfo;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.enums.SensitiveRuleEnum;
import com.telecom.apigateway.model.vo.request.QuerySensitiveRuleRequest;
import com.telecom.apigateway.model.vo.response.SensitiveRuleLessResponse;
import com.telecom.apigateway.utils.MapUtil;
import com.telecom.apigateway.utils.PageUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class SensitiveRuleService extends ServiceImpl<SensitiveRuleMapper, SensitiveRule> {

    @Resource
    private SensitiveWhiteListService sensitiveWhiteListService;
    @Resource
    private UserInfoService userInfoService;

    @Resource
    private ApiInfoService apiInfoService;

    public IPage<SensitiveRule> queryPage(QuerySensitiveRuleRequest request) {
        Page<SensitiveRule> page = this.lambdaQuery()
                .like(StrUtil.isNotBlank(request.getName()), SensitiveRule::getName, request.getName())
                .in(ArrayUtil.isNotEmpty(request.getLevel()), SensitiveRule::getLevel, request.getLevel())
                .eq(request.getIsEnable() != null, SensitiveRule::getIsEnable, request.getIsEnable())
                .eq(request.getSource() != null, SensitiveRule::getSource, request.getSource())
                .between(request.getStartTime() != null && request.getEndTime() != null,
                        SensitiveRule::getUpdateTime, request.getStartTime(), request.getEndTime())
                .eq(SensitiveRule::getIsDeleted, false)
                .orderByDesc(SensitiveRule::getSource, SensitiveRule::getUpdateTime)
                .page(new Page<>(request.getPageNum(), request.getPageSize()));
        List<SensitiveRule> records = page.getRecords();
        List<String> updater =
                records.stream().map(SensitiveRule::getUpdateUser).distinct().collect(Collectors.toList());
        Map<String, UserInfo> updaterMap = MapUtil.toMapByUnionParams(UserInfo::getUsername,
                userInfoService.listByUsernames(updater));
        for (SensitiveRule record : records) {
            record.setUpdateUser(Optional.ofNullable(updaterMap.get(record.getUpdateUser())).map(UserInfo::getRealName).orElse("异常状态用户"));
        }
        return PageUtils.convertPage(page, records);
    }

    @Override
    public List<SensitiveRule> list() {
        return this.lambdaQuery()
                .eq(SensitiveRule::getIsDeleted, false)
                .list();
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.SENSITIVE_RULE,
            spelArgs = {"#{#request.name}"}
    )
    public void add(SensitiveRule request) {
        if (this.lambdaQuery().eq(SensitiveRule::getName, request.getName()).exists()) {
            throw new BusinessException(ResultCodeEnum.EXISTED_RULE_NAME);
        }
        if (StrUtil.isBlank(request.getName()) ||
                request.getLevel() == null ||
                request.getDetectParts() == null || request.getDetectParts().length == 0 ||
                request.getMatchCase() == null ||
                request.getMatchMethod() == null ||
                StrUtil.isBlank(request.getKeywords()) ||
                request.getIsEnable() == null) {
            throw new BusinessException(ResultCodeEnum.UNSATISFIED_RULE_PARAM);
        }
        if (request.getMatchCase() == SensitiveRuleEnum.MatchCase.REGEX) {
            validRegex(request.getKeywords().split("\n"));
        }
        LocalDateTime now = LocalDateTime.now();
        String id = IdUtil.fastSimpleUUID();
        request.setId(id);
        request.setSource(SensitiveRuleEnum.Source.CUSTOM);
        request.setCode(id);
        request.setCreateUser(StpUtil.getLoginIdAsString());
        request.setCreateTime(now);
        request.setUpdateUser(StpUtil.getLoginIdAsString());
        request.setUpdateTime(now);
        request.setIsDeleted(false);
        this.save(request);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.SENSITIVE_RULE,
            spelArgs = {"#{#result.name}"}
    )
    public SensitiveRule delete(String id) {
        // 判断不可删除
        SensitiveRule rule = this.lambdaQuery()
                .eq(SensitiveRule::getId, id)
                .eq(SensitiveRule::getIsDeleted, false)
                .one();
        if (rule.getSource() == SensitiveRuleEnum.Source.SYSTEM) {
            throw new BusinessException("系统内置规则无法删除");
        }
        rule.setIsDeleted(true);
        rule.setUpdateUser(StpUtil.getLoginIdAsString());
        rule.setUpdateTime(LocalDateTime.now());
        this.updateById(rule);
        return rule;
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.SENSITIVE_RULE,
            spelArgs = {"#{#result}"}
    )
    public List<String> deleteByIds(Collection<String> ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<SensitiveRule> rules = this.lambdaQuery()
                .eq(SensitiveRule::getIsDeleted, false)
                .eq(SensitiveRule::getSource, SensitiveRuleEnum.Source.CUSTOM)
                .in(SensitiveRule::getId, ids)
                .list();

        this.lambdaUpdate()
                .set(SensitiveRule::getIsDeleted, true)
                .set(SensitiveRule::getUpdateTime, LocalDateTime.now())
                .set(SensitiveRule::getUpdateUser, StpUtil.getLoginIdAsString())
                .eq(SensitiveRule::getSource, SensitiveRuleEnum.Source.CUSTOM) // 只删除自定义规则
                .in(SensitiveRule::getId, ids)
                .update();

        return rules.stream().map(SensitiveRule::getName).collect(Collectors.toList());
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.SENSITIVE_RULE,
            description = "启用 {resourceName}: #{@sensitiveRuleService.getName(#id)}"
    )
    public void enable(String id) {
        this.lambdaUpdate()
                .set(SensitiveRule::getIsEnable, true)
                .set(SensitiveRule::getUpdateTime, LocalDateTime.now())
                .set(SensitiveRule::getUpdateUser, StpUtil.getLoginIdAsString())
                .eq(SensitiveRule::getId, id)
                .update();
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.SENSITIVE_RULE,
            description = "启用 {resourceName}: #{@sensitiveRuleService.getName(#ids)}"
    )
    public void enable(String... ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return;
        }
        this.lambdaUpdate()
                .set(SensitiveRule::getIsEnable, true)
                .set(SensitiveRule::getUpdateTime, LocalDateTime.now())
                .set(SensitiveRule::getUpdateUser, StpUtil.getLoginIdAsString())
                .in(SensitiveRule::getId, ids)
                .update();
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.SENSITIVE_RULE,
            description = "禁用 {resourceName}: #{@sensitiveRuleService.getName(#id)}"
    )
    public void disable(String id) {
        this.lambdaUpdate()
                .set(SensitiveRule::getIsEnable, false)
                .set(SensitiveRule::getUpdateTime, LocalDateTime.now())
                .set(SensitiveRule::getUpdateUser, StpUtil.getLoginIdAsString())
                .eq(SensitiveRule::getId, id)
                .update();
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.SENSITIVE_RULE,
            description = "禁用 {resourceName}: #{@sensitiveRuleService.getName(#ids)}"
    )
    public void disable(String[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return;
        }
        this.lambdaUpdate()
                .set(SensitiveRule::getIsEnable, false)
                .set(SensitiveRule::getUpdateTime, LocalDateTime.now())
                .set(SensitiveRule::getUpdateUser, StpUtil.getLoginIdAsString())
                .in(SensitiveRule::getId, ids)
                .update();
    }

    public List<SensitiveRuleLessResponse> queryName(String name) {
        return this.lambdaQuery()
                .select(SensitiveRule::getId, SensitiveRule::getName)
                .like(StrUtil.isNotBlank(name), SensitiveRule::getName, name)
                .eq(SensitiveRule::getIsDeleted, false)
                .orderByAsc(SensitiveRule::getName)
                .list().stream().map(sensitiveRule -> SensitiveRuleLessResponse.builder()
                        .id(sensitiveRule.getId())
                        .name(sensitiveRule.getName()).build())
                .collect(Collectors.toList());
    }

    private void validRegex(String[] regexes) {
        try {
            for (String regex : regexes) {
                Pattern.compile(regex);
            }
        } catch (PatternSyntaxException e) {
            throw new BusinessException(ResultCodeEnum.RULE_REGEX_ERROR);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.SENSITIVE_RULE,
            description = "新增 {resourceName} 白名单api: #{@sensitiveRuleService.getApiName(#apiIds)}"
    )
    public void addWhitelistApi(String id, String[] apiIds) {
        sensitiveWhiteListService.addWhitelistApi(id, apiIds);
        lambdaUpdate()
                .eq(SensitiveRule::getId, id)
                .set(SensitiveRule::getUpdateTime, LocalDateTime.now())
                .set(SensitiveRule::getUpdateUser, StpUtil.getLoginIdAsString())
                .update();
    }

    public List<SensitiveWhiteList> getWhiteList(String id) {
        return sensitiveWhiteListService.getBySensitiveRuleId(id);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.FULL_UPDATE,
            resourceType = ResourceTypeEnum.SENSITIVE_RULE,
            spelArgs = {"#{#request.name}", "#{#result}"}
    )
    public SensitiveRule update(SensitiveRule request) {
        SensitiveRule originData = this.getById(request.getId());
        if (!originData.getName().equals(request.getName()) &&
                this.lambdaQuery().eq(SensitiveRule::getName, request.getName()).eq(SensitiveRule::getIsDeleted,
                        false).exists()) {
            throw new BusinessException(ResultCodeEnum.EXISTED_RULE_NAME);
        }
        if (StrUtil.isBlank(request.getName()) ||
                request.getLevel() == null ||
                request.getDetectParts() == null || request.getDetectParts().length == 0 ||
                request.getMatchCase() == null ||
                request.getMatchMethod() == null ||
                StrUtil.isBlank(request.getKeywords()) ||
                request.getIsEnable() == null) {
            throw new BusinessException(ResultCodeEnum.UNSATISFIED_RULE_PARAM);
        }
        if (request.getMatchCase() == SensitiveRuleEnum.MatchCase.REGEX) {
            validRegex(request.getKeywords().split("\n"));
        }
        LocalDateTime now = LocalDateTime.now();
        request.setSource(SensitiveRuleEnum.Source.CUSTOM);
        request.setCode(request.getId());
        request.setUpdateTime(now);
        request.setCreateUser(StpUtil.getLoginIdAsString());
        request.setIsDeleted(false);
        this.updateById(request);
        return request;
    }

    public String getName(String id) {
        return this.lambdaQuery()
                .eq(SensitiveRule::getId, id)
                .eq(SensitiveRule::getIsDeleted, false)
                .oneOpt()
                .map(SensitiveRule::getName)
                .orElse(null);
    }

    public String getName(Collection<String> ids) {
        if (ids.isEmpty()) {
            return null;
        }
        return this.lambdaQuery()
                .in(SensitiveRule::getId, ids)
                .eq(SensitiveRule::getIsDeleted, false)
                .oneOpt()
                .map(SensitiveRule::getName)
                .orElse(null);
    }

    public String getName(String[] ids) {
        return getName(Arrays.asList(ids));
    }

    public String getApiName(String apiId) {
        return apiInfoService.getById(apiId).getName();
    }

    public List<String> getApiName(Collection<String> apiIds) {
        if (apiIds.isEmpty()) {
            return null;
        }
        return apiInfoService.lambdaQuery()
                .in(ApiInfo::getId, apiIds)
                .list()
                .stream()
                .map(ApiInfo::getName)
                .collect(Collectors.toList());
    }

    public List<String> getApiName(String[] apiIds) {
        return getApiName(Arrays.asList(apiIds));
    }
}

