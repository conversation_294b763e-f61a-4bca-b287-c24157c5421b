package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.vo.request.QueryOperationLogRequest;
import com.telecom.apigateway.model.vo.response.OperationLogResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 操作日志业务服务
 */
@Service
@RequiredArgsConstructor
public class OperationLogBusinessService {

    private final OperationLogService operationLogService;

    /**
     * 分页查询操作日志
     *
     * @param request 查询请求
     * @return 分页结果
     */
    public Page<OperationLogResponse> queryPage(QueryOperationLogRequest request) {
        return operationLogService.queryPage(request);
    }
}
