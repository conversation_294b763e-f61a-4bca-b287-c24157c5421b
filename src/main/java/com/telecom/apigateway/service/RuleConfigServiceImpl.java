package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.RuleConfigMapper;
import com.telecom.apigateway.model.entity.RuleConfig;
import com.telecom.apigateway.model.enums.CategoryEnum;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Repository
public class RuleConfigServiceImpl extends ServiceImpl<RuleConfigMapper, RuleConfig> implements RuleConfigService {
    @Override
    public Optional<RuleConfig> optByRuleId(String cveId) {
        return this.lambdaQuery()
                .eq(RuleConfig::getRuleId, cveId)
                .oneOpt();
    }

    @Override
    public List<RuleConfig> crsRuleConfigList() {
        return this.lambdaQuery()
                .eq(RuleConfig::getCategory, CategoryEnum.SYSTEM.getCode())
                .list();
    }
}
