package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.Blocklist;
import com.telecom.apigateway.model.vo.request.BlocklistQueryRequest;

import java.util.List;
import java.util.Optional;

public interface BlocklistService extends IService<Blocklist> {

    /**
     * 获取列表数据
     */
    List<Blocklist> list();

    /**
     * 添加记录
     */
    Blocklist add(Blocklist blocklist);

    /**
     * 启用记录
     */
    Blocklist enable(String blockId);

    /**
     * 禁用记录
     */
    Blocklist disable(String blockId);

    /**
     * 删除记录
     */
    void delete(String blockId);

    /**
     * 批量删除记录
     */
    void batchDelete(List<String> blockIds);

    /**
     * 分页查询记录
     */
    Page<Blocklist> findAll(BlocklistQueryRequest pageRequest);

    /**
     * 根据 ID 查询记录
     */
    Optional<Blocklist> findById(String blockId);

    /**
     * 根据 ID 列表查询记录
     */
    List<Blocklist> findAllById(List<String> blockIds);

    /**
     * 根据 ID 删除记录
     */
    void deleteById(String blockId);

    /**
     * 根据 ID 列表删除记录
     */
    void deleteAllById(List<String> blockIds);

    void deleteByApiId(String apiId);

    void updateApiId(List<String> fromApiIds, String toApiId);
}
