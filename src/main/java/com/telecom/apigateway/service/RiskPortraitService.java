package com.telecom.apigateway.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.dto.EsRiskRuleDTO;
import com.telecom.apigateway.model.entity.IpInfo;
import com.telecom.apigateway.model.entity.Rule;
import com.telecom.apigateway.model.enums.RiskLevelEnum;
import com.telecom.apigateway.model.vo.request.QueryPortraitRequest;
import com.telecom.apigateway.model.vo.response.ApplicationResponse;
import com.telecom.apigateway.model.vo.response.PortraitHistoryResponse;
import com.telecom.apigateway.model.vo.response.PortraitQueryResponse;
import com.telecom.apigateway.model.vo.response.RiskLogStatResponse;
import com.telecom.apigateway.model.vo.response.StatCount;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import com.telecom.apigateway.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeValuesSourceBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.ParsedComposite;
import org.elasticsearch.search.aggregations.bucket.composite.TermsValuesSourceBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-06
 */
@Slf4j
@Service
public class RiskPortraitService {
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Resource
    private RuleService ruleService;
    @Resource
    private NginxLogEsClient nginxLogEsClient;
    @Resource
    private ApplicationBusinessService applicationBusinessService;
    @Resource
    private NginxAccessLogService nginxAccessLogService;

    /**
     * 分页查询 获取用户画像
     * 多关键词模糊查询
     */
    public IPage<PortraitQueryResponse> queryPortraitPage(QueryPortraitRequest request) {
        if (CollUtil.isEmpty(request.getKeywords())) {
            return Page.of(0, 0, 0);
        }
        request.format();
        List<Rule> ruleList = ruleService.list();
        Set<String> keywords = request.getKeywords();

        String crsShortRuleId = "";
        outer:
        for (Rule rule : ruleList) {
            for (Iterator<String> it = keywords.iterator(); it.hasNext(); ) {
                String keyword = it.next();
                if (rule.getAttackType().toLowerCase().contains(keyword.toLowerCase())) {
                    it.remove();
                    crsShortRuleId = rule.getRuleId();
                    break outer;
                }
            }
        }
        if (keywords.contains(Constant.INNER_REGION)) {
            keywords.remove(Constant.INNER_REGION);
            keywords.add(Constant.INNER_REGION_CODE);
        }
        if (keywords.contains(Constant.UNKNOWN_REGION)) {
            keywords.remove(Constant.UNKNOWN_REGION);
            keywords.add(Constant.UNKNOWN_REGION_CODE);
        }

        // 处理剩余的关键词并构建模糊查询
        List<String> finalKeywords = keywords.stream()
                .map(k -> "*" + k + "*")
                .collect(Collectors.toList());

        if (StrUtil.isNotBlank(crsShortRuleId)) {
            finalKeywords.add(crsShortRuleId);
        }

        String[] fields = {
                "clientIp", "clientIpInfo.country", "clientIpInfo.province", "clientIpInfo.city", "clientIpInfo.isp",
                "riskRules.crsShortRuleId"
        };

        // 构建查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        finalKeywords.forEach(keyword -> {
            BoolQueryBuilder keywordQuery = QueryBuilders.boolQuery();
            Arrays.stream(fields).forEach(field -> {
                        if (field.contains(".")) {
                            keywordQuery.should(
                                    QueryBuilders.nestedQuery(
                                            field.split("\\.")[0],
                                            QueryBuilders.boolQuery().must(QueryBuilders.wildcardQuery(field, keyword)),
                                            ScoreMode.None
                                    )
                            );
                        } else {
                            keywordQuery.should(QueryBuilders.wildcardQuery(field, keyword));
                        }
                    }
            );
            keywordQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(keywordQuery);
        });

        // 构建 composite 聚合
        List<CompositeValuesSourceBuilder<?>> sources = Collections.singletonList(
                new TermsValuesSourceBuilder("clientIp").field("clientIp")
        );

        CompositeAggregationBuilder cagg = AggregationBuilders.composite("my_agg", sources)
                .subAggregation(AggregationBuilders.topHits("my_sub_agg")
                        .size(1).sort(SortBuilders.fieldSort("logTime").order(SortOrder.DESC)))
                .size(request.getPageSize());
        if (StrUtil.isNotBlank(request.getLastKey())) {
            cagg.aggregateAfter(Collections.singletonMap("clientIp", request.getLastKey()));
        }

        // 构建查询
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withAggregations(cagg)
                .build();
        query.setMaxResults(1);
        // 执行查询
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(query, EsNginxDTO.class);

        // 解析结果
        List<PortraitQueryResponse> list = new ArrayList<>();
        if (searchHits.hasAggregations()) {
            Aggregations aggregations = (Aggregations) searchHits.getAggregations().aggregations();
            List<? extends MultiBucketsAggregation.Bucket> buckets =
                    ((ParsedComposite) aggregations.get("my_agg")).getBuckets();

            for (MultiBucketsAggregation.Bucket bucket : buckets) {
                String esNginxLogStr =
                        ((ParsedTopHits) bucket.getAggregations().get("my_sub_agg")).getHits().getAt(0).getSourceAsString();
                EsNginxDTO esNginxDTO = JSONUtil.parseObj(esNginxLogStr).toBean(EsNginxDTO.class);

                PortraitQueryResponse portraitQueryResponse = getPortraitQueryResponse(esNginxDTO);
                list.add(portraitQueryResponse);
            }
        }

        // 分页结果
        Page<PortraitQueryResponse> pageData = new Page<>();
        pageData.setTotal(list.size());
        pageData.setRecords(list);
        pageData.setCurrent(request.getPageNum());
        pageData.setSize(request.getPageSize());
        return pageData;
    }

    private static PortraitQueryResponse getPortraitQueryResponse(EsNginxDTO esNginxDTO) {
        PortraitQueryResponse portraitQueryResponse = new PortraitQueryResponse();
        portraitQueryResponse.setIp(esNginxDTO.getClientIp());
        IpInfo clientIpInfo = esNginxDTO.getClientIpInfo();

        String country = clientIpInfo.getCountry();
        portraitQueryResponse.setCountry(IpUtils.getTranslatedRegion(country));
        if (Constant.INNER_REGION_CODE.equals(country) || Constant.UNKNOWN_REGION_CODE.equals(country)) {
            portraitQueryResponse.setProvince("");
            portraitQueryResponse.setCity("");
        } else {
            portraitQueryResponse.setProvince(clientIpInfo.getProvince());
            portraitQueryResponse.setCity(clientIpInfo.getCity());
        }
        String isp = IpUtils.getTranslatedRegion(clientIpInfo.getIsp());
        portraitQueryResponse.setIsp(isp);
        portraitQueryResponse.setLocation(clientIpInfo.getAddr());
        return portraitQueryResponse;
    }

    /**
     * 获取 IP 历史记录 溯源
     */
    public List<PortraitHistoryResponse> getIpHistory(String ip, LocalDateTime startTime, LocalDateTime endTime) {
        List<PortraitHistoryResponse> resultList = new ArrayList<>();
        try {
            List<ApplicationResponse> applicationResponses = applicationBusinessService.listWithBizName();
            // map,  applicationId 和 App
            Map<String, ApplicationResponse> appMap =
                    applicationResponses.stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                            Function.identity(), (a, b) -> a));

            List<Rule> rules = ruleService.list();
            //
            Map<String, Rule> sysRuleMap = rules.stream()
                    .collect(Collectors.toMap(Rule::getRuleId, rule -> rule, (k1, k2) -> k1));

            EsQueryDTO queryDTO = EsQueryDTO.builder()
                    .pageNum(1)
                    .pageSize(10000)
                    .start(startTime)
                    .end(endTime)
                    .build()
                    .addQuery("clientIp", ip)
                    .addQuery("requestResourceType", Constant.Api.REQUEST_RESOURCE_TYPE_API)
                    .orderBy("logTime", SortOrder.DESC);

            DateTimeFormatter dtf = DateTimeFormatter.ofPattern(Constant.DATE_PATTERN);

            Page<EsNginxDTO> esNginxDTOPage = nginxLogEsClient.queryPage(queryDTO);
            List<EsNginxDTO> records = esNginxDTOPage.getRecords();
            if (CollUtil.isEmpty(records)) {
                return Collections.emptyList();
            }
            // 根据 date 分组
            LinkedHashMap<String, List<EsNginxDTO>> mapByDate = records.stream().collect(Collectors.groupingBy(
                    ele -> ele.getDate().format(dtf),
                    LinkedHashMap::new,
                    Collectors.toList()
            ));

            Set<String> riskTypesAndApiIdAndDateList = new HashSet<>();
            Set<String> normalLogDateAndApiIdList = new HashSet<>();
            mapByDate.forEach((date, nginxAccessLogDTOS) -> {
                riskTypesAndApiIdAndDateList.clear();
                normalLogDateAndApiIdList.clear();
                for (EsNginxDTO esNginxDTO : nginxAccessLogDTOS) {
                    if (esNginxDTO.getRiskRules() == null) {
                        // 正常日志
                        String key = esNginxDTO.getDate().format(dtf) + esNginxDTO.getApiId();
                        if (normalLogDateAndApiIdList.add(key)) {
                            ApplicationResponse application = appMap.get(esNginxDTO.getAppId());
                            PortraitHistoryResponse phResponse =
                                    new PortraitHistoryResponse(esNginxDTO, application, null, null);
                            resultList.add(phResponse);
                        }
                    } else {
                        List<EsRiskRuleDTO> riskRules = esNginxDTO.getRiskRules();
                        for (EsRiskRuleDTO riskRule : riskRules) {
                            String key = esNginxDTO.getDate().format(dtf) + esNginxDTO.getApiId() + riskRule.getType();
                            if (riskTypesAndApiIdAndDateList.add(key)) {
                                // 添加
                                ApplicationResponse application = appMap.get(esNginxDTO.getAppId());
                                Rule sysRule = sysRuleMap.get(riskRule.getCrsShortRuleId());
                                PortraitHistoryResponse phResponse =
                                        new PortraitHistoryResponse(esNginxDTO, application, sysRule, riskRule);
                                resultList.add(phResponse);
                            } else {
                                // count + 1
                                resultList.stream()
                                        .filter(ele ->
                                                "attack".equals(ele.getType()) &&
                                                        ele.getDate().equals(esNginxDTO.getDate()) &&
                                                        ele.getApiId().equals(esNginxDTO.getApiId()) &&
                                                        ele.getAttackTypeId().equals(riskRule.getType()))
                                        .findFirst()
                                        .ifPresent(PortraitHistoryResponse::plusCount);
                            }
                        }
                    }

                }

            });
        } catch (Exception e) {
            log.error("getIpHistory error", e);
        }
        return resultList;
    }

    // public PortraitHistoryResponse buildPortraitHistoryResponse(EsNginxDTO ngLog, ApplicationResponse application) {
    //     PortraitHistoryResponse data = PortraitHistoryResponse.builder()
    //             .ip(ngLog.getClientIp())
    //             .apiId(ngLog.getApiId())
    //             .uri(ngLog.getUri())
    //             .date(ngLog.getDate())
    //             .attackType(sysRule.getAttackType())
    //             .attackTypeId(riskRule.getType())
    //             .attackLevel(RiskLevelEnum.getRiskLevel(riskRule.getScore()).getCode())
    //             .count(1)
    //             .appId(ngLog.getAppId())
    //             .appName(application.getName())
    //             .appType(application.getType().name())
    //             .type("attack")
    //             .build();
    // }

    /**
     * 统计风险画像
     */
    public RiskLogStatResponse statPortrait() {
        List<Rule> ruleList = ruleService.list();
        // map 数据,ruleId 为key: value 为规则名称
        Map<String, String> ruleId2NameMap = ruleList.stream().collect(Collectors.toMap(Rule::getRuleId,
                Rule::getAttackType));
        // map type:name
        Map<String, String> type2NameMap = ruleList.stream().collect(Collectors.toMap(Rule::getType,
                Rule::getAttackType, (v1, v2) -> v1));

        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusMonths(1);
        // ************ 最近攻击 top5 ************
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(start)
                .end(end)
                .pageNum(1)
                .pageSize(5).build()
                .addExistQuery("riskRules")
                .orderBy("logTime", SortOrder.DESC);

        Page<EsNginxDTO> esNginxDTOPage = nginxLogEsClient.queryPage(queryDTO);
        List<EsNginxDTO> records = esNginxDTOPage.getRecords();

        List<RiskLogStatResponse.Recent> recentList = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            for (EsNginxDTO record : records) {
                List<EsRiskRuleDTO> riskRules = record.getRiskRules();
                for (EsRiskRuleDTO riskRule : riskRules) {
                    RiskLogStatResponse.Recent build = RiskLogStatResponse.Recent.builder()
                            .ip(record.getClientIp())
                            .location(record.getClientIpInfo().getAddr())
                            .attackType(ruleId2NameMap.get(riskRule.getCrsShortRuleId()))
                            .attackLevel(RiskLevelEnum.getRiskLevel(riskRule.getScore()).getCode())
                            .datetime(record.getLogTime())
                            .build();
                    recentList.add(build);
                    if (recentList.size() >= 5) {
                        break;
                    }
                }
                if (recentList.size() == 5) {
                    break;
                }
            }
        }

        List<String> appIds = applicationBusinessService.listWithBizName().stream()
                .map(ApplicationResponse::getApplicationId).collect(Collectors.toList());
        EsQueryDTO queryDTO1 = EsQueryDTO.builder()
                .queryCount(5)
                .start(start)
                .end(end)
                .build()
                .addMultipleQuery("appId", appIds)
                .addExistQuery("riskRules");
        // ************ 攻击频次1个月 top5 ************
        List<RiskLogStatResponse.Frequency> frequencies = new ArrayList<>();
        List<StatCount> statCounts = nginxAccessLogService.groupCount(queryDTO1, "clientIp");
        for (StatCount statCount : statCounts) {
            frequencies.add(new RiskLogStatResponse.Frequency(statCount.getLabel(), statCount.getCount()));
        }
        // ************ 攻击城市 top5 ************
        List<String> cities = new ArrayList<>();
        List<StatCount> statCountsCity = nginxAccessLogService.groupCount(queryDTO1, "clientIpInfo.city");
        for (StatCount statCount : statCountsCity) {
            String city = IpUtils.getTranslatedRegion(statCount.getLabel());
            cities.add(city);
        }
        // ************ 姓名 top5 ************
        List<String> names = new ArrayList<>();
        List<StatCount> statCountsPerson = nginxAccessLogService.groupCount(queryDTO1, "clientIpInfo.person");
        for (StatCount countsPerson : statCountsPerson) {
            names.add(countsPerson.getLabel());
        }
        // ************ 攻击类型 top5 ************
        List<RiskLogStatResponse.AttackType> attackTypes = new ArrayList<>();
        List<StatCount> statCountsAttackTypes = nginxAccessLogService.groupCount(queryDTO1, "riskRules.type");
        for (StatCount statCountsAttackType : statCountsAttackTypes) {
            attackTypes.add(
                    new RiskLogStatResponse.AttackType(
                            type2NameMap.getOrDefault(statCountsAttackType.getLabel(), "其他"), "0"));
        }
        return RiskLogStatResponse.builder()
                .recents(recentList)
                .frequencies(frequencies)
                .cities(cities)
                .names(names)
                .attackTypes(attackTypes)
                .build();
    }


}
