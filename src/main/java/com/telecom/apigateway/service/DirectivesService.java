package com.telecom.apigateway.service;

import com.telecom.apigateway.model.dto.ValidateDirectiveDTO;
import com.telecom.apigateway.model.vo.request.CustomRuleConfigRequest;
import com.telecom.apigateway.model.vo.request.OwaspRuleConfigRequest;
import com.telecom.apigateway.model.vo.request.ValidateDirectivesRequest;

public interface DirectivesService {
    
    /**
     * 验证规则内容
     *
     * @param request 请求参数
     * @return 是否验证通过
     */
    ValidateDirectiveDTO validateDirectives(ValidateDirectivesRequest request);
    
    /**
     * 配置OWASP规则
     *
     * @param request 请求参数
     */
    void configOwaspRule(OwaspRuleConfigRequest request);
    
    /**
     * 配置自定义规则
     *
     * @param request 请求参数
     */
    void configCustomRule(CustomRuleConfigRequest request);
} 