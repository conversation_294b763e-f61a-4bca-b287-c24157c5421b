package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpInterface;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义权限加载接口实现类
 */
@Component
@AllArgsConstructor
public class StpInterfaceImpl implements StpInterface {
    private ApplicationPermissionService applicationPermissionService;

    /**
     * 返回有权限的应用
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        return applicationPermissionService.getApplicationPermissions(loginId.toString());
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        return applicationPermissionService.getRoleIdsByUsername(loginId.toString());
    }

}

