package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.dto.CheckCveRuleDTO;
import com.telecom.apigateway.model.vo.request.BatchConfigCveRuleRequest;
import com.telecom.apigateway.model.vo.request.ConfigCveRuleRequest;
import com.telecom.apigateway.model.vo.request.DeleteCveRuleRequest;
import com.telecom.apigateway.model.vo.request.QueryCveRuleRequest;
import com.telecom.apigateway.model.vo.response.QueryCveRuleResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface CveRuleBusinessService {

    List<CheckCveRuleDTO> checkCveRule(MultipartFile[] files);

    List<String> saveCveRule(MultipartFile[] files);

    Page<QueryCveRuleResponse> queryCveRule(QueryCveRuleRequest request);

    String queryCveRuleDetail(String cveId);

    void configCveRule(ConfigCveRuleRequest request);

    List<String> deleteCveRule(DeleteCveRuleRequest request);

    void batchConfigCveRule(BatchConfigCveRuleRequest request);
}
