package com.telecom.apigateway.service;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.SensitiveLogMapper;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.SensitiveLog;
import com.telecom.apigateway.model.vo.response.SensitiveTopResponse;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class SensitiveLogService extends ServiceImpl<SensitiveLogMapper, SensitiveLog> {
    void deleteByApiId(String apiId) {
        lambdaUpdate().eq(SensitiveLog::getApiId, apiId).remove();
    }

    /**
     * ruleId ,count
     */
    public List<SensitiveTopResponse.TopData> statTopRuleOfContent(LocalDateTime startTime, LocalDateTime endTime,
                                                                   Integer queryCount) {
        return baseMapper.statTopRuleOfContent(startTime, endTime, queryCount);
    }

    public Integer sumContentOfRule(LocalDateTime startTime, LocalDateTime endTime) {
        Integer i = baseMapper.sumContentOfRule(startTime, endTime);
        if (i == null) {
            return 0;
        }
        return i;
    }

    public void deleteByRuleId(String ruleId) {
        lambdaUpdate()
                .eq(SensitiveLog::getRuleId, ruleId)
                .remove();
    }

    public void updateAppIdByApiId(List<ApiInfo> apiList) {
        for (ApiInfo apiInfo : apiList) {
            String appId = apiInfo.getAppId();
            String apiId = apiInfo.getId();
            if (StrUtil.isBlank(appId) || StrUtil.isBlank(apiId)) {
                continue;
            }
            lambdaUpdate()
                    .set(SensitiveLog::getAppId, appId)
                    .eq(SensitiveLog::getApiId, apiId)
                    .update();
        }
    }
}
