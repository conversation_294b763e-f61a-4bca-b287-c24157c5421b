package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import com.telecom.apigateway.model.dto.HistoryMergeTaskDTO;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.entity.ApplicationCorrectPolicy;
import com.telecom.apigateway.model.enums.CorrectPolicyStatusEnum;
import com.telecom.apigateway.model.enums.MergeTaskStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 历史数据合并服务
 * 使用Redis存储任务状态
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HistoryMergeService {

    private final ApiMergeService apiMergeService;
    private final ApplicationService applicationService;
    private final ApiMergeBizService apiMergeBizService;
    private final HistoryMergeTaskRedisService taskRedisService;
    private final ApplicationCorrectPolicyService applicationCorrectPolicyService;

    /**
     * 异步执行历史数据合并
     * 先合并API，再合并应用
     *
     * @return 任务ID
     */
    @Async("historyMergeTaskExecutor")
    public CompletableFuture<String> historyMerge() {
        // 创建任务记录
        String taskId = taskRedisService.createTask(StpUtil.getLoginIdAsString());

        log.info("[HISTORY_MERGE] 开始执行历史数据合并任务，任务ID: {}", taskId);

        try {
            // 更新任务状态为执行中
            taskRedisService.updateTaskStatus(taskId, MergeTaskStatus.RUNNING, "开始执行历史数据合并");

            process();
            // 任务完成
            taskRedisService.updateTaskStatus(taskId, MergeTaskStatus.SUCCESS, "历史数据合并完成");

            log.info("[HISTORY_MERGE] 历史数据合并任务完成，任务ID: {}", taskId);

        } catch (Exception e) {
            log.error("[HISTORY_MERGE] 历史数据合并任务失败，任务ID: {}", taskId, e);
            taskRedisService.setTaskError(taskId, "合并失败: " + e.getMessage());
        }

        return CompletableFuture.completedFuture(taskId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void process() {
        // 查询未归档。api，process_application_policy 为 true 则跳过应用部分。
        // 排序， 有 uri的优先处理
        // 查询所有已启用的应用修正策略
        // 查询所有已启用的 api 修正策略
        // 匹配应用忽略策略
        // 匹配 api 忽略策略
        // 匹配应用合并策略
        // 匹配 api 合并策略
    }

    /**
     * 合并API数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void mergeApis(String taskId) {
        log.info("[HISTORY_MERGE] 开始合并API数据");

        // 更新任务状态
        taskRedisService.updateTaskStatus(taskId, MergeTaskStatus.MERGING_API, "正在合并API数据");

        // 获取所有需要合并的API策略
        List<ApiMerge> apiMerges = apiMergeService.lambdaQuery()
                .eq(ApiMerge::getDeleted, false)
                .eq(ApiMerge::getEnable, true) // 只处理未启用的策略
                .list();

        taskRedisService.updateApiProgress(taskId, 0, apiMerges.size());

        int processedCount = 0;
        for (ApiMerge apiMerge : apiMerges) {
            try {
                // 启用API合并策略
                apiMergeBizService.doMerge(apiMerge);
                processedCount++;

                // 更新进度
                taskRedisService.updateApiProgress(taskId, processedCount, apiMerges.size());

                log.info("[HISTORY_MERGE] API合并完成: {} ({}/{})", apiMerge.getName(), processedCount, apiMerges.size());

            } catch (Exception e) {
                log.error("[HISTORY_MERGE] API合并失败: {}", apiMerge.getName(), e);
                // 继续处理其他API，不中断整个流程
            }
        }

        log.info("[HISTORY_MERGE] API数据合并完成，共处理 {} 个策略", processedCount);
    }

    /**
     * 合并应用数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void mergeApplications(String taskId) {
        log.info("[HISTORY_MERGE] 开始合并应用数据");

        // 更新任务状态
        taskRedisService.updateTaskStatus(taskId, MergeTaskStatus.MERGING_APPLICATION, "正在合并应用数据");

        // 获取所有需要合并的应用（这里可以根据具体业务逻辑来确定哪些应用需要合并）
        // 示例：获取所有有子应用的主应用
        List<ApplicationCorrectPolicy> merges = applicationCorrectPolicyService.lambdaQuery()
                .eq(ApplicationCorrectPolicy::getDeleted, false)
                .eq(ApplicationCorrectPolicy::getStatus, CorrectPolicyStatusEnum.ENABLED)
                .list();

        taskRedisService.updateApplicationProgress(taskId, 0, merges.size());

        int processedCount = 0;
        for (ApplicationCorrectPolicy merge : merges) {
            try {
                // 执行应用合并逻辑
                applicationCorrectPolicyService.mergeApplication(merge);
                processedCount++;

                // 更新进度
                taskRedisService.updateApplicationProgress(taskId, processedCount, merges.size());

                log.info("[HISTORY_MERGE] 应用合并完成: {} ({}/{})", merge.getMergedName(), processedCount, merges.size());

            } catch (Exception e) {
                log.error("[HISTORY_MERGE] 应用合并失败: {}", merge.getMergedName(), e);
                // 继续处理其他应用，不中断整个流程
            }
        }

        log.info("[HISTORY_MERGE] 应用数据合并完成，共处理 {} 个应用", processedCount);
    }
    /**
     * 获取任务状态
     */
    public HistoryMergeTaskDTO getTaskStatus(String taskId) {
        return taskRedisService.getTask(taskId);
    }

    /**
     * 获取最新的任务状态
     */
    public HistoryMergeTaskDTO getLatestTask() {
        return taskRedisService.getLatestTask();
    }

    /**
     * 检查是否有正在执行的任务
     */
    public boolean hasRunningTask() {
        return taskRedisService.hasRunningTask();
    }
}
