package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.client.CrsClient;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.common.excption.WafException;
import com.telecom.apigateway.mapper.RiskLogMapper;
import com.telecom.apigateway.model.dto.CrsDetectResponseDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.dto.EsRiskRuleDTO;
import com.telecom.apigateway.model.entity.RiskLog;
import com.telecom.apigateway.model.entity.Rule;
import com.telecom.apigateway.model.vo.request.QueryPortraitRequest;
import com.telecom.apigateway.model.vo.response.*;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import com.telecom.apigateway.utils.DateTimeUtils;
import com.telecom.apigateway.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-09-25
 */
@Slf4j
@Service
public class RiskLogService extends ServiceImpl<RiskLogMapper, RiskLog> {

    @Resource
    private RiskPortraitService riskPortraitService;
    @Resource
    private NginxAccessLogService nginxAccessLogService;
    @Resource
    private RuleService ruleService;
    @Resource
    private CrsClient crsClient;

    public static List<EsRiskRuleDTO> getRiskRules(CrsDetectResponseDTO crsResult, List<Rule> rules) {
        List<EsRiskRuleDTO> riskRules = new ArrayList<>();

        for (CrsDetectResponseDTO.MatchedData risk : crsResult.getMatchedData()) {
            // 去除后三位
            String shortRuleId = risk.getRuleId().substring(0, risk.getRuleId().length() - 3);

            // 获取触发规则对应规则集信息
            Rule rule = rules.stream()
                    .filter(r -> shortRuleId.equals(r.getRuleId()))
                    .findFirst()
                    .orElse(null);
            if (rule == null) {
                continue;
            }

            // 获取规则集检测得分
            String[] collection = crsResult.getCollections().stream()
                    .filter(c -> c[1].equals(rule.getScoreKeyword()))
                    .findFirst()
                    .orElse(null);
            if (collection == null) {
                continue;
            }
            Integer score = Integer.parseInt(collection[3]);

            EsRiskRuleDTO riskRule = EsRiskRuleDTO.builder()
                    .crsRuleId(risk.getRuleId())
                    .crsShortRuleId(shortRuleId)
                    .crsSeverity(risk.getSeverity())
                    .content(risk.getMessage())
                    .score(score)
                    .type(rule.getType())
                    .isDealt(false)
                    .build();
            riskRules.add(riskRule);
        }
        return riskRules;
    }

    /**
     * 攻击画像
     */
    public RiskLogStatResponse statPortrait() {
        return riskPortraitService.statPortrait();
    }

    /**
     * 攻击者画像查询
     */
    public IPage<PortraitQueryResponse> queryPortraitPage(QueryPortraitRequest request) {
        return riskPortraitService.queryPortraitPage(request);
    }

    /**
     * ip 行为溯源
     * 顺序集合返回, 按照时间逆序
     */
    public List<PortraitHistoryResponse> getIpHistory(String ip, Integer range, LocalDateTime start,
                                                      LocalDateTime end) {
        if (start != null && end != null && start.plusDays(30).isBefore(end)) {
            throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "查询范围不能超过30天");
        }
        Pair<LocalDateTime, LocalDateTime> timePair = DateTimeUtils.rangeTime(range, start, end);
        return riskPortraitService.getIpHistory(ip, timePair.getLeft(), timePair.getRight());
    }

    public ApiAttackStatResponse statAttack(String apiId) {
        LocalDateTime end_trend = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0).plusDays(1);
        // *********** api 攻击量趋势 ***********
        LocalDateTime start_trend = end_trend.minusDays(8);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(start_trend)
                .end(end_trend)
                .build()
                .addQuery("apiId", apiId)
                .addExistQuery("riskRules");
        List<StatCount> statTrendList = nginxAccessLogService.groupByDate(queryDTO);
        // *********** 攻击国家/城市分布 ***********
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusDays(7);
        queryDTO.setQueryCount(5);
        queryDTO.setStart(start);
        List<StatCount> statCountryList = nginxAccessLogService.groupCount(queryDTO, "clientIpInfo.country");
        statCountryList.forEach(ele -> ele.setLabel(IpUtils.getTranslatedRegion(ele.getLabel())));

        queryDTO.setQueryCount(10);
        List<StatCount> statCityList = nginxAccessLogService.groupCount(queryDTO, "clientIpInfo.city");
        statCityList.forEach(ele -> ele.setLabel(IpUtils.getTranslatedRegion(ele.getLabel())));
        // *********** api 攻击量类型分布 ***********
        queryDTO.setQueryCount(5);
        List<StatCount> statAttackTypeList = nginxAccessLogService.groupCount(queryDTO, "riskRules.type");
        List<Rule> riskRules = ruleService.list();
        // map ruleId: attackType
        Map<String, String> ruleId2AttackType = riskRules.stream().collect(Collectors.toMap(Rule::getType,
                Rule::getAttackType, (v1, v2) -> v1));
        statAttackTypeList.forEach(e -> e.setLabel(ruleId2AttackType.get(e.getLabel())));

        ApiAttackStatResponse apiAttackStatResponse = new ApiAttackStatResponse();
        apiAttackStatResponse.setCountries(statCountryList);
        apiAttackStatResponse.setCities(statCityList);
        apiAttackStatResponse.setAttackTrends(statTrendList);
        apiAttackStatResponse.setAttackTypes(statAttackTypeList);
        return apiAttackStatResponse;
    }

    /**
     * 分析日志威胁信息
     *
     * @param httpRequestInfo 请求信息, 需要满足 http raw 格式, 不然调用 crs 可能会报错
     * @return 检测结果
     */
    public List<EsRiskRuleDTO> analyseHttpInfoRisk(String httpRequestInfo) throws WafException {
        // 请求 crs 分析结果
        CrsDetectResponseDTO crsResult = crsClient.analyseHttpInfoRisk(httpRequestInfo);

        if (crsResult == null || crsResult.getMatchedData() == null || crsResult.getMatchedData().isEmpty()) {
            return Collections.emptyList();
        }

        // 获取规则列表并构建规则关键字与规则ID的映射
        List<Rule> rules = ruleService.list();

        return getRiskRules(crsResult, rules);
    }
}
