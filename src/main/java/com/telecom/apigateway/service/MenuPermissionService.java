package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.MenuPermission;

import java.util.Collection;
import java.util.List;

public interface MenuPermissionService extends IService<MenuPermission> {
    
    /**
     * 获取角色的权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<MenuPermission> getPermissionsByRoleId(Long roleId);

    List<MenuPermission> getByCodes(Collection<String> permissionCodes);

    List<MenuPermission> getByParentCodes(Collection<String> parentCode);

    /**
     * 获取全部权限，包括隐藏的权限（即有parent code的子项）
     * @param permissionCodes
     * @return
     */
    List<MenuPermission> getAllPermissionByCodes(Collection<String> permissionCodes);

    /**
     * 获取展示的权限, 即不展示有 parent code 的权限
     */
    List<MenuPermission> getDisplayPermissionsByRoleId();
}