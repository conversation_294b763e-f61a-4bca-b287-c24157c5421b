package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.OperationLog;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.QueryOperationLogRequest;
import com.telecom.apigateway.model.vo.response.OperationLogResponse;

import java.time.LocalDateTime;

public interface OperationLogService extends IService<OperationLog> {
    /**
     * 记录操作日志
     *
     * @param operationLog 操作日志
     */
    void recordLog(OperationLog operationLog);

    /**
     * 创建操作日志
     *
     * @param username 用户名
     * @param operationType 操作类型
     * @param description 操作描述
     * @param ip IP地址
     */
    void createLog(String username, OperationTypeEnum operationType, ResourceTypeEnum resourceType, String description, String ip);

    /**
     * 分页查询操作日志
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<OperationLogResponse> queryPage(QueryOperationLogRequest request);
}