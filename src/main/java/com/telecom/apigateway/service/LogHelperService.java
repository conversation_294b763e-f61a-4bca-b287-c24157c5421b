package com.telecom.apigateway.service;

import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.UpdateApplicationRequest;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class LogHelperService {
    private final ApplicationService applicationService;
    private final NginxAccessLogService nginxAccessLogService;

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.FULL_UPDATE,
            resourceType = ResourceTypeEnum.APPLICATION,
            spelArgs = {"#{#application.name + '(' + #application.applicationId + ')'}", "#{#application}"}
    )
    public Application updateApplication(UpdateApplicationRequest request, Application application) {
        String email = Optional.ofNullable(request.getEmail()).orElse(application.getEmail());
        String remark = Optional.ofNullable(request.getRemark()).orElse(application.getRemark());
        String owner = Optional.ofNullable(request.getOwner()).orElse(application.getOwner());
        String phone = Optional.ofNullable(request.getPhone()).orElse(application.getPhone());
        String name = Optional.ofNullable(request.getName()).orElse(application.getName());
        String[] updateArea = Optional.ofNullable(request.getArea()).map((area) -> area.toArray(new String[0])).orElse(application.getArea());
        Application update = application.update(name, remark, owner, phone, email, updateArea);
        applicationService.updateById(update);
        // add on 2024-12-09: 绑定 appId 到 nginx_access_log
        nginxAccessLogService.updateAppId(application);
        return update;
    }
}
