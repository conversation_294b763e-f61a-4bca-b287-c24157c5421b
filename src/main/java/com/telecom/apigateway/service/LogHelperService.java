package com.telecom.apigateway.service;

import cn.hutool.core.util.IdUtil;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.UpdateLogAnnotation;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.IpGroup;
import com.telecom.apigateway.model.entity.IpGroupRelation;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.UpdateApplicationRequest;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class LogHelperService {
    private final ApplicationService applicationService;
    private final NginxAccessLogService nginxAccessLogService;
    private final IpGroupService ipGroupService;
    private final IpGroupRelationService ipGroupRelationService;

    @UpdateLogAnnotation(
            resourceType = ResourceTypeEnum.APPLICATION,
            displayName = "#application.name",
            queryMethod = "@applicationServiceImpl.getById(#application.id)"
    )
    public Application updateApplication(UpdateApplicationRequest request, Application application) {
        String name = Optional.ofNullable(request.getName()).orElse(application.getName());
        String email = Optional.ofNullable(request.getEmail()).orElse(application.getEmail());
        String remark = Optional.ofNullable(request.getRemark()).orElse(application.getRemark());
        String owner = Optional.ofNullable(request.getOwner()).orElse(application.getOwner());
        String phone = Optional.ofNullable(request.getPhone()).orElse(application.getPhone());
        String[] updateArea = Optional.ofNullable(request.getArea()).map((area) -> area.toArray(new String[0])).orElse(application.getArea());
        Application update = application.update(name, remark, owner, phone, email, updateArea);
        applicationService.updateById(update);
        // add on 2024-12-09: 绑定 appId 到 nginx_access_log
        nginxAccessLogService.updateAppId(application);
        return update;
    }

    @UpdateLogAnnotation(
            resourceType = ResourceTypeEnum.IP_GROUP,
            displayName = "#ipGroup.name",
            queryMethod = "@ipGroupServiceImpl.getById(#ipGroup.id)"
    )
    public IpGroup updateIpGroup(IpGroup ipGroup, String name) {
        IpGroup update = ipGroup.update(name);
        ipGroupService.updateById(update);
        return update;
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.IP_GROUP,
            description = "IP组 #{#ipGroup.name}的内容从 #{#oldRelations.![ipAddress].toString()} 修改为 #{#result.![ipAddress].toString()}"
    )
    @Transactional
    public List<IpGroupRelation> updateIpGroupRelation(List<IpGroupRelation> oldRelations, List<String> ipGroups, IpGroup ipGroup) {
        ipGroupRelationService.updateBatchById(oldRelations);

        List<IpGroupRelation> newRelations = ipGroups.stream().map((ip) -> {
            String relationId = IdUtil.fastSimpleUUID();
            return new IpGroupRelation(relationId, ipGroup.getGroupId(), ip);
        }).collect(Collectors.toList());
        ipGroupRelationService.saveBatch(newRelations);
        return newRelations;
    }
}
