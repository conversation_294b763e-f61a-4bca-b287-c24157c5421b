package com.telecom.apigateway.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.telecom.apigateway.model.dto.HistoryMergeTaskDTO;
import com.telecom.apigateway.model.enums.MergeTaskStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Set;

/**
 * 历史数据合并任务Redis管理服务
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HistoryMergeTaskRedisService {

    private final StringRedisTemplate stringRedisTemplate;

    // Redis key前缀
    private static final String TASK_KEY_PREFIX = "history_merge_task:";
    private static final String RUNNING_TASK_KEY = "history_merge_task:running";
    private static final String LATEST_TASK_KEY = "history_merge_task:latest";
    
    // 任务过期时间：7天
    private static final Duration TASK_EXPIRE_TIME = Duration.ofDays(7);

    /**
     * 创建新任务
     */
    public String createTask(String createUser) {
        String taskId = IdUtil.simpleUUID();
        HistoryMergeTaskDTO task = HistoryMergeTaskDTO.createNew(taskId, createUser);
        
        // 保存任务信息
        saveTask(task);
        
        // 设置为最新任务
        setLatestTask(taskId);
        
        log.info("[REDIS_TASK] 创建历史合并任务: {}", taskId);
        return taskId;
    }

    /**
     * 保存任务信息
     */
    public void saveTask(HistoryMergeTaskDTO task) {
        String key = TASK_KEY_PREFIX + task.getId();
        String taskJson = JSONUtil.toJsonStr(task);
        stringRedisTemplate.opsForValue().set(key, taskJson, TASK_EXPIRE_TIME);
        
        // 如果任务正在运行，设置运行标记
        if (task.isRunning()) {
            stringRedisTemplate.opsForValue().set(RUNNING_TASK_KEY, task.getId(), TASK_EXPIRE_TIME);
        }
        
        // 如果任务完成，清除运行标记
        if (task.isCompleted()) {
            stringRedisTemplate.delete(RUNNING_TASK_KEY);
        }
    }

    /**
     * 获取任务信息
     */
    public HistoryMergeTaskDTO getTask(String taskId) {
        String key = TASK_KEY_PREFIX + taskId;
        String taskJson = stringRedisTemplate.opsForValue().get(key);
        
        if (taskJson == null) {
            return null;
        }
        
        return JSONUtil.toBean(taskJson, HistoryMergeTaskDTO.class);
    }

    /**
     * 更新任务状态
     */
    public void updateTaskStatus(String taskId, MergeTaskStatus status, String progressDescription) {
        HistoryMergeTaskDTO task = getTask(taskId);
        if (task != null) {
            task.updateStatus(status, progressDescription);
            saveTask(task);
            log.info("[REDIS_TASK] 更新任务状态: {} -> {}", taskId, status);
        }
    }

    /**
     * 更新API处理进度
     */
    public void updateApiProgress(String taskId, int processedCount, int totalCount) {
        HistoryMergeTaskDTO task = getTask(taskId);
        if (task != null) {
            task.updateApiProgress(processedCount, totalCount);
            saveTask(task);
            log.debug("[REDIS_TASK] 更新API进度: {} -> {}/{}", taskId, processedCount, totalCount);
        }
    }

    /**
     * 更新应用处理进度
     */
    public void updateApplicationProgress(String taskId, int processedCount, int totalCount) {
        HistoryMergeTaskDTO task = getTask(taskId);
        if (task != null) {
            task.updateApplicationProgress(processedCount, totalCount);
            saveTask(task);
            log.debug("[REDIS_TASK] 更新应用进度: {} -> {}/{}", taskId, processedCount, totalCount);
        }
    }

    /**
     * 设置任务错误
     */
    public void setTaskError(String taskId, String errorMessage) {
        HistoryMergeTaskDTO task = getTask(taskId);
        if (task != null) {
            task.setError(errorMessage);
            saveTask(task);
            log.error("[REDIS_TASK] 任务执行失败: {} -> {}", taskId, errorMessage);
        }
    }

    /**
     * 检查是否有正在运行的任务
     */
    public boolean hasRunningTask() {
        return stringRedisTemplate.hasKey(RUNNING_TASK_KEY);
    }

    /**
     * 获取正在运行的任务ID
     */
    public String getRunningTaskId() {
        return stringRedisTemplate.opsForValue().get(RUNNING_TASK_KEY);
    }

    /**
     * 设置最新任务
     */
    public void setLatestTask(String taskId) {
        stringRedisTemplate.opsForValue().set(LATEST_TASK_KEY, taskId, TASK_EXPIRE_TIME);
    }

    /**
     * 获取最新任务
     */
    public HistoryMergeTaskDTO getLatestTask() {
        String latestTaskId = stringRedisTemplate.opsForValue().get(LATEST_TASK_KEY);
        if (latestTaskId == null) {
            return null;
        }
        return getTask(latestTaskId);
    }

    /**
     * 删除任务
     */
    public void deleteTask(String taskId) {
        String key = TASK_KEY_PREFIX + taskId;
        stringRedisTemplate.delete(key);
        
        // 如果是正在运行的任务，清除运行标记
        String runningTaskId = getRunningTaskId();
        if (taskId.equals(runningTaskId)) {
            stringRedisTemplate.delete(RUNNING_TASK_KEY);
        }
        
        log.info("[REDIS_TASK] 删除任务: {}", taskId);
    }

    /**
     * 清理过期任务
     */
    public void cleanupExpiredTasks() {
        Set<String> keys = stringRedisTemplate.keys(TASK_KEY_PREFIX + "*");
        if (!keys.isEmpty()) {
            for (String key : keys) {
                // Redis会自动处理过期，这里只是记录日志
                if (!stringRedisTemplate.hasKey(key)) {
                    log.info("[REDIS_TASK] 清理过期任务: {}", key);
                }
            }
        }
    }

    /**
     * 获取所有任务数量
     */
    public long getTaskCount() {
        Set<String> keys = stringRedisTemplate.keys(TASK_KEY_PREFIX + "*");
        return keys.size();
    }
}
