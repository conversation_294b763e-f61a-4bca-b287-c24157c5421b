package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.DataScope;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.mapper.ApiInfoMapper;
import com.telecom.apigateway.model.dto.ApiImportFromFileDTO;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.vo.request.BatchUpdateApiRequest;
import com.telecom.apigateway.model.vo.request.QueryApiRequest;
import com.telecom.apigateway.model.vo.request.UpdateApiRequest;
import com.telecom.apigateway.model.vo.response.ApiHttpInfoResponse;
import com.telecom.apigateway.model.vo.response.ApiQueryResponse;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import com.telecom.apigateway.utils.ApiUrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-08-13
 */
@Slf4j
@Service
public class ApiInfoService extends ServiceImpl<ApiInfoMapper, ApiInfo> {

    @Resource
    private NginxAccessLogService nginxAccessLogService;
    @Resource
    private NginxLogEsClient nginxLogEsClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final String API_INFO_KEY_PREFIX = "api:key:";
    private static final String API_INFO_ID_PREFIX = "api:id:";

    @PostConstruct
    public void init() {
        resetRedisStore();
        log.info("初始化 api 缓存 ✅");
    }

    @Override
    public List<ApiInfo> list() {
        return lambdaQuery().eq(ApiInfo::getDeleted, false).list();
    }

    /**
     * 上线 api
     *
     * @param id api id
     */
    public void online(String id) {
        if (id == null) {
            throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "id 不能为空");
        }
        LocalDateTime now = LocalDateTime.now();
        ApiInfo apiInfo = new ApiInfo();
        apiInfo.setId(id);
        apiInfo.setIsOnline(true);
        apiInfo.setOnlineTime(now);
        apiInfo.setUpdateTime(now);
        this.updateById(apiInfo);
    }

    /**
     * 下线
     *
     * @param id api id
     */
    public void offline(String id) {
        if (id == null) {
            throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "id 不能为空");
        }
        ApiInfo apiInfo = new ApiInfo();
        apiInfo.setId(id);
        apiInfo.setIsOnline(false);
        apiInfo.setUpdateTime(LocalDateTime.now());
        this.updateById(apiInfo);
    }

    public void batchUpdateApi(BatchUpdateApiRequest param) {
        if (CollUtil.isEmpty(param.getIds())) {
            return;
        }
        if (param.getIsOnline() == null) {
            throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "isOnline 不能为空");
        }

        this.lambdaUpdate()
                .set(ApiInfo::getIsOnline, param.getIsOnline())
                .set(ApiInfo::getUpdateTime, LocalDateTime.now())
                .in(ApiInfo::getId, param.getIds())
                .update();
    }

    /**
     * code-much-ism production
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<ApiImportFromFileDTO> newApis, boolean forceSave) {
        if (CollUtil.isEmpty(newApis)) {
            return;
        }
        if (newApis.size() > 100) {
            throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "批量导入数据不能超过100条");
        }

        // 数据矫正
        for (ApiImportFromFileDTO one : newApis) {
            one.format();
            if (StrUtil.isBlank(one.getUri())) {
                throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "uri 不能为空");
            }
            if ("/".equals(one.getUri())) {
                throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "uri 不能为 /");
            }
            if (one.getHttpMethod() == null) {
                throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "httpMethod 不能为空");
            }
            if (one.getAppId() == null) {
                throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "appId 不能为空");
            }
        }

        for (int i = 0; i < newApis.size() - 1; i++) {
            for (int j = i + 1; j < newApis.size(); j++) {
                if (newApis.get(i).getHttpMethod().equals(newApis.get(j).getHttpMethod()) &&
                        newApis.get(i).getHost().equals(newApis.get(j).getHost()) &&
                        newApis.get(i).getPort().equals(newApis.get(j).getPort()) &&
                        ApiUrlUtils.isMatch(newApis.get(i).getUri(), newApis.get(j).getUri())) {
                    throw new BusinessException(ResultCodeEnum.DUPLICATE_API);
                }
            }
        }


        List<ApiInfo> updateApis = new ArrayList<>();
        List<ApiInfo> insertApis = new ArrayList<>();
        List<String> deleteApis = new ArrayList<>();
        Map<String, String> updateApiKeys = new HashMap<>();

        LocalDateTime now = LocalDateTime.now();
        List<ApiInfo> allApis = list();
        for (ApiImportFromFileDTO newApi : newApis) {
            List<ApiInfo> duplicateApis = new ArrayList<>();
            for (ApiInfo allApi : allApis) {
                if (allApi.getAppId().equals(newApi.getAppId()) &&
                        allApi.getHttpMethod().equals(newApi.getHttpMethod()) &&
                        allApi.getHost().equals(newApi.getHost()) &&
                        allApi.getPort().equals(newApi.getPort()) &&
                        ApiUrlUtils.isMatch(allApi.getUri(), newApi.getUri())) {
                    if (forceSave) {
                        if (newApi.isPathVariable()) {
                            duplicateApis.add(allApi);
                        } else {
                            // 更新已有 API
                            ApiInfo updateApi = new ApiInfo();
                            updateApi.setId(allApi.getId());
                            updateApi.setAppId(newApi.getAppId());
                            updateApi.setName(newApi.getName());
                            updateApi.setOnlineTime(newApi.getOnlineTime());
                            updateApi.setUpdateTime(now);
                            updateApi.setUpdateUser(StpUtil.getLoginIdAsString());
                            updateApis.add(updateApi);
                        }
                    } else {
                        throw new BusinessException(ResultCodeEnum.DUPLICATE_API);
                    }
                }
            }

            if (duplicateApis.size() == 1) {
                // 更新已有 API
                ApiInfo updateApi = new ApiInfo();
                updateApi.setId(duplicateApis.get(0).getId());
                updateApi.setAppId(newApi.getAppId());
                updateApi.setUri(newApi.getUri());
                updateApi.setName(newApi.getName());
                updateApi.setOnlineTime(newApi.getOnlineTime());
                updateApi.setUpdateTime(now);
                updateApi.setUpdateUser(StpUtil.getLoginIdAsString());
                updateApis.add(updateApi);
            } else if (duplicateApis.size() > 1) {
                // 插入新的 API
                ApiInfo apiInfo = getApiInfo(newApi);
                apiInfo.setId(IdUtil.fastSimpleUUID());
                apiInfo.setIsActive(true);
                insertApis.add(apiInfo);
                List<String> apiIds = duplicateApis.stream().map(ApiInfo::getId).collect(Collectors.toList());
                deleteApis.addAll(apiIds);
                log.info("[API][合并] 删除了 {} 个api:{}, 新增了 1 个api:{}", apiIds.size(), apiIds, apiInfo.getId());
                apiIds.forEach(
                        apiId -> updateApiKeys.put(apiId, apiInfo.getId())
                );
            }
        }

        // 批量更新与插入
        if (!updateApis.isEmpty()) {
            this.updateBatchById(updateApis);
        }
        if (!insertApis.isEmpty()) {
            this.saveBatch(insertApis);
        }
        if (!deleteApis.isEmpty()) {
            this.lambdaUpdate()
                    .in(ApiInfo::getId, deleteApis)
                    .set(ApiInfo::getUpdateTime, now)
                    .set(ApiInfo::getUpdateUser, StpUtil.getLoginIdAsString())
                    .set(ApiInfo::getDeleted, true)
                    .update();
        }
        if (!deleteApis.isEmpty() || !insertApis.isEmpty()) {
            resetRedisStore();
        }
        if (!updateApiKeys.isEmpty()) {
            EsQueryDTO q = EsQueryDTO.builder().build();
            updateApiKeys.forEach((apiId, newApiId) -> {
                q.removeQuery("apiId");
                q.addQuery("apiId", apiId);
                HashMap<String, Object> updateMap = new HashMap<>();
                updateMap.put("apiId", newApiId);
                long count = nginxLogEsClient.updateByQueryId(q, updateMap);
                log.info("[API][合并] old_apiId:{} --> new_apiId:{}, count:{}", apiId, newApiId, count);
            });
        }
    }

    private ApiInfo getApiInfo(ApiImportFromFileDTO newApi) {
        // todo 数据校验
        LocalDateTime now = LocalDateTime.now();

        ApiInfo apiInfo = new ApiInfo();
        apiInfo.setHttpMethod(newApi.getHttpMethod());
        apiInfo.setName(newApi.getName());
        apiInfo.setHost(newApi.getHost());
        apiInfo.setPort(newApi.getPort());
        apiInfo.setUri(newApi.getUri());
        apiInfo.setPathLevel(newApi.getUri().split("/").length - 1);
        apiInfo.setSource(Constant.Api.SOURCE_MANUAL);
        apiInfo.setDeleted(false);
        // 新增的 api 默认不活跃
        apiInfo.setIsActive(false);
        apiInfo.setIsOnline(true);
        apiInfo.setSensitiveLevel(Constant.Api.SENSITIVE_LEVEL_SAFE);
        apiInfo.setCreateUser(StpUtil.getLoginIdAsString());
        apiInfo.setCreateTime(now);
        apiInfo.setUpdateUser(StpUtil.getLoginIdAsString());
        apiInfo.setUpdateTime(now);
        apiInfo.setOnlineTime(newApi.getOnlineTime());
        apiInfo.setAppId(newApi.getAppId());
        apiInfo.setRemark(newApi.getRemark());
        return apiInfo;
    }


    public ApiHttpInfoResponse getHttpDetail(String apiId) {
        ApiInfo apiInfo = this.getById(apiId);
        if (apiInfo == null) {
            throw new BusinessException(ResultCodeEnum.API_NOT_EXISTED);
        }
        // 查询一条
        EsNginxDTO log = nginxAccessLogService.queryOneAccessLog(apiId, Constant.RANGE_MONTH);
        if (log == null) {
            log = new EsNginxDTO();
        }
        return ApiHttpInfoResponse.builder()
                .apiId(apiId)
                .requestHeaders(log.getRequestHeader())
                .requestParam(log.getRequestParam())
                .requestBody(log.getRequestBody())
                .responseBody(log.getResponseData())
                .responseCode(log.getStatusCode())
                .build();
    }

    public List<LinkedHashMap<String, String>> export(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException(ResultCodeEnum.API_PARAM_ERROR, "ids不能为空");
        }
        return baseMapper.queryExportDataByIds(ids);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateByApiId(UpdateApiRequest req) {
        ApiInfo apiInfo = new ApiInfo();
        apiInfo.setId(req.getId());
        apiInfo.setName(req.getName());
        apiInfo.setRemark(req.getRemark());
        apiInfo.setOnlineTime(req.getOnlineTime());
        apiInfo.setUpdateUser(StpUtil.getLoginIdAsString());
        apiInfo.setUpdateTime(LocalDateTime.now());
        this.updateById(apiInfo);
    }

    public String getFileNameByType(String fileType) {
        switch (fileType.toLowerCase()) {
            case "xlsx":
                return "sample_import_excel.xlsx";
            case "csv":
                return "sample_import_csv.csv";
            case "yaml":
                return "sample_import_yaml.yml";
            case "json":
                return "sample_import_json.json";
            default:
                throw new BusinessException(ResultCodeEnum.NOT_SUPPORT_API_FILE_TYPE);
        }
    }

    /**
     * 根据文件扩展名返回媒体类型
     */
    public MediaType getMediaType(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "xlsx":
                return MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            case "csv":
                return MediaType.parseMediaType("text/csv");
            case "yaml":
                return MediaType.parseMediaType("application/x-yaml");
            case "json":
                return MediaType.APPLICATION_JSON;
            default:
                return MediaType.APPLICATION_OCTET_STREAM;
        }
    }


    public List<String> queryUri(String uri, Integer pathLevel) {
        return this.lambdaQuery()
                .select(ApiInfo::getUri)
                .eq(ApiInfo::getDeleted, false)
                .like(StrUtil.isNotBlank(uri), ApiInfo::getUri, uri)
                .eq(pathLevel != null && pathLevel > 0, ApiInfo::getPathLevel, pathLevel)
                .orderByAsc(ApiInfo::getUri)
                .list().stream().map(ApiInfo::getUri).distinct().collect(Collectors.toList());
    }

    public Optional<ApiInfo> getOptById(String id) {
        return this.lambdaQuery()
                .eq(ApiInfo::getDeleted, false)
                .eq(ApiInfo::getId, id)
                .oneOpt();
    }

    public List<ApiInfo> getSensitiveByApplicationIds(Collection<String> applicationIds) {
        if (CollUtil.isEmpty(applicationIds)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .eq(ApiInfo::getDeleted, false)
                .gt(ApiInfo::getSensitiveLevel, 0)
                .in(ApiInfo::getAppId, applicationIds)
                .list();
    }

    @DataScope(columnAlias = "app_id")
    public long getApiCount() {
        return this.lambdaQuery()
                .eq(ApiInfo::getDeleted, false)
                .count();
    }

    public List<ApiInfo> getByApplicationId(String applicationId) {
        return getByApplicationId(applicationId, false);
    }

    public List<ApiInfo> getByApplicationId(String applicationId, boolean containDeleted) {
        return this.lambdaQuery()
                .eq(!containDeleted, ApiInfo::getDeleted, false)
                .eq(ApiInfo::getAppId, applicationId)
                .list();
    }

    public List<ApiInfo> getByApplicationIds(List<String> applicationIds, boolean containDeleted) {
        return this.lambdaQuery()
                .eq(!containDeleted, ApiInfo::getDeleted, false)
                .in(CollUtil.isNotEmpty(applicationIds), ApiInfo::getAppId, applicationIds)
                .list();
    }

    @DataScope(columnAlias = "app_id")
    public List<ApiInfo> getByApplicationIdWithDataScope(String applicationId) {
        return getByApplicationId(applicationId, false);
    }

    @DataScope(columnAlias = "app_id")
    public List<ApiInfo> listApiWithDataScope() {
        return this.lambdaQuery()
                .eq(ApiInfo::getDeleted, false)
                .list();
    }

    @DataScope(columnAlias = "app_id")
    public List<ApiInfo> listApiWithDataScope(Collection<String> applicationIds) {
        if (applicationIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .eq(ApiInfo::getDeleted, false)
                .in(ApiInfo::getAppId, applicationIds)
                .list();
    }

    public long getSensitiveApiCount() {
        QueryApiRequest request = new QueryApiRequest();
        request.setSensitiveLevels(new Integer[]{1, 2, 3});
        IPage<ApiQueryResponse> query = baseMapper.query(new Page<>(1L, 1L), request);
        return query.getTotal();
    }

    public List<ApiInfo> getByAppIdWithoutDataScope(List<String> appIds) {
        if (CollUtil.isEmpty(appIds)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .eq(ApiInfo::getDeleted, false)
                .in(ApiInfo::getAppId, appIds)
                .list();
    }

    public void deleteById(String apiId) {
        getOptById(apiId).ifPresent(apiInfo -> {
            lambdaUpdate()
                    .set(ApiInfo::getDeleted, true)
                    .set(ApiInfo::getUpdateTime, LocalDateTime.now())
                    .set(ApiInfo::getUpdateUser, StpUtil.getLoginIdAsString())
                    .eq(ApiInfo::getId, apiId)
                    .update();
            deleteRedisOne(apiInfo.toReconizedString());
        });
    }

    public void deleteByIds(Collection<String> apiIds) {
        if (CollUtil.isEmpty(apiIds)) return;

        lambdaUpdate()
                .set(ApiInfo::getDeleted, true)
                .set(ApiInfo::getUpdateTime, LocalDateTime.now())
                .set(ApiInfo::getUpdateUser, StpUtil.getLoginIdAsString())
                .in(ApiInfo::getId, apiIds)
                .update();
        resetRedisStore();
    }

    @Override
    public boolean updateById(ApiInfo apiInfo) {
        super.updateById(apiInfo);
        updateRedis(apiInfo);
        return true;
    }

    public List<ApiInfo> getByIds(List<String> apiIds) {
        if (CollUtil.isEmpty(apiIds)) {
            return new ArrayList<>();
        }

        return lambdaQuery()
                .eq(ApiInfo::getDeleted, false)
                .in(ApiInfo::getId, apiIds)
                .list();
    }

    /**
     * 重置redis缓存
     */
    public void resetRedisStore() {
        Set<String> keys = stringRedisTemplate.keys(API_INFO_KEY_PREFIX + "*");
        if (CollUtil.isNotEmpty(keys)) {
            stringRedisTemplate.delete(keys);
        }
        List<ApiInfo> list = list();
        Map<String, String> keyMap = list.stream().collect(Collectors.toMap(apiInfo ->
                        API_INFO_KEY_PREFIX + apiInfo.toReconizedString(),
                apiInfo -> JSONUtil.parseObj(apiInfo).toString()
        ));
        stringRedisTemplate.opsForValue().multiSet(keyMap);
    }

    /**
     * 删除redis缓存 单个
     *
     * @param key {@value   API_INFO_KEY_PREFIX)
     */
    public void deleteRedisOne(String key) {
        if (!stringRedisTemplate.delete(API_INFO_KEY_PREFIX + key)) {
            log.warn("[api][delete] redis key error: {}", key);
        }
    }

    /**
     * 更新redis缓存
     */
    public void updateRedis(ApiInfo apiInfo) {
        stringRedisTemplate.opsForValue().setIfPresent(
                API_INFO_KEY_PREFIX + apiInfo.toReconizedString(),
                JSONUtil.parseObj(apiInfo).toString());
    }
}

