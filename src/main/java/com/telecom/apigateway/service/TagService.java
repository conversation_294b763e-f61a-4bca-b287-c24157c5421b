package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.Tag;
import com.telecom.apigateway.model.vo.request.QueryTagRequest;

import java.util.List;
import java.util.Optional;

public interface TagService extends IService<Tag> {
    String addTag(Tag tag);

    Page<Tag> getTagsLikeName(QueryTagRequest query);

    String deleteByTagId(String tagId);

    Optional<Tag> getOpt(String trim);

    void batchDeleteByTagIds(List<String> tagIds);
}
