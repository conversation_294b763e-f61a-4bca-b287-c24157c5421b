package com.telecom.apigateway.service;

import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.Rule;
import com.telecom.apigateway.model.entity.RuleConfig;
import com.telecom.apigateway.model.enums.CategoryEnum;
import com.telecom.apigateway.model.enums.ConfigTypeEnum;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.GuardConfigRequest;
import com.telecom.apigateway.model.vo.request.OwaspRuleConfigRequest;
import com.telecom.apigateway.model.vo.request.RuleConfigAction;
import com.telecom.apigateway.model.vo.response.GuardResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.telecom.apigateway.common.ResultCodeEnum.RULE_NOT_EXISTED;

@Service
@AllArgsConstructor
public class GuardConfigService {
    private final RuleConfigService ruleConfigService;
    private final RuleService ruleService;
    private final DirectivesService directivesService;

    public List<GuardResponse> list() {
        // 默认高度防护
        List<Rule> rules = ruleService.crsRuleList();
        Map<String, RuleConfig> configMap = ruleConfigService.crsRuleConfigList()
                .stream()
                .collect(Collectors.toMap(RuleConfig::getRuleId, Function.identity()));
        List<GuardResponse> guard = rules.stream().map((rule -> {
            String ruleId = rule.getRuleId();
            RuleConfig ruleConfig = configMap.get(ruleId);
            GuardResponse guardResponse = new GuardResponse();
            guardResponse.setRuleId(ruleId);
            guardResponse.setRuleName(rule.getModule());
            guardResponse.setType(ruleConfig.getType());
            return guardResponse;
        })).collect(Collectors.toList());
        // 相同名称只显示一条
        return guard.stream()
                .filter(distinctByKey(GuardResponse::getRuleName))
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.PROTECTION_CONFIG,
            description = "配置通用模块防护规则 #{#result.module} 为 #{T(com.telecom.apigateway.model.enums.ConfigTypeEnum).getEnum(#request.type).getText()}"
    )
    public Rule config(GuardConfigRequest request) {
        String type = checkType(request.getType());
        Optional<Rule> ruleOpt = ruleService.lambdaQuery()
                .eq(Rule::getRuleId, request.getRuleId())
                .eq(Rule::getCategory, CategoryEnum.SYSTEM.getCode())
                .oneOpt();
        if (!ruleOpt.isPresent()) {
            throw new BusinessException(RULE_NOT_EXISTED);
        }
        Rule rule = ruleOpt.get();

        // 查询出所有同名的规则
        List<Rule> sameNameRules = ruleService.lambdaQuery()
                .eq(Rule::getModule, rule.getModule())
                .eq(Rule::getCategory, CategoryEnum.SYSTEM.getCode())
                .list();

        List<String> ruleIds = sameNameRules.stream().map(Rule::getRuleId).collect(Collectors.toList());
        List<RuleConfig> configs = ruleConfigService.lambdaQuery()
                .in(RuleConfig::getRuleId, ruleIds)
                .eq(RuleConfig::getCategory, CategoryEnum.SYSTEM.getCode())
                .ne(RuleConfig::getType, type)
                .list();

        for (RuleConfig config : configs) {
            ConfigTypeEnum from = ConfigTypeEnum.getEnum(config.getType());
            ConfigTypeEnum to = ConfigTypeEnum.getEnum(type);
            RuleConfigAction ruleConfigAction = new RuleConfigAction(from, to);
            directivesService.configOwaspRule(new OwaspRuleConfigRequest(config.getFilename(), ruleConfigAction));

            config.setType(type);
            ruleConfigService.updateById(config);
        }
        return rule;
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.PROTECTION_CONFIG,
            description = "配置全部模块防护规则为 #{T(com.telecom.apigateway.model.enums.ConfigTypeEnum).getEnum(#type).getText()}"
    )
    public void configAll(String type) {
        checkType(type);
        List<RuleConfig> configs = ruleConfigService.lambdaQuery()
                .eq(RuleConfig::getCategory, CategoryEnum.SYSTEM.getCode())
                .ne(RuleConfig::getType, type)
                .list();

        for (RuleConfig config : configs) {
            ConfigTypeEnum from = ConfigTypeEnum.getEnum(config.getType());
            ConfigTypeEnum to = ConfigTypeEnum.getEnum(type);
            RuleConfigAction ruleConfigAction = new RuleConfigAction(from, to);
            directivesService.configOwaspRule(new OwaspRuleConfigRequest(config.getFilename(), ruleConfigAction));

            config.setType(type);
            ruleConfigService.updateById(config);
        }
    }

    private String checkType(String type) {
        boolean validType = ConfigTypeEnum.checkValid(type);
        if (!validType) {
            throw new IllegalArgumentException();
        }
        return type;
    }

    // 辅助方法
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }
}
