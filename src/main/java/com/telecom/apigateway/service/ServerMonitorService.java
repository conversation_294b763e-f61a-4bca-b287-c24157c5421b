package com.telecom.apigateway.service;

import com.telecom.apigateway.model.entity.ServerNode;
import com.telecom.apigateway.model.vo.ServerMonitorVO;
import com.telecom.apigateway.model.vo.request.UpdateServerNodeRequest;

import java.util.List;

/**
 * 服务器监控服务
 */
public interface ServerMonitorService {
    
    /**
     * 添加服务器节点
     *
     * @param serverNode 服务器节点信息
     * @return 是否添加成功
     */
    boolean addServerNode(ServerNode serverNode);
    
    /**
     * 更新服务器节点
     *
     * @param serverNode 服务器节点信息
     * @return 是否更新成功
     */
    boolean updateServerNode(UpdateServerNodeRequest serverNode);
    
    /**
     * 删除服务器节点
     *
     * @param id 节点ID
     * @return 是否删除成功
     */
    boolean deleteServerNodeById(Long id);
    
    /**
     * 获取所有服务器节点
     *
     * @return 节点列表
     */
    List<ServerNode> getAllServerNodes();
    
    /**
     * 获取所有服务器的监控信息
     *
     * @return 监控信息列表
     */
    List<ServerMonitorVO> getAllServerMonitorInfo();
    
    /**
     * 根据主机ID获取服务器监控信息
     *
     * @param hostId 主机ID
     * @return 监控信息
     */
    ServerMonitorVO getServerMonitorInfoByHostId(String hostId);
} 