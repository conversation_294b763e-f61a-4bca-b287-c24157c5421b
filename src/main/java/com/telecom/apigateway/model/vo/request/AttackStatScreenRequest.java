package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.common.Constant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class AttackStatScreenRequest implements Serializable {
    private String appId = "ALL";

    @DateTimeFormat(pattern =
            Constant.DATE_TIME_PATTERN)
    private LocalDateTime startTime;

    @DateTimeFormat(pattern =
            Constant.DATE_TIME_PATTERN)
    private LocalDateTime endTime;
}
