package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
public class UnknownInterfaceResponse {
    private String applicationId;
    private String applicationName;
    private String url;
    private Integer sensitiveLevel;
    private Boolean isActive;
    private String api;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime discoverTime;
}
