package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "角色请求")
@EqualsAndHashCode(callSuper = true)
public class UpdateRoleRequest extends RoleRequest implements Serializable {
    @NotBlank(message = "role id 不能为空")
    @Schema(description = "角色id")
    private String roleId;
} 