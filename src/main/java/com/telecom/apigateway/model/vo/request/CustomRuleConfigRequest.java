package com.telecom.apigateway.model.vo.request;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
public class CustomRuleConfigRequest implements Serializable {
    
    @NotBlank(message = "规则内容不能为空")
    private String directives;
    
    @NotNull(message = "变更类型不能为空")
    private RuleConfigAction action;
} 