package com.telecom.apigateway.model.vo.request;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.enums.LogEnum;
import com.telecom.apigateway.utils.DateTimeUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortOrder;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-11
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class QueryLogRequest extends PageRequest implements Serializable {
    @Schema(description = "日志 id")
    private String logId;
    @Schema(description = "关键词搜索")
    private String keyword;
    @Schema(description = "api id")
    private String apiId;
    @Schema(description = "查询数量")
    private Integer number;
    @Schema(description = "uri 模糊")
    private String uri;
    @Schema(description = "url 模糊")
    private String url;
    @Schema(description = "应用id")
    private List<String> appId;
    @Schema(description = "应用id")
    private List<String> appIds;
    @Schema(description = "http方法")
    private String httpMethod;
    @Schema(description = "状态码")
    private List<String> statusCodes;
    @Schema(description = "源ip")
    private String clientIp;
    @Schema(description = "目标ip")
    private String serverIp;
    @Schema(description = "负载均衡ip")
    private String upstreamAddr;
    @Schema(description = "请求资源类型  api / file")
    private String requestResourceType;
    @Schema(description = "时间范围")
    private Integer range;
    @Schema(description = "起始时间")
    private String startTime;
    @Schema(description = "终止时间")
    private String endTime;

    /**
     * todo 改为枚举
     */
    @Schema(description = "查询日志类型, 查询类型  ALL(空串)-全部 / SENSITIVE-涉敏 / RISK(THREAT)-威胁")
    private String logType;
    @Schema(description = "涉敏规则id, 多个")
    private List<String> sensitiveRuleIds;
    @Schema(description = "威胁规则id, 多个")
    private List<String> riskTypes;

    private List<LogEnum.RequestState> requestState;

    private String abnormalBehaviorRuleTriggerId;
    private List<String> logIds;

    public EsQueryDTO toEsQueryDTO() {
        Pair<LocalDateTime, LocalDateTime> timePair = DateTimeUtils.rangeTime(range, startTime, endTime);
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .pageNum(this.getPageNum())
                .pageSize(this.getPageSize())
                .start(timePair.getLeft())
                .end(timePair.getRight())
                .build();


        queryDTO.addQuery(StrUtil.isNotBlank(logId), "uuid", logId)
                .addMultipleQuery(CollUtil.isNotEmpty(logIds), "uuid", logIds)
                .addMultipleQuery(CollUtil.isNotEmpty(appId), "appId", appId)
                .addMultipleQuery(CollUtil.isNotEmpty(appIds), "appId", appIds)
                .addQuery(StrUtil.isNotBlank(apiId), "apiId", apiId)
                .addQuery(StrUtil.isNotBlank(httpMethod), "httpMethod", httpMethod)
                .addQuery(StrUtil.isNotBlank(requestResourceType), "requestResourceType", requestResourceType)
                .addFuzzyQuery(StrUtil.isNotBlank(clientIp), "clientIp", clientIp)
                .addFuzzyQuery(StrUtil.isNotBlank(uri), "uri", uri)
                .addFuzzyQuery(StrUtil.isNotBlank(url), "url", url)
                .addFuzzyQuery(StrUtil.isNotBlank(serverIp), "domain", serverIp)
                .addFuzzyQuery(StrUtil.isNotBlank(upstreamAddr), "upstreamAddr", upstreamAddr)
                .addMultipleQuery(ArrayUtil.isNotEmpty(statusCodes), "statusCode", statusCodes)
                .addNestedQuery(ArrayUtil.isNotEmpty(sensitiveRuleIds), "sensitiveRules.ruleId", sensitiveRuleIds)
                .addNestedQuery(ArrayUtil.isNotEmpty(riskTypes), "riskRules.type", riskTypes);

        if (StrUtil.isNotBlank(logType)) {
            switch (logType) {
                case "SENSITIVE":
                    queryDTO.addExistQuery("sensitiveRules");
                    break;
                case "RISK":
                case "THREAT":
                    queryDTO.addExistQuery("riskRules");
                    break;
            }
        }
        buildStateQuery(queryDTO, requestState);

        queryDTO.orderBy("logTime", SortOrder.DESC);
        return queryDTO;
    }

    static void buildStateQuery(EsQueryDTO queryDTO, List<LogEnum.RequestState> requestState) {
        if (CollUtil.isNotEmpty(requestState)) {
            if (requestState.size() == 1) {
                switch (requestState.get(0)) {
                    case NORMAL:
                        queryDTO.addQuery("crsDetectStatus", LogEnum.CrsDetectStatus.PASS.name());
                        queryDTO.addQuery("wafDetectStatus", LogEnum.WafDetectStatus.PASS.name());
                        queryDTO.addQuery("abnormalBehaviorDetectStatus",
                                LogEnum.AbnormalBehaviorDetectStatus.PASS.name());
                        break;
                    case BLOCKLIST_ALLOW:
                        queryDTO.addQuery("wafDetectStatus", LogEnum.WafDetectStatus.ALLOW.name());
                        break;
                    case BLOCKLIST_REJECT:
                        queryDTO.addQuery("wafDetectStatus", LogEnum.WafDetectStatus.REJECT.name());
                        break;
                    case RISK_REJECT:
                        queryDTO.addQuery("crsDetectStatus", LogEnum.CrsDetectStatus.REJECT.name());
                        break;
                    case RISK_LOG:
                        queryDTO.addQuery("crsDetectStatus", LogEnum.CrsDetectStatus.LOG.name());
                        queryDTO.addExistQuery("riskRules");
                        break;
                    case ABRT_REJECT:
                        queryDTO.addQuery("abnormalBehaviorDetectStatus",
                                LogEnum.AbnormalBehaviorDetectStatus.REJECT.name());
                        break;
                    case ABRT_LOG:
                        queryDTO.addQuery("abnormalBehaviorDetectStatus",
                                LogEnum.AbnormalBehaviorDetectStatus.LOG.name());
                        break;
                }
            } else {
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                for (LogEnum.RequestState state : requestState) {
                    switch (state) {
                        case NORMAL:
                            boolQueryBuilder.should(
                                    QueryBuilders.boolQuery()
                                            .filter(QueryBuilders.termQuery("crsDetectStatus",
                                                    LogEnum.CrsDetectStatus.PASS.name()))
                                            .filter(QueryBuilders.termQuery("wafDetectStatus",
                                                    LogEnum.WafDetectStatus.PASS.name()))
                                            .filter(QueryBuilders.termQuery("abnormalBehaviorDetectStatus",
                                                    LogEnum.AbnormalBehaviorDetectStatus.PASS.name()))
                            );
                            break;
                        case BLOCKLIST_ALLOW:
                            boolQueryBuilder.should(QueryBuilders.termQuery("wafDetectStatus",
                                    LogEnum.WafDetectStatus.ALLOW.name()));
                            break;
                        case BLOCKLIST_REJECT:
                            boolQueryBuilder.should(QueryBuilders.termQuery("wafDetectStatus",
                                    LogEnum.WafDetectStatus.REJECT.name()));
                            break;
                        case RISK_REJECT:
                            boolQueryBuilder.should(QueryBuilders.termQuery("crsDetectStatus",
                                    LogEnum.CrsDetectStatus.REJECT.name()));
                            break;
                        case RISK_LOG:
                            boolQueryBuilder.should(QueryBuilders.termQuery("crsDetectStatus",
                                    LogEnum.CrsDetectStatus.LOG.name()));
                            break;
                        case ABRT_REJECT:
                            boolQueryBuilder.should(QueryBuilders.termQuery("abnormalBehaviorDetectStatus",
                                    LogEnum.AbnormalBehaviorDetectStatus.REJECT.name()));
                            break;
                        case ABRT_LOG:
                            boolQueryBuilder.should(QueryBuilders.termQuery("abnormalBehaviorDetectStatus",
                                    LogEnum.AbnormalBehaviorDetectStatus.LOG.name()));
                            break;
                    }
                }
                queryDTO.addCustomQuery(boolQueryBuilder);
            }
        }
    }

    public void format() {
        keyword = StrUtil.trim(keyword);
        apiId = StrUtil.trim(apiId);
        httpMethod = StrUtil.trim(httpMethod);
        requestResourceType = StrUtil.trim(requestResourceType);
        clientIp = StrUtil.trim(clientIp);
        uri = StrUtil.trim(uri);
        url = StrUtil.trim(url);
        serverIp = StrUtil.trim(serverIp);
        upstreamAddr = StrUtil.trim(upstreamAddr);
    }
}
