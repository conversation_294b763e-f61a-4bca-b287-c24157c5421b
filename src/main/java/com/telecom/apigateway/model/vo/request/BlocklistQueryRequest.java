package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.common.Constant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class BlocklistQueryRequest extends BasePageQueryRequest implements Serializable {
    private String name;
    private String type;
    private String status;

    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime startTime;

    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime endTime;
}
