package com.telecom.apigateway.model.vo.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiCountResponse {
    private Integer total = 0;
    private Integer activationCount = 0;

    public void add(ApiCountResponse other) {
        if (other == null) return;
        this.total += other.getTotal();
        this.activationCount += other.getActivationCount();
    }
}
