package com.telecom.apigateway.model.vo.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.telecom.apigateway.config.mybatisplus.JsonbListTypeHandler;
import com.telecom.apigateway.config.mybatisplus.StringListTypeHandler;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.entity.ApiMergeCondition;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

public class UpdateApiMergeRequest implements Serializable {

    private String id;
    private String name;
    /**
     * 策略
     */
    private ApiMergeEnum.Policy policy;

    @Schema(description = "匹配条件")
    @TableField(typeHandler = JsonbListTypeHandler.class)
    private List<ApiMergeCondition> condition;

    private String urlReg;
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> httpMethod;

    @TableField(value = "is_enable")
    private Boolean enabled;

    private String createUser;
    private LocalDateTime createTime;
    private String updateUser;
    private LocalDateTime updateTime;
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ApiMergeEnum.Policy getPolicy() {
        return policy;
    }

    public void setPolicy(ApiMergeEnum.Policy policy) {
        this.policy = policy;
    }

    public List<ApiMergeCondition> getCondition() {
        return condition;
    }

    public void setCondition(List<ApiMergeCondition> condition) {
        this.condition = condition;
    }

    public String getUrlReg() {
        return urlReg;
    }

    public void setUrlReg(String urlReg) {
        this.urlReg = urlReg;
    }

    public List<String> getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(List<String> httpMethod) {
        this.httpMethod = httpMethod;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public ApiMerge toEntity() {
        return ApiMerge.ofUpdate(id,
                name,
                policy,
                condition,
                urlReg,
                httpMethod,
                remark
        );
    }
}
