package com.telecom.apigateway.model.vo.response;

import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.enums.ProtocolEnum;
import com.telecom.apigateway.model.enums.RiskLevelEnum;
import com.telecom.apigateway.utils.MapUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class ApplicationResponse {
    private String applicationId;
    private String name;
    private ApiCountResponse apiCount;
    private Integer riskLevel;
    private String host;
    private String remark;
    private ProtocolEnum protocol;
    private String owner;
    private String phone;
    private String email;
    private String port;
    private List<ApplicationResponse> children = new ArrayList<>();
    private String parentId;
    private ApplicationTypeEnum type;
    private String uri;
    private String url;

    private BaseLevelResponse threatCount;
    private BaseLevelResponse sensitiveCount;

    public void setThreatCountAndSensitiveCount(BaseLevelResponse threatCount,
                                                BaseLevelResponse sensitiveCount) {
        this.threatCount = threatCount;
        this.sensitiveCount = sensitiveCount;
    }

    public static ApplicationResponse convertApplicationResponse(Application application, ApiCountResponse apiCount) {
        ApplicationResponse applicationResponse = new ApplicationResponse();
        applicationResponse.setApplicationId(application.getApplicationId());
        applicationResponse.setName(application.getName());
        applicationResponse.setApiCount(apiCount);
        applicationResponse.setRiskLevel(RiskLevelEnum.SAFE.getCode());
        applicationResponse.setHost(application.getHost());
        applicationResponse.setRemark(application.getRemark());
        applicationResponse.setPort(application.getPort());
        applicationResponse.setProtocol(application.getProtocol());
        applicationResponse.setParentId(application.getParentId());
        applicationResponse.setType(application.getType());
        applicationResponse.setOwner(application.getOwner());
        applicationResponse.setPhone(application.getPhone());
        applicationResponse.setEmail(application.getEmail());
        applicationResponse.setUri(application.getUri());
        applicationResponse.setUrl(String.format("%s:%s%s", application.getHost(), application.getPort(), application.getUri()));
        return applicationResponse;
    }

    /**
     * 将 ApplicationResponse 列表转换为树结构
     *
     * @param applicationResponses ApplicationResponse 列表
     * @return 树的根节点列表
     */
    public static List<ApplicationResponse> buildTree(List<ApplicationResponse> applicationResponses) {
        Map<String, ApplicationResponse> nodeMap = MapUtil.toMapByUnionParams(ApplicationResponse::getApplicationId, applicationResponses);

        // 第二次遍历，构建树结构
        for (ApplicationResponse response : applicationResponses) {
            String parentId = response.getParentId();
            if (parentId != null && !parentId.isEmpty() && nodeMap.containsKey(parentId)) {
                // 找到父节点，并将当前节点添加到父节点的 children 列表中
                ApplicationResponse parent = nodeMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(response);
                }
            }
        }

        // 返回所有没有父节点的节点，即树的根节点
        return applicationResponses.stream()
                .filter(response -> response.getParentId() == null || response.getParentId().isEmpty() || !nodeMap.containsKey(response.getParentId()))
                .collect(Collectors.toList());
    }
}
