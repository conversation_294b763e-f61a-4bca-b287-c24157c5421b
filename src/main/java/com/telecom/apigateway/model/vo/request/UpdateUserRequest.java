package com.telecom.apigateway.model.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "更新用户")
public class UpdateUserRequest implements Serializable {
    @NotBlank(message = "用户名不能为空")
    private String username;

    @NotBlank(message = "所属部门不能为空")
    private String department;

    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Email(message = "邮箱格式不正确")
    private String email;

    private String loginMode = "PASSWORD";

    @NotNull(message = "有效期不能为空")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime validStartTime;

    @NotNull(message = "有效期不能为空")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime validEndTime;

    @Size.List({
        @Size(min = 1, message = "角色不能为空"),
    })
    private List<String> roleIds;
}
