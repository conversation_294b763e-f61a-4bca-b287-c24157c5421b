package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
public class QueryAbResponse {
    @Schema(description = "id")
    private String id;
    @Schema(description = "源 ip")
    private String clientIp;
    @Schema(description = "源端口")
    private String clientPort;
    @Schema(description = "源地区")
    private String clientRegion;
    @Schema(description = "资产")
    private Asset asset;
    @Schema(description = "规则名称")
    private String ruleName;
    @Schema(description = "规则类型")
    private AbnormalBehaviorRuleEnum.AbnormalType abnormalType;
    @Schema(description = "规则详情")
    private String abnormalDetail;
    @Schema(description = "限制策略")
    private AbnormalBehaviorRuleEnum.Policy policy;
    @Schema(description = "限制详情")
    private String policyDetail;
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;
    private Boolean isActive;

    @Data
    @AllArgsConstructor
    public static class Asset {
        @Schema(description = "资产类型")
        private AbnormalBehaviorRuleEnum.AssetType assetType;
        @Schema(description = "资产id")
        private String assetId;
        private String assetName;
        @Schema(description = "请求方法")
        private List<String> httpMethod;
        @Schema(description = "请求路径")
        private String url;
    }
}
