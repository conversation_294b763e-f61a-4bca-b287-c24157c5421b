package com.telecom.apigateway.model.vo.request;

import cn.dev33.satoken.stp.StpUtil;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRule;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateAbRuleRequest extends AddAbRuleRequest implements Serializable {
    @NotBlank(message = "规则 id 不能为空")
    private String id;

    @Override
    public AbnormalBehaviorRule toRule() {
        AbnormalBehaviorRule rule = super.toRule();
        rule.setId(this.id);
        rule.setCreateTime(null);
        rule.setCreateUser(null);
        rule.setUpdateTime(LocalDateTime.now());
        rule.setUpdateUser(StpUtil.getLoginIdAsString());
        return rule;
    }
}
