package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ThreatDetailResponse {
    private String riskId;
    private String uri;
    private List<String> attackType;
    private String severity;
    private Boolean dealt;
    private String clientIP;
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime attackedTime;
    private String targetAddress;
    private String belongApplication;
    private String logRequest;
    private String logResponse;
    private Integer riskLevel;
    private List<String> guardModule;
    private Boolean isEncryptApi;
    private String decryptRequestBody;
}
