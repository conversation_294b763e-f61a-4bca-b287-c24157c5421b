package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.common.Constant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ThreatCountRequest implements Serializable {
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime startTime;
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime endTime;
    /**
     * 精确查询
     */
    private String appId;
}
