package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.model.enums.IpGroupTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class IpGroupResponse {
    private String groupId;
    private String name;
    private String content;
    private IpGroupTypeEnum type;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
