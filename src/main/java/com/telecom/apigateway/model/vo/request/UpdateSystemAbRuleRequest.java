package com.telecom.apigateway.model.vo.request;

import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRule;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleCondition;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Data
public class UpdateSystemAbRuleRequest implements Serializable {
    @NotBlank(message = "规则 id 不能为空")
    private String id;

    @NotNull(message = "持续时间不能为空")
    @Schema(description = "持续时间s")
    @Min(value = 10, message = "持续时间不能超过10~600")
    @Max(value = 600, message = "持续时间不能超过10~600")
    private Long abnormalDuration;

    @Schema(description = "异常阈值")
    @NotNull(message = "异常阈值不能为空")
    @Min(value = 1, message = "异常阈值不能小于1")
    private Long abnormalThreshold;

    private List<String> condition;

    @NotNull(message = "异常类型不能为空")
    private AbnormalBehaviorRuleEnum.AbnormalType abnormalType;

    @NotNull(message = "限制策略不能为空")
    private AbnormalBehaviorRuleEnum.Policy policy;

    @Min(value = 1, message = "封禁时间不能少于 1 分钟")
    @Max(value = Integer.MAX_VALUE, message = "封禁时间不能超过限制")
    private Long policyDuration;

    public AbnormalBehaviorRule toSystemRule() {


        // 系统规则能修改的字段
        AbnormalBehaviorRule abr = AbnormalBehaviorRule.builder()
                .id(id)
                .abnormalThreshold(abnormalThreshold)
                .abnormalDuration(abnormalDuration)
                .policy(policy)
                .policyDuration(policyDuration)
                .build();

        if (condition != null) {
            if (condition.isEmpty()) {
                abr.setCondition(Collections.emptyList());
            } else if (abnormalType == AbnormalBehaviorRuleEnum.AbnormalType.ERROR) {
                abr.setCondition(Collections.singletonList(new AbnormalBehaviorRuleCondition(
                        AbnormalBehaviorRuleEnum.Target.errorCode,
                        AbnormalBehaviorRuleEnum.Operation.containsAny,
                        StrUtil.join(",", condition),
                        null
                )));
            } else if (abnormalType == AbnormalBehaviorRuleEnum.AbnormalType.SENSITIVE) {
                abr.setCondition(Collections.singletonList(new AbnormalBehaviorRuleCondition(
                        AbnormalBehaviorRuleEnum.Target.sensitiveRule,
                        AbnormalBehaviorRuleEnum.Operation.containsAny,
                        StrUtil.join(",", condition),
                        null
                )));
            } else {
                abr.setCondition(Collections.emptyList());
            }
        }

        return abr;
    }
}
