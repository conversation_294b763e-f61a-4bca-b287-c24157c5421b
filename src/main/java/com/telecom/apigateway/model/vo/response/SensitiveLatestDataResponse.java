package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.LogEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
public class SensitiveLatestDataResponse {
    private String logId;
    private String keyword;
    private String sensitiveRuleName;
    private Integer sensitiveLevel;
    private String apiId;
    private String apiName;
    private String appName;
    private String clientIp;
    private List<LogEnum.RequestState> requestState;
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime logTime;
}
