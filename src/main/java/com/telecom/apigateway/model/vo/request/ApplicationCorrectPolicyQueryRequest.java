package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.enums.CorrectPolicyActionEnum;
import com.telecom.apigateway.model.enums.CorrectPolicyStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用修正策略查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "应用修正策略查询请求")
public class ApplicationCorrectPolicyQueryRequest extends BasePageQueryRequest {
    
    @Schema(description = "策略名称")
    private String policyName;
    
    @Schema(description = "策略状态")
    private CorrectPolicyStatusEnum status;
    
    @Schema(description = "策略动作")
    private CorrectPolicyActionEnum action;
} 