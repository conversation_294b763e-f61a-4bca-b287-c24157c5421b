package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.enums.ProtocolEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Schema(description = "添加应用")
@Data
public class AddApplicationRequest implements Serializable {
    @Schema(description = "应用名称")
    @NotBlank
    private String name;

    @Schema(description = "域名")
    private String host;

    @Schema(description = "端口")
    private String port;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "应用管理人")
    private String owner;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "协议， HTTP、HTTPS")
    private ProtocolEnum protocol;

    @Schema(description = "类型， APPLICATION、 GROUP")
    private ApplicationTypeEnum type;

    private String parentId;

    @Schema(description = "区域")
    private List<String> area;
}
