package com.telecom.apigateway.model.vo.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class BatchFalsePositiveAndAccessListRequest implements Serializable {
    @Size(min = 1)
    private List<String> logIds;

    @NotBlank(message = "reason cannot be blank")
    private String reason;
    private String processSuggestion;
    private BlocklistRequest blocklistRequest;
}
