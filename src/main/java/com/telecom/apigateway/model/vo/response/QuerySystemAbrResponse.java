package com.telecom.apigateway.model.vo.response;

import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleCondition;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QuerySystemAbrResponse {

    private String id;
    /**
     * 规则名
     */
    private String name;

    /**
     * 类型
     */
    private AbnormalBehaviorRuleEnum.AbnormalType abnormalType;
    /**
     * 持续时间(s)
     */
    private Long abnormalDuration;
    /**
     * 阈值
     */
    private Long abnormalThreshold;
    /**
     * 限制策略
     */
    private AbnormalBehaviorRuleEnum.Policy policy;

    /**
     * 策略持续时间(分钟)
     */
    private Long policyDuration;

    /**
     * 是否启用
     */
    private Boolean enable;

    private List<AbnormalBehaviorRuleCondition> condition;
}
