package com.telecom.apigateway.model.vo.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.DecryptType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@Schema(name = "Api 查询信息", description = "api 信息")
public class ApiQueryResponse implements Serializable {
    private static final long serialVersionUID = -1991071522449669167L;

    @Schema(description = "标签")
    private HashSet<String> sensitiveRuleIds;
    private String tagIdsStr;

    @Schema(description = "id")
    private String id;
    private String mergeId;

    @Schema(description = "api 名称, 默认等于 uri")
    private String name;
    /**
     * 所用应用 id
     */
    @Schema(description = "所用应用 id")
    private String appId;
    private String mainApplicationId;
    /**
     * http method,get post put delete
     */
    @Schema(description = "http method,get post put delete")
    private List<String> httpMethods;
    /**
     * 涉敏等级
     */
    @Schema(description = "涉敏等级, 0安全/1低/2中/3高")
    private Integer sensitiveLevel;
    /**
     * 状态
     */
    @Schema(description = "是否在线")
    private Boolean isOnline;
    private Boolean merged;
    /**
     * uri
     */
    @Schema(description = "uri")
    private String uri;
    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean isActive;
    /**
     * 来源, 0自动 1手动
     */
    @Schema(description = "来源, 0自动 1手动,2合并")
    private Integer source;
    /**
     * 发现时间
     */
    @Schema(description = "发现时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime discoverTime;
    private String remark;

    @TableField(value = "create_user")
    private Integer createUser;
    private Integer updateUser;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;


    @TableField(value = "is_deleted")
    @JsonIgnore
    private Boolean deleted;

    private String appName;
    private String onlineTime;
    private List<String> hosts;
    private Integer port;
    private String url;
    private Boolean isEncryptApi;
    private String decryptKey;
    private DecryptType decryptType;


    private Integer visitCount;

    public String getHostName() {
        // todo
        return "待开发";
    }
}
