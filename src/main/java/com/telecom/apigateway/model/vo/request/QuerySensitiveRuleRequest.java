package com.telecom.apigateway.model.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.SensitiveRuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Schema(description = "查询涉敏规则请求参数")
public class QuerySensitiveRuleRequest implements Serializable {

    @Schema(description = "应用id")
    private String appId;

    @Schema(description = "规则名称")
    @Length(max = 100, message = "长度不能超过 100")
    private String name;

    @Schema(description = "规则等级")
    private Integer[] level;
    @Schema(description = "是否启用")
    private Boolean isEnable;
    @Schema(description = "来源")
    private SensitiveRuleEnum.Source source;

    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime startTime;
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime endTime;

    @Schema(description = "每页条数")
    @Max(value = 1000, message = "页码格式不正常")
    @Min(value = 1, message = "页码格式不正常")
    @NotNull(message = "分页参数缺失")
    private Integer pageNum;
    @Schema(description = "每页条数")
    @Max(value = 10000, message = "每页条数格式不正常")
    @Min(value = 1, message = "每页条数格式不正常")
    @NotNull(message = "分页参数缺失")
    private Integer pageSize;

}
