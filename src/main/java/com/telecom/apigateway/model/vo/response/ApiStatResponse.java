package com.telecom.apigateway.model.vo.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-09-10
 */
@Data
public class ApiStatResponse {

    private List<StatCount> resultCodes;
    private List<StatCount> countries;
    private List<StatCount> cities;
    private List<StatCount> sources;
    private List<StatCount> devices;
    private List<StatCount> visitTrends;

    /**
     * Merges another ApiStatResponse into this one.
     *
     * @param other the other ApiStatResponse to merge
     */
    public void merge(ApiStatResponse other) {
        this.resultCodes = mergeStatCounts(this.resultCodes, other.resultCodes);
        this.countries = mergeStatCounts(this.countries, other.countries);
        this.cities = mergeStatCounts(this.cities, other.cities);
        this.sources = mergeStatCounts(this.sources, other.sources);
        this.devices = mergeStatCounts(this.devices, other.devices);
        this.visitTrends = mergeStatCounts(this.visitTrends, other.visitTrends);
    }

    private List<StatCount> mergeStatCounts(List<StatCount> list1, List<StatCount> list2) {
        Map<String, Integer> countMap = new HashMap<>();

        if (list1 != null) {
            for (StatCount stat : list1) {
                countMap.put(stat.getLabel(), countMap.getOrDefault(stat.getLabel(), 0) + stat.getCount());
            }
        }

        if (list2 != null) {
            for (StatCount stat : list2) {
                countMap.put(stat.getLabel(), countMap.getOrDefault(stat.getLabel(), 0) + stat.getCount());
            }
        }

        List<StatCount> mergedList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
            mergedList.add(new StatCount(entry.getKey(), entry.getValue()));
        }

        return mergedList;
    }
}
