package com.telecom.apigateway.model.vo.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-05-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuerySensitivePortraitLogStatRequest extends PageAndTimeRequest implements Serializable {
    private String type;

    private String content;
    private String apiId;
    private String appId;
    private String clientIp;

    private Integer queryCount;

    public void format() {
        Optional.ofNullable(clientIp).ifPresent(s -> clientIp = s.trim());
        if (this.getStartTime() == null || this.getEndTime() == null) {
            LocalDateTime now = LocalDateTime.now();
            this.setStartTime(now.minusDays(7));
            this.setEndTime(now);
        }
    }
}
