package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.enums.ConfigTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
public class RuleConfigAction implements Serializable {
    
    @NotNull(message = "原配置不能为空")
    private ConfigTypeEnum from;
    
    @NotNull(message = "现配置不能为空")
    private ConfigTypeEnum to;
} 