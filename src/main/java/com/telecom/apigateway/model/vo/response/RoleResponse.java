package com.telecom.apigateway.model.vo.response;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RoleResponse {
    private String roleId;
    private String name;
    private String menuPermissionContent;
    private String dataPermissionContent;
    private Integer userCount;
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
