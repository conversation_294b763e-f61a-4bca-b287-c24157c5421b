package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AbRuleQueryRequest extends PageAndTimeRequest implements Serializable {
    @Schema(description = "规则名称")
    @Length(max = 20, message = "规则名称长度不能超过20个字符")
    private String name;

    @Schema(description = "规则状态")
    private Boolean enable;

    @Schema(description = "规则类型")
    private List<AbnormalBehaviorRuleEnum.AbnormalType> abnormalType;
}
