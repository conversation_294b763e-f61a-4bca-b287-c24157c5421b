package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.model.enums.ConfigTypeEnum;
import com.telecom.apigateway.model.enums.IpGroupTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class QueryCveRuleResponse {
    private String cveId;
    private String cveName;
    private ConfigTypeEnum status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
