package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryAbRequest extends PageAndTimeRequest implements Serializable {
    @Schema(description = "源 ip")
    private String clientIp;
    @Schema(description = "源端口")
    private String clientPort;
    @Schema(description = "异常资产名")
    private Set<String> assetId;
    private String assetName;
    @Schema(description = "异常类型")
    private List<AbnormalBehaviorRuleEnum.AbnormalType> abnormalType;
    @Schema(description = "策略")
    private List<AbnormalBehaviorRuleEnum.Policy> policy;

    public void format() {
        if (clientIp != null) {
            clientIp = clientIp.trim();
        }
        if (clientPort != null) {
            clientPort = clientPort.trim();
        }
        if (assetName != null) {
            assetName = assetName.trim();
        }
    }
}
