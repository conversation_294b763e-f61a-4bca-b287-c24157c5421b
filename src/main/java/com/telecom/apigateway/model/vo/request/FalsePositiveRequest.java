package com.telecom.apigateway.model.vo.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class FalsePositiveRequest implements Serializable {

    @NotBlank(message = "logId cannot be blank")
    private String logId;

    @NotBlank(message = "reason cannot be blank")
    private String reason;

    private String processSuggestion;
}
