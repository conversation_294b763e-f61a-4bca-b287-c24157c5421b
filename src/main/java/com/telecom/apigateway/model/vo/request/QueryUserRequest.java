package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "查询用户请求")
@EqualsAndHashCode(callSuper = true)
public class QueryUserRequest extends BasePageQueryRequest implements Serializable {
    @Schema(description = "查询类型：null-全部, 1-仅看临期, 2-仅看禁用, 3-仅看失效")
    private Integer type;
    
    @Schema(description = "所属部门（精确查询）")
    private String department;
    
    @Schema(description = "角色ID列表")
    private List<String> roleIds;
    
    @Schema(description = "模糊查询关键字（用户名或姓名）")
    private String keyword;
}
