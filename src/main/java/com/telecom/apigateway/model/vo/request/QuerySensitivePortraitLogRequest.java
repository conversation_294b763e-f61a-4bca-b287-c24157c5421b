package com.telecom.apigateway.model.vo.request;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.entity.SensitiveRule;
import com.telecom.apigateway.model.enums.LogEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuerySensitivePortraitLogRequest extends PageAndTimeRequest implements Serializable {
    private String content;
    private String exactContent;
    private String uri;
    private String url;
    private String exactClientIp;
    private String clientIp;
    private List<String> apiId;
    private List<String> appId;
    private List<String> ruleId;
    private List<Integer> sensitiveLevel;
    private List<LogEnum.RequestState> requestState;
    private String city;

    public void format() {
        Optional.ofNullable(uri).ifPresent(s -> uri = s.trim());
        Optional.ofNullable(url).ifPresent(s -> url = s.trim());
        Optional.ofNullable(clientIp).ifPresent(s -> clientIp = s.trim());
        Optional.ofNullable(exactClientIp).ifPresent(s -> exactClientIp = s.trim());
        Optional.ofNullable(city).ifPresent(s -> {
            city = s.trim();
            if (Constant.INNER_REGION.contains(city)) {
                city = Constant.INNER_REGION_CODE;
            } else if (Constant.UNKNOWN_REGION.contains(city)) {
                city = Constant.UNKNOWN_REGION_CODE;
            }
        });
        if (this.getStartTime() == null || this.getEndTime() == null) {
            LocalDateTime now = LocalDateTime.now();
            this.setStartTime(now.minusDays(7));
            this.setEndTime(now);
        }
    }

    public EsQueryDTO toEsQueryDTO(List<SensitiveRule> rules) {
        this.format();
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                // .pageNum(this.getPageNum())
                // .pageSize(this.getPageSize())
                .start(this.getStartTime())
                .end(this.getEndTime())
                .build()
                .addExistQuery("sensitiveRules")
                .addQuery("requestResourceType", Constant.Api.REQUEST_RESOURCE_TYPE_API)
                .addFuzzyQuery(StrUtil.isNotBlank(content), "sensitiveRules.content", content)
                .addQuery(StrUtil.isNotBlank(exactContent), "sensitiveRules.content", exactContent)
                .addFuzzyQuery(StrUtil.isNotBlank(uri), "uri", uri)
                .addFuzzyQuery(StrUtil.isNotBlank(url), "url", url)
                .addFuzzyQuery(StrUtil.isNotBlank(clientIp), "clientIp", clientIp)
                .addQuery(StrUtil.isNotBlank(exactClientIp), "clientIp", exactClientIp)
                .addMultipleQuery(CollUtil.isNotEmpty(apiId), "apiId", apiId)
                .addMultipleQuery(CollUtil.isNotEmpty(ruleId), "sensitiveRules.ruleId", ruleId)
                .addFuzzyQuery(StrUtil.isNotBlank(city), "clientIpInfo.city", city);
        if (StrUtil.isNotBlank(city)) {
            queryDTO.addShouldQuery(Arrays.asList(
                    new EsQueryDTO.Element("clientIpInfo.country", city, EsQueryDTO.QueryType.NESTED_LIKE),
                    new EsQueryDTO.Element("clientIpInfo.province", city, EsQueryDTO.QueryType.NESTED_LIKE),
                    new EsQueryDTO.Element("clientIpInfo.city", city, EsQueryDTO.QueryType.NESTED_LIKE)
            ));
        }

        if (sensitiveLevel != null) {
            List<String> ruleIds =
                    rules.stream().filter(rule -> sensitiveLevel.contains(rule.getLevel()))
                            .map(SensitiveRule::getId).collect(Collectors.toList());
            queryDTO.addMultipleQuery(CollUtil.isNotEmpty(ruleIds), "sensitiveRules.ruleId", ruleIds);
        }

        QueryLogRequest.buildStateQuery(queryDTO, requestState);

        return queryDTO;
    }


}
