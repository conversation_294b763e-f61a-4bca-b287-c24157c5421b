package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class BlocklistRequest implements Serializable {
    private static final long serialVersionUID = 978819052804116326L;
    @NotBlank(message = "名称不能为空")
    @Size(max = 20, message = "名称长度不能超过20个字符")
    private String name;

    @NotBlank(message = "类型不能为空")
    private String type;

    @Size(min = 1, message = "条件不能为空")
    private List<Condition> condition;

    @Schema(description = "优先级")
    @NotNull(message = "不能为空")
    @Positive(message = "需为正数")
    @Max(value = 100, message = "优先级不能超过100")
    @Min(value = 1, message = "优先级不能小于1")
    private Integer priority;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Condition implements Serializable {
        private static final long serialVersionUID = -33334395902843129L;
        private String target;
        private String operation;
        private String value;
        // 给前端反显数据
        private Object[] valueArr;
        private String headerName;

        public Condition(String target, String operation, String value) {
            this.target = target;
            this.operation = operation;
            this.value = value;
        }
    }
}
