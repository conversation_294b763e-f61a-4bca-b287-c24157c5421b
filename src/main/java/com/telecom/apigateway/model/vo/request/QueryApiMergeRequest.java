package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.enums.ApiMergeEnum;

import java.io.Serializable;

public class QueryApiMergeRequest extends PageRequest implements Serializable {
    private String name;
    private Boolean enable;
    private ApiMergeEnum.Policy policy;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public ApiMergeEnum.Policy getPolicy() {
        return policy;
    }

    public void setPolicy(ApiMergeEnum.Policy policy) {
        this.policy = policy;
    }
}
