package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.entity.UserInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "用户登录返回信息")
public class UserLoginResponse {
    
    @Schema(description = "用户信息")
    private UserInfoResponse userInfo;
    
    @Schema(description = "登录后跳转的首页路由名")
    private String homeName;
    
    @Schema(description = "角色编码列表")
    private List<String> roles;

    @Schema(description = "权限码列表")
    private List<String> permissionCodes;
    
    @Schema(description = "拥有访问权限的路由名称列表")
    private List<String> accessRoutes;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "是否需要修改密码")
    private Boolean changePassword;

    @Data
    @Schema(description = "用户基本信息")
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserInfoResponse {
//        @Schema(description = "用户ID")
//        private Long userId;

        @Schema(description = "用户名")
        private String userName;

        @Schema(description = "真实姓名")
        private String realName;

        @Schema(description = "部门")
        private String department;

        @Schema(description = "手机")
        private String phone;

        @Schema(description = "邮箱")
        private String email;

        @Schema(description = "有效期")
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime validEndTime;

        private List<RoleLabelResponse> role;
    }
} 