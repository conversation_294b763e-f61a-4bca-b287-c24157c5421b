package com.telecom.apigateway.model.vo.response;

import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import com.telecom.apigateway.model.enums.LogEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024-11-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LogQueryResponse extends EsNginxDTO {
    private static final long serialVersionUID = 4864461704926875378L;

    private String appName;
    private Set<String> sensitiveRuleIds;
    private Set<String> riskTypes;

    @Schema(description = "请求状态")
    private List<LogEnum.RequestState> requestState;

    @Schema(description = "拦截规则名称, 如果被拦截")
    private String blocklistName;

    @Schema(description = "异常行为名称")
    private String abnormalBehaviorRuleName;
    @Schema(description = "异常行为类型")
    private AbnormalBehaviorRuleEnum.AbnormalType abnormalType;

    @Schema(description = "是否是加密api")
    private Boolean isEncryptApi;
}
