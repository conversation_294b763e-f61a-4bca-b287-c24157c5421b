package com.telecom.apigateway.model.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Schema(description = "查询涉敏api请求参数")
public class QuerySensitiveApiRequest implements Serializable {

    @Schema(description = "api的uri")
    private String uri;

    @Schema(description = "应用id, 数组")
    private List<String> appIds;
    @Schema(description = "url")
    private String url;
    @Schema(description = "涉敏规则id, 数组")
    private String[] sensitiveRuleIds;
    @Schema(description = "敏感等级, 数组")
    private Integer[] sensitiveLevels;

    /**
     * 查询什么时间
     * 发现时间 discoverTime、上线时间 onlineTime、更新时间 updateTime
     */
    @Schema(description = "时间范围类型")
    private String rangeType;

    @Schema(description = "时间范围, 1:1小时, 2-1天, 3-一周, 4: 一月")
    private Integer range;

    @Schema(description = "查询开始时间")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime startTime;

    @Schema(description = "查询结束时间")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime endTime;

    @Max(value = 1000, message = "页码格式不正常")
    @Min(value = 1, message = "页码格式不正常")
    @NotNull(message = "分页参数缺失")
    private Integer pageNum;
    @Max(value = 10000, message = "每页条数格式不正常")
    @Min(value = 1, message = "每页条数格式不正常")
    @NotNull(message = "分页参数缺失")
    private Integer pageSize;
}
