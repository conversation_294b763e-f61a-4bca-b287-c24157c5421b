package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.entity.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "标签信息")
@Data
public class TagResponse {
    @Schema(description = "标签id")
    private String tagId;
    @Schema(description = "标签名")
    private String tagName;
    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    public static TagResponse covertTagResponse(Tag tag) {
        TagResponse tagResponse = new TagResponse();
        tagResponse.setTagName(tag.getTagName());
        tagResponse.setTagId(tag.getTagId());
        tagResponse.setCreateTime(tag.getCreateTime());
        return tagResponse;
    }
}
