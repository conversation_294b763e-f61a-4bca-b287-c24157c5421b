package com.telecom.apigateway.model.vo.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OwaspRuleConfigRequest implements Serializable {
    
    @NotBlank(message = "规则文件名不能为空")
    private String filename;
    
    @NotNull(message = "变更类型不能为空")
    private RuleConfigAction action;
} 