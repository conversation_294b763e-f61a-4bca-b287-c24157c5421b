package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class UserDetailResponse {
    private String username;

    private String realName;

    private String department;

    private String phone;

    private String email;

    private String loginMode = "PASSWORD";

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime validStartTime;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime validEndTime;

    private List<String> roleIds;
}
