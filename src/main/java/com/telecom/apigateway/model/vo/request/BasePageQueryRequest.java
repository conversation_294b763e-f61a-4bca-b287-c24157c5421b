package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Schema(description = "分页查询")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasePageQueryRequest implements Serializable {
    @Schema(description = "每页查询数量")
    private Integer pageSize = 20;

    @Schema(description = "当前页")
    private Integer pageNum = 1;
}
