package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Schema(description = "权限请求")
public class PermissionRequest implements Serializable {
    
    @Schema(description = "权限ID")
    private Long id;
    
    @NotBlank(message = "菜单名称不能为空")
    @Schema(description = "菜单名称")
    private String name;
    
    @NotBlank(message = "路由路径不能为空")
    @Schema(description = "前端路由路径")
    private String routePath;
    
    @Schema(description = "父菜单ID")
    private Long parentId;
    
    @Schema(description = "排序")
    private Integer sort;
    
    @Schema(description = "图标")
    private String icon;
    
    @NotBlank(message = "组件路径不能为空")
    @Schema(description = "组件路径")
    private String component;
} 