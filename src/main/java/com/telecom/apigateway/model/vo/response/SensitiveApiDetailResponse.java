package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-11-25
 */
@Builder
@Data
public class SensitiveApiDetailResponse {
    @Schema(description = "涉敏api id")
    private String sensitiveApiId;
    @Schema(description = "api id")
    private String apiId;
    @Schema(description = "uri")
    private String uri;
    @Schema(description = "api 名称")
    private String apiName;
    @Schema(description = "日志id")
    private String logId;
    @Schema(description = "涉敏日志请求")
    private String logRequest;
    @Schema(description = "涉敏日志响应")
    private String logResponse;
    @Schema(description = "涉敏关键字")
    private String keyword;
    @Schema(description = "应用名")
    private String appName;
    @Schema(description = "涉敏规则id")
    private String sensitiveRuleId;
    @Schema(description = "涉敏规则名")
    private String sensitiveRuleName;
    @Schema(description = "涉敏等级")
    private Integer sensitiveLevel;
    @Schema(description = "发现时间")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime discoverTime;

    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    @Schema(description = "最后涉敏时间")
    private LocalDateTime lastSensitiveTime;

    @Schema(description = "涉敏次数")
    private Integer sensitiveCount;
}
