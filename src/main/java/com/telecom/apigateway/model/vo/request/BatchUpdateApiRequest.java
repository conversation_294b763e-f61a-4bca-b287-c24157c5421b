package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 批量更新api
 *
 * <AUTHOR>
 * @date 2024-08-19
 */
@Data
public class BatchUpdateApiRequest implements Serializable {

    private static final long serialVersionUID = -2643260144617147360L;

    @Schema(description = "apiI数组")
    @NotEmpty(message = "不能为空")
    private List<String> ids;
    @Schema(description = "true / false 上线/下线")
    @NotNull(message = "不能为空")
    private Boolean isOnline;
}
