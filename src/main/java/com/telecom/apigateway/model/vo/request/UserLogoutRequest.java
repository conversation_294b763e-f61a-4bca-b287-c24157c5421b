package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Schema(description = "用户登录请求")
public class UserLogoutRequest implements Serializable {
    
    @NotBlank(message = "token不能为空")
    @Schema(description = "token")
    private String accessToken;
    
    @NotBlank(message = "refresh token不能为空")
    @Schema(description = "refresh token")
    private String refreshToken;
} 