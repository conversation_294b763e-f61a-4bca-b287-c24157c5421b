package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.LogEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-25
 */
@Data
public class SensitivePortraitLogResponse {
    private String logId;
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime logTime;
    private String apiId;
    private String apiName;
    private String appId;
    private String appName;
    private String uri;
    private String url;
    private List<LogEnum.RequestState> requestState;
    private String clientIp;
    private String addr;
    private Integer sensitiveLevel;
    private String sensitiveRule;
    private String sensitiveContent;

}
