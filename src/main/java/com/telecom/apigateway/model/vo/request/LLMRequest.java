package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @program: APIWG-Service
 * @ClassName LLMRequest
 * @description:
 * @author: Levi
 * @create: 2025-01-23 11:50
 * @Version 1.0
 **/
@Data
@Schema(description = "大模型智能分析请求")
public class LLMRequest {
    @NotBlank(message = "分析内容不能为空")
    @Schema(description = "分析内容")
    private String requestBody;
    @NotBlank(message = "分析类型为空")
    @Schema(description = "分析类型")
    private Integer llmType;
    @NotBlank(message = "日志id")
    @Schema(description = "日志id")
    private String logId;
}