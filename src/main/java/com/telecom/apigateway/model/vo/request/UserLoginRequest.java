package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Schema(description = "用户登录请求")
public class UserLoginRequest implements Serializable {
    
    @NotBlank(message = "用户名或手机号不能为空")
    @Schema(description = "用户名或手机号")
    private String usernameOrPhone;
    
    @NotBlank(message = "密码不能为空")
    @Schema(description = "密码")
    private String password;

    @Schema(description = "图形验证码")
    private String captChaId;
} 