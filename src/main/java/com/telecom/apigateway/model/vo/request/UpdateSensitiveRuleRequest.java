package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.entity.SensitiveRule;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
public class UpdateSensitiveRuleRequest implements Serializable {
    @Schema(description = "主键")
    @NotBlank(message = "id不能为空")
    private String id;

    @Schema(description = "规则名称")
    private String name;

    @Schema(description = "规则等级")
    private Integer level;

    @Schema(description = "是否启用")
    private Boolean isEnable;

    public SensitiveRule toRule() {
        return SensitiveRule.newUpdateRule(this.id, this.name, this.level, this.isEnable);
    }
}
