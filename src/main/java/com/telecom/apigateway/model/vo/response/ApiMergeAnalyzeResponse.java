package com.telecom.apigateway.model.vo.response;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
public class ApiMergeAnalyzeResponse implements Serializable {

    private static final long serialVersionUID = 2692418276121094525L;
    /**
     * 可否合并
     */
    private boolean mergeable;

    /**
     * 路径
     */
    private String uri;

    private List<MatchMergeApiResponse> matchedApis;


    public boolean isMergeable() {
        return mergeable;
    }

    public void setMergeable(boolean mergeable) {
        this.mergeable = mergeable;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public List<MatchMergeApiResponse> getMatchedApis() {
        return matchedApis;
    }

    public void setMatchedApis(List<MatchMergeApiResponse> matchedApis) {
        this.matchedApis = matchedApis;
    }

    public static ApiMergeAnalyzeResponse ofNotMergeable() {
        ApiMergeAnalyzeResponse response = new ApiMergeAnalyzeResponse();
        response.setMergeable(false);
        return response;
    }
}
