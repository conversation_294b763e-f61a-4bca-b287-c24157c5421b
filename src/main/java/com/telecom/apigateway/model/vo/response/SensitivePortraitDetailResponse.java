package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
public class SensitivePortraitDetailResponse {
    @Data
    public static class Content {
        private String keyword;
        private List<SensitivePortraitResponse.Content.RuleAndLevel> sensitiveRules;
        private List<ApiAndApp> apis;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime lastLogTime;

        private Integer sensitiveCount;
        private Integer sensitiveApiCount;
        private Integer clientIpCount;

        @Data
        @Builder
        public static class ApiAndApp {
            private String apiId;
            private String apiName;
            private String appId;
            private String appName;
        }
    }

    @Data
    public static class Api {
        private String apiId;
        private String apiName;
        private Integer sensitiveLevel;
        private List<String> sensitiveRules;
        private String appName;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime lastLogTime;

        private Long sensitiveContentCount;
        private Long sensitiveCount;
        private Long clientIpCount;
    }

    @Data
    public static class App {
        private String appId;
        private String appName;
        private List<String> sensitiveRules;
        private String parentAppName;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime lastLogTime;

        private Integer sensitiveContentCount;
        private Integer sensitiveCount;
        private Integer clientIpCount;
        private Integer sensitiveApiCount;
    }

    @Data
    public static class ClientIp {
        private String clientIp;
        private String addr;
        private String isp;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime lastLogTime;

        private List<String> sensitiveRules;
        private Integer sensitiveContentCount;
        private Integer sensitiveCount;
        private Integer sensitiveApiCount;
    }
}
