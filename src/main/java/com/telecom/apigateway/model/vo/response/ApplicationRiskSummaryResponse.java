package com.telecom.apigateway.model.vo.response;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class ApplicationRiskSummaryResponse {
    /**
     * 最近30天威胁总数
     */
    private Integer totalAttacksCount;

    /**
     * 涉敏 API 总数
     */
    private Long totalSensitiveApiCount;

    /**
     * 拦截占比
     */
    private Double interceptedAttackPercentage;

    /**
     * 涉敏 API 占比
     */
    private Double sensitiveApiPercentage;
}
