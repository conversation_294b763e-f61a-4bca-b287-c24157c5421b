package com.telecom.apigateway.model.vo.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class CreateIpGroupRequest implements Serializable {
    @NotBlank(message = "ip组名称不能为空")
    private String name;

    @Size(min = 1, message = "ip不能为空")
    private List<String> ipGroups;
}
