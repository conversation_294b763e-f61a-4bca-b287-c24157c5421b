package com.telecom.apigateway.model.vo.response;

import com.telecom.apigateway.model.enums.RiskLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseLevelResponse {
    private Integer count;
    private Integer highCount;
    private Integer mediumCount;
    private Integer lowCount;

    public void setByLevel(RiskLevelEnum levelEnum, Integer count) {
        if (Objects.isNull(levelEnum)) {
            return;
        }
        if (levelEnum.equals(RiskLevelEnum.HIGH)) {
            this.highCount = count;
        } else if (levelEnum.equals(RiskLevelEnum.MEDIUM)) {
            this.mediumCount = count;
        } else if (levelEnum.equals(RiskLevelEnum.LOW)) {
            this.lowCount = count;
        }
    }

    public void add(BaseLevelResponse other) {
        if (other == null) return;
        this.count += other.getCount();
        this.highCount += other.getHighCount();
        this.mediumCount += other.getMediumCount();
        this.lowCount += other.getLowCount();
    }
}
