package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * api 查询条件
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@Schema(description = "api 查询条件")
public class QueryApiRequest implements Serializable {

    @Schema(description = "api id")
    @Length(min = 32, max = 32, message = "id 格式不正常")
    private String id;
    @Schema(description = "api 名称, 默认等于 uri")
    private String name;

    @Schema(description = "api id 列表")
    private List<String> ids;
    @Schema(description = "应用 id 列表")
    private List<String> appIds;

    @Schema(description = "时间范围")
    @Max(value = 4, message = "时间范围格式不正常")
    @Min(value = 1, message = "时间范围格式不正常")
    private Integer range;
    /**
     * 查询什么时间
     * 发现时间 discoverTime、上线时间 onlineTime、更新时间 updateTime
     */
    @Schema(description = "时间范围类型")
    private String rangeType;

    @Schema(description = "开始时间")
    private String startTime;
    @Schema(description = "结束时间")
    private String endTime;
    @Schema(description = "http 方法")
    private String httpMethod;

    @Schema(description = "uri")
    private String uri;
    @Schema(description = "url")
    private String url;
    @Schema(description = "host")
    private String host;
    @Schema(description = "端口")
    private Integer port;
    @Schema(description = "路径级别")
    private Integer pathLevel;
    @Schema(description = "是否上线")
    private Boolean isOnline;

    @Schema(description = "涉敏等级, 0安全/1低/2中/3高")
    private Integer[] sensitiveLevels;

    @Schema(description = "是否激活")
    private Boolean isActive;
    private List<String> aliveApiIds;
    @Schema(description = "涉敏规则标签id")
    private List<String> tagIds;

    @Schema(description = "开始时间")
    private LocalDateTime start;
    @Schema(description = "结束时间")
    private LocalDateTime end;

    @Schema(description = "页码")
    @Max(value = 1000, message = "页码格式不正常")
    @Min(value = 1, message = "页码格式不正常")
    @NotNull(message = "分页参数缺失")
    private Integer pageNum;
    @Schema(description = "每页条数")
    @Max(value = 10000, message = "每页条数格式不正常")
    @Min(value = 1, message = "每页条数格式不正常")
    @NotNull(message = "分页参数缺失")
    private Integer pageSize;

    private Boolean isEncryptApi;

    public void format(){
        uri = Optional.ofNullable(uri).map(String::trim).orElse(null);
        url = Optional.ofNullable(url).map(String::trim).orElse(null);
        name = Optional.ofNullable(name).map(String::trim).orElse(null);
        rangeType = Optional.ofNullable(rangeType).map(String::trim).orElse(null);
        host = Optional.ofNullable(host).map(String::trim).orElse(null);
        startTime = Optional.ofNullable(startTime).map(String::trim).orElse(null);
        endTime = Optional.ofNullable(endTime).map(String::trim).orElse(null);
    }
}
