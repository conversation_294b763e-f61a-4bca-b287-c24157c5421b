package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Schema(description = "新增或修改标签")
@Data
public class AddOrUpdateTag implements Serializable {
    @Schema(description = "标签id，新增时传空")
    private String tagId;

    @Schema(description = "标签名称")
    @NotEmpty(message = "标签名称不能为空")
    private String tagName;
}
