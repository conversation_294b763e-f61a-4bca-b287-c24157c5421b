package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.entity.ApiMergeCondition;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

public class AddApiMergeRequest implements Serializable {

    private String name;
    private String appId;
    private String apiName;
    /**
     * 策略
     */
    private ApiMergeEnum.Policy policy;

    @Schema(description = "匹配条件")
    private List<ApiMergeCondition> condition;

    private String urlReg;
    private List<String> httpMethods;

    private String remark;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ApiMergeEnum.Policy getPolicy() {
        return policy;
    }

    public void setPolicy(ApiMergeEnum.Policy policy) {
        this.policy = policy;
    }

    public List<ApiMergeCondition> getCondition() {
        return condition;
    }

    public void setCondition(List<ApiMergeCondition> condition) {
        this.condition = condition;
    }

    public String getUrlReg() {
        return urlReg;
    }

    public void setUrlReg(String urlReg) {
        this.urlReg = urlReg;
    }

    public List<String> getHttpMethods() {
        return httpMethods;
    }

    public void setHttpMethods(List<String> httpMethods) {
        this.httpMethods = httpMethods;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public ApiMerge toIgnoreEntity() {
        return ApiMerge.ofIgnore(name, condition);
    }

    public ApiMerge toMergeEntity(List<String> api) {
        return ApiMerge.ofMerge(appId, name, apiName, urlReg, httpMethods, api);
    }
}
