package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.dto.PolicyConditionDTO;
import com.telecom.apigateway.model.enums.CorrectPolicyActionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 添加应用修正策略请求
 */
@Data
@Schema(description = "添加应用修正策略请求")
public class AddApplicationCorrectPolicyRequest {
    
    @NotBlank(message = "策略名称不能为空")
    @Schema(description = "策略名称")
    private String policyName;
    
    @NotNull(message = "策略动作不能为空")
    @Schema(description = "策略动作")
    private CorrectPolicyActionEnum action;
    
    @Schema(description = "策略条件列表")
    private List<PolicyConditionDTO> conditions;
    
    @Schema(description = "合并后的应用名称")
    private String mergedAppId;
} 