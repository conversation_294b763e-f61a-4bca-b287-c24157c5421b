package com.telecom.apigateway.model.vo.response;

import lombok.Builder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-25
 */
@lombok.Data
public class SensitivePortraitChartResponse {

    @lombok.Data
    @Builder
    public static class Content {
        private List<StatCount> trends;
        private List<SensitiveApiTriggerCountResponse> topApis;
        private List<StatCount> topClientIps;
    }

    @lombok.Data
    @Builder
    public static class Api {
        private List<StatCount> trends;
        private List<StatCount> topRules;
        private List<StatCount> topContents;
        private List<StatCount> topClientIps;
    }

    @lombok.Data
    @Builder
    public static class App {
        private List<StatCount> trends;
        private List<SensitiveApiTriggerCountResponse> topApis;
        private List<StatCount> topRules;
        private List<StatCount> topContents;
        private List<StatCount> topClientIps;
    }

    @lombok.Data
    @Builder
    public static class ClientIp {
        private List<StatCount> trends;
        private List<SensitiveApiTriggerCountResponse> topApis;
        private List<StatCount> topRules;
        private List<StatCount> topContents;
    }
}
