package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-11-06
 */
@Data
@Schema(description = "客户端 ip 获取的涉敏信息统计")
public class SensitiveKeywordResponse {
    @Schema(description = "关键字/获取的信息")
    private String keyword;
    @Schema(description = "涉敏标签")
    private String sensitiveTag;
    @Schema(description = "应用名称")
    private String appName;
    @Schema(description = "获取时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;
    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @Schema(description = "获取次数")
    private Integer count;

}
