package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class BlocklistResponse {
    private String blockId;

    private String name;

    private String type;

    private List<BlocklistRequest.Condition> condition;

    @Schema(description = "优先级, 1-100")
    private Integer priority;
    @Schema(description = "触发次数")
    private Long triggerCount;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Condition {
        private String target;
        private String operation;
        private String value;
        private String headerName;
    }
}
