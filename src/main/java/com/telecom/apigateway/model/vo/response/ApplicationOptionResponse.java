package com.telecom.apigateway.model.vo.response;

import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ApplicationOptionResponse {
    private String applicationId;
    private String applicationName;
    private String parentId;
    private ApplicationTypeEnum applicationType;

    public ApplicationOptionResponse(Application application) {
        this.applicationId = application.getApplicationId();
        this.applicationName = application.getName();
        this.parentId = application.getParentId();
        this.applicationType = application.getType();
    }
}
