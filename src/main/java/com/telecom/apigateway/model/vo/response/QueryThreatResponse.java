package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.model.enums.RiskLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryThreatResponse {
    private String uri;
    private String belongApplication;
    private String attackType;
    private Boolean dealt;
    private String severity;
    /**
     * 攻击者ip
     */
    private String clientIp;

    /**
     * 攻击者端口
     */
    private Integer clientPort;

    /**
     * 攻击者地区
     */
    private String clientRegion;

    /**
     * 攻击目标地址
     */
    private String targetAddress;

    /**
     * 攻击目标地址
     */
    private String targetRegion;

    /**
     * 攻击时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime attackedTime;

    private String riskLogId;
    private String apiId;
    private Integer riskLevel;
    private String logId;
    private String falsePositiveReason;
    private String processSuggestion;
    private String requestLog;
    private String responseLog;
}
