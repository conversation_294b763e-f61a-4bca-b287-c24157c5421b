package com.telecom.apigateway.model.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-08-21
 */
@Builder
@Data
@Schema(name = "ApiHttpInfoResponse", description = "api http 信息")
public class ApiHttpInfoResponse implements Serializable {
    private static final long serialVersionUID = -1991071522449669167L;

    @Schema(name = "apiId", description = "api id")
    private String apiId;
    @Schema(name = "requestHeaders", description = "请求头")
    private String requestHeaders;
    @Schema(name = "requestParam", description = "请求参数")
    private String requestParam;
    @Schema(name = "requestBody", description = "请求体")
    private String requestBody;
    @Schema(name = "responseBody", description = "响应体")
    private String responseBody;
    @Schema(name = "responseCode", description = "响应码")
    private String responseCode;
}
