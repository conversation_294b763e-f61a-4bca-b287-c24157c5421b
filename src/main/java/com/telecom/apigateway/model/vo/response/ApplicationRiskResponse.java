package com.telecom.apigateway.model.vo.response;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ApplicationRiskResponse {

    // 风险API计数
    private RiskCounts riskApiCount;

    // 涉敏API计数
    private SensitiveCounts sensitiveApiCount;

    // 风险API计数子类
    @Data
    public static class RiskCounts {
        private Integer high = 0;
        private Integer medium = 0;
        private Integer low = 0;
    }

    // 涉敏API计数子类
    @Data
    public static class SensitiveCounts {
        private Integer high = 0;
        private Integer medium = 0;
        private Integer low = 0;
    }
}
