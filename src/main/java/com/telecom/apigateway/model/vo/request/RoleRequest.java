package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
@Schema(description = "角色请求")
public class RoleRequest implements Serializable {
    @NotBlank(message = "角色名称不能为空")
    @Schema(description = "角色名称")
    private String name;
    
    @NotEmpty(message = "菜单权限列表不能为空")
    @Schema(description = "菜单权限列表")
    private List<String> menuPermission;

    @Schema(description = "数据权限列表")
    private List<String> dataPermission = Collections.emptyList();
} 