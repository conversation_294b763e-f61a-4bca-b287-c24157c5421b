package com.telecom.apigateway.model.vo.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SensitiveTopResponse {

    private Long total;
    private Long increase;
    /**
     * 增加量
     */
    private List<TopData> topData;

    public static SensitiveTopResponse empty() {
        return new SensitiveTopResponse(0L, 0L, Collections.emptyList());
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class TopData {
        private String label;
        private Long count;
        /**
         * 增加量
         */
        private Long increase;
    }
}
