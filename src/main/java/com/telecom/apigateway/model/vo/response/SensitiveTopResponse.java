package com.telecom.apigateway.model.vo.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SensitiveTopResponse {

    private Integer total;
    private Integer increase;
    /**
     * 增加量
     */
    private List<TopData> topData;

    public static SensitiveTopResponse empty() {
        return new SensitiveTopResponse(0, 0, Collections.emptyList());
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class TopData {
        private String label;
        private Integer count;
        /**
         * 增加量
         */
        private Integer increase;
    }
}
