package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.dto.PolicyConditionDTO;
import com.telecom.apigateway.model.entity.ApplicationCorrectPolicy;
import com.telecom.apigateway.model.enums.CorrectPolicyActionEnum;
import com.telecom.apigateway.model.enums.CorrectPolicyStatusEnum;
import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 应用修正策略响应
 */
@Data
@Schema(description = "应用修正策略响应")
public class ApplicationCorrectPolicyResponse {
    
    @Schema(description = "策略ID")
    private String policyId;
    
    @Schema(description = "策略名称")
    private String policyName;
    
    @Schema(description = "策略状态")
    private CorrectPolicyStatusEnum status;
    
    @Schema(description = "策略动作")
    private CorrectPolicyActionEnum action;
    
    @Schema(description = "策略条件列表")
    private List<PolicyConditionDTO> conditions;
    
    @Schema(description = "合并后的应用名称")
    private String mergedAppName;
    
    @Schema(description = "合并后统一展示的IP/域名")
    private String mergedAppHost;
    
    @Schema(description = "是否曾经启用过")
    private Boolean everEnabled;
    
    @Schema(description = "创建人")
    private String createUser;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;
    
    public ApplicationCorrectPolicyResponse(ApplicationCorrectPolicy policy) {
        this.policyId = policy.getPolicyId();
        this.policyName = policy.getPolicyName();
        this.status = policy.getStatus();
        this.action = policy.getAction();
        this.conditions = JSONUtil.toList(JSONUtil.parseArray(policy.getConditions()), PolicyConditionDTO.class);
        this.mergedAppName = policy.getMergedAppName();
        this.mergedAppHost = policy.getMergedAppHost();
        this.everEnabled = policy.getEverEnabled();
        this.createUser = policy.getCreateUser();
        this.createTime = policy.getCreateTime();
        this.updateTime = policy.getUpdateTime();
    }
} 