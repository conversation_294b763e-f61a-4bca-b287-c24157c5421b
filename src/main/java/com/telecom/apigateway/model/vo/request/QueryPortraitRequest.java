package com.telecom.apigateway.model.vo.request;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 查询画像请求
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryPortraitRequest extends PageRequest implements Serializable {

    @Schema(description = "关键词,可以有多个")
    private Set<String> keywords;

    @Schema(description = "上一页的最后一个key,作为es 续传查询")
    private String lastKey;

    public void format() {
        if (CollUtil.isEmpty(keywords)) {
            return;
        }
        keywords = keywords.stream().map(String::trim).collect(Collectors.toSet());
        keywords.removeIf(StrUtil::isBlank);
        //  request.getKeywords() 去重
        keywords = new LinkedHashSet<>(keywords); // 确保不修改原始集合
    }
}
