package com.telecom.apigateway.model.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * api 风险概览
 *
 * <AUTHOR>
 * @date 2024-08-19
 */
@Builder
@Data
@Schema(description = "api 风险概览")
public class ApiRiskSummaryResponse implements Serializable {

    private static final long serialVersionUID = 4391994442603379373L;

    @Schema(name = "今日攻击总数")
    private long todayAttackApiCount;
    @Schema(name = "过去一周攻击总数")
    private long weekAttackApiCount;
    @Schema(name = "拦截攻击总数")
    private float interceptAttackPercent;
    @Schema(name = "敏感 api 总数")
    private long sensitiveApiCount;
}
