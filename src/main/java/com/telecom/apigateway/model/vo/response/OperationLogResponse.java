package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 操作日志响应
 */
@Data
@Schema(description = "操作日志响应")
public class OperationLogResponse {
    
    @Schema(description = "日志ID")
    private String logId;
    
    @Schema(description = "用户名")
    private String username;
    
    @Schema(description = "姓名")
    private String realName;
    
    @Schema(description = "操作类型")
    private String operationType;
    
    @Schema(description = "资源类型")
    private String resourceType;
    
    @Schema(description = "操作描述")
    private String description;
    
    @Schema(description = "IP地址")
    private String ip;
    
    @Schema(description = "操作时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime operationTime;
}
