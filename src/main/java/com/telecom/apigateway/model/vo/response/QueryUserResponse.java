package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class QueryUserResponse {
    private String username;
    private String realName;
    private String status;
    private List<String> roles;
    private String email;
    private String phone;
    private String department;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime validTime;
    private String loginMode;
    private String updateUser;
}
