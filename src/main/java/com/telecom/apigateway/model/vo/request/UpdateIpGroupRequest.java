package com.telecom.apigateway.model.vo.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class UpdateIpGroupRequest extends CreateIpGroupRequest implements Serializable {
    @NotBlank(message = "ip组ip不能为空")
    private String groupId;
}
