package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "标签名称查询")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryTagRequest extends BasePageQueryRequest implements Serializable {
    @Schema(description = "标签名称搜索参数")
    private String name;

    public QueryTagRequest(BasePageQueryRequest base) {
        super(base.getPageSize(), base.getPageNum());
    }
}
