package com.telecom.apigateway.model.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * api 概览
 *
 * <AUTHOR>
 * @date 2024-08-19
 */
@Builder
@Data
@Schema(description = "api 概览")
public class ApiSummaryResponse implements Serializable {

    private static final long serialVersionUID = 4391994442603379373L;

    @Schema(name = "api 总数")
    private long apiCount;
    @Schema(name = "今天新增 api 总数")
    private long newApiCount;
    @Schema(name = "api 活跃总数")
    private long activeApiCount;
    @Schema(name = "api 不活跃总数")
    private long inactiveApiCount;
    @Schema(name = "api 僵尸总数")
    private long zombieApiCount;
}
