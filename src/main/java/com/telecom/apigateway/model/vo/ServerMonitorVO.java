package com.telecom.apigateway.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 服务器监控VO
 */
@Data
public class ServerMonitorVO {
    /**
     * 主机ID
     */
    private String hostId;

    /**
     * 主机名称
     */
    private String hostName;
    
    /**
     * 备注
     */
    private String remarks;
    
    /**
     * CPU总数
     */
    private Integer cpuTotal;
    
    /**
     * 内存总量(字节)
     */
    private Long memoryTotal;
    
    /**
     * CPU使用率(%)
     */
    private Double cpuPercent;
    
    /**
     * 内存使用率(%)
     */
    private Double memoryPercent;
    
    /**
     * 五分钟负载
     */
    private Double load5;
    
    /**
     * 上行网络(KB/s)
     */
    private Double ingressKbps;
    
    /**
     * 下行网络(KB/s)
     */
    private Double egressKbps;
    
    /**
     * 数据采集时间
     */
    private LocalDateTime timestamp;
} 