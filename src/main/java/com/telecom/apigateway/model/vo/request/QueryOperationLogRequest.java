package com.telecom.apigateway.model.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志查询请求
 */
@Data
@Schema(description = "操作日志查询请求")
public class QueryOperationLogRequest implements Serializable {

    @Schema(description = "页码", defaultValue = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页大小", defaultValue = "10")
    private Integer pageSize = 10;

    @Schema(description = "用户名（支持模糊搜索）")
    private String username;

    @Schema(description = "操作类型")
    private List<String> operationType;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime endTime;

    @Schema(description = "IP地址（支持模糊搜索）")
    private String ip;
}
