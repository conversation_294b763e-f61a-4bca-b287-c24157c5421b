package com.telecom.apigateway.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-25
 */
@Data
public class CrsDetectResponseDTO {

    @JsonProperty("matched_data")
    private List<MatchedData> matchedData;

    private List<String[]> collections;

    @JsonProperty("rules_matched_total")
    private String matchedTotalScore;


    private Integer code;
    private String msg;

    @Data
    public static class MatchedData {
        private String message;
        @JsonProperty("rule_id")
        private String ruleId;
        private String severity;
        private String[] tags;
    }
}
