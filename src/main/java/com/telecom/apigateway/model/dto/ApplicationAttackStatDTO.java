package com.telecom.apigateway.model.dto;

import com.telecom.apigateway.model.vo.response.BaseLabelResponse;
import lombok.Data;
import java.util.List;

@Data
public class ApplicationAttackStatDTO {
    // 攻击总次数
    private Long totalAttacks;
    // 拦截总次数
    private Long totalIntercepts;
    // 攻击IP数量
    private Long uniqueAttackerIps;
    // 攻击类型及次数
    private List<BaseLabelResponse> attackTypes;
    // 攻击者IP TOP5
    private List<BaseLabelResponse> topAttackers;

    @Data
    public static class AttackTypeCount {
        private String type;
        private Long count;
    }

    @Data
    public static class AttackerIpCount {
        private String ip;
        private Long count;
    }
}
