package com.telecom.apigateway.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class QueryThreatDTO {
    private String uri;
    private String belongApplication;
    private String ruleId;
    private Boolean dealt;
    private String severity;
    /**
     * 攻击者ip
     */
    private String clientIp;

    /**
     * 攻击者端口
     */
    private Integer clientPort;

    /**
     * 攻击者地区
     */
    private String clientRegion;

    /**
     * 攻击目标地址
     */
    private String targetAddress;

    /**
     * 攻击目标地址
     */
    private String targetRegion;

    /**
     * 攻击时间
     */
    private LocalDateTime attackedTime;

    private String riskLogId;
    private String apiId;
    private Integer score;
    private String logId;
}
