package com.telecom.apigateway.model.dto;

import com.telecom.apigateway.model.enums.RiskLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class ApplicationQueryDTO {
    private Integer pageSize;
    private Integer page;
    private String applicationName;
    private String featureName;
    private String url;
    private String owner;

    public ApplicationQueryDTO(Integer pageSize, Integer page) {
        this.page = page;
        this.pageSize = pageSize;
    }
}
