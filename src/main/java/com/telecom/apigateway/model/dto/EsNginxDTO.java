package com.telecom.apigateway.model.dto;

import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.entity.IpInfo;
import com.telecom.apigateway.model.enums.HttpFieldEnum;
import com.telecom.apigateway.model.enums.LogEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * es 存储索引对象
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@Document(indexName = "#{@indexNameProvider.getIndexName()}")
public class EsNginxDTO implements Serializable {
    private static final long serialVersionUID = -4011418832334001929L;

    /**
     * 日志主键,也是es主键
     */
    @Id
    private String uuid;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constant.DATE_TIME_PATTERN, timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime logTime;
    @Schema(description = "客户端 ip")
    private String clientIp;
    @Schema(description = "客户端端口")
    private String clientPort;
    @Schema(description = "服务端 ip")
    private String serverIp;
    @Schema(description = "服务端 域名")
    private String httpHost;
    @Schema(description = "服务端 域名, ip场景在不包含端口")
    private String domain;
    @Schema(description = "服务端 端口")
    private String serverPort;
    @Schema(description = "协议")
    private String scheme;
    @Schema(description = "协议版本")
    private String protocol;
    @Schema(description = "请求路径")
    private String uri;
    @Schema(description = "请求 url")
    private String url;
    @Schema(description = "请求方法")
    private String httpMethod;
    @Schema(description = "状态码")
    private String statusCode;
    @Schema(description = "userAgent")
    private String userAgent;
    @Schema(description = "后端服务地址")
    private String upstreamAddr;
    @Schema(description = "请求来源")
    private String referer;
    @Schema(description = "请求头")
    private String requestHeader;
    @Schema(description = "请求体")
    private String requestBody;
    /**
     * 解密请求体
     */
    private String decryptedRequestBody;
    @Schema(description = "请求参数")
    private String requestParam;
    @Schema(description = "请求资源类型 api / file")
    private String requestResourceType;
    @Schema(description = "响应头")
    private String responseHeader;
    @Schema(description = "响应体")
    private String responseData;
    @Schema(description = "响应时间")
    private long responseTime;
    @Schema(description = "请求大小")
    private long requestSize;
    @Schema(description = "响应大小")
    private long responseSize;
    @Schema(description = "应用 id")
    private String appId;
    @Schema(description = "api id")
    private String apiId;

    @Schema(description = "waf拦截状态 PASS/ ALLOW(白名单)/ REJECT(黑名单) / ERROR")
    private String wafDetectStatus;
    @Schema(description = "waf 触发的规则, 对应 blocklist 表的 block_id, 默认值 UNKNOWN")
    private String wafDetectId;

    @Schema(description = "威胁拦截状态 PASS / REJECT / ERROR")
    private String crsDetectStatus;

    @Schema(description = "异常行为触发规则")
    private String abnormalBehaviorRuleTriggerId;
    @Schema(description = "异常行为拦截状态 PASS / REJECT / ERROR")
    private String abnormalBehaviorDetectStatus;

    @Schema(description = "涉敏规则")
    @Field(type = FieldType.Nested)
    private List<EsSensitiveRuleDTO> sensitiveRules;

    @Schema(description = "触发的威胁分析规则")
    @Field(type = FieldType.Nested)
    private List<EsRiskRuleDTO> riskRules;

    @Schema(description = "触发的威胁拒绝规则")
    @Field(type = FieldType.Nested)
    private List<EsRiskRuleDTO> rejectRiskRules;

    @Schema(description = "客户端 ip 信息")
    @Field(type = FieldType.Nested)
    private IpInfo clientIpInfo;

    @Schema(description = "服务端 ip 信息, 暂无")
    @Field(type = FieldType.Nested)
    private IpInfo serverIpInfo;

    @Schema(description = "设备类型")
    private String device;

    @Schema(description = "威胁是否已处理")
    private Boolean isDealt;
    /**
     * 误报信息
     */
    @Field(type = FieldType.Nested)
    private FalsePositiveDTO falsePositive;


    public boolean hasResponse() {
        return StrUtil.isNotBlank(responseData) && !"UNKNOWN".equalsIgnoreCase(responseData);
    }

    public String toReconizedString() {
        return httpMethod + ":" + httpHost + uri;
    }

    public String get(HttpFieldEnum field) {
        switch (field) {
            case REQUEST_HEADER:
                return requestHeader;
            case REQUEST_PARAM:
                return requestParam;
            case REQUEST_BODY:
                return requestBody;
            case RESPONSE_BODY:
                return responseData;
            default:
                return null;
        }
    }

    public String getProtocol() {
        if (StrUtil.isBlank(this.protocol)) {
            return "HTTP/1.1";
        }
        return protocol;
    }

    public LocalDate getDate() {
        return logTime.toLocalDate();
    }

    public String toHttpRequest() {
        StringBuilder rawString = new StringBuilder();

        // Request line
        rawString.append(httpMethod).append(" ").append(uri);
        if (StrUtil.isNotBlank(requestParam) && !"{}".equals(requestParam) && !"UNKNOWN".equals(requestParam)) {
            // json 格式的url 参数不为空, 转换为 url 格式拼接
            rawString.append("?");
            rawString.append(URLUtil.encode(UrlQuery.of(JSONUtil.parseObj(requestParam)).toString()));
        }
        rawString.append(" ").append(protocol).append("\r\n");

        // // Request headers
        if (StrUtil.isNotBlank(requestHeader) && !"UNKNOWN".equals(requestHeader)) {
            JSONUtil.parseObj(requestHeader).forEach((key, value) -> rawString.append(key).append(": ").append(value).append("\r\n"));
        }
        // Empty line to separate headers from body
        rawString.append("\r\n");

        // Request body
        if (StrUtil.isNotBlank(requestBody)) {
            String prettyBody;
            try {
                JSONObject jsonObj = JSONUtil.parseObj(requestBody);
                prettyBody = JSONUtil.toJsonPrettyStr(jsonObj);
            } catch (Exception e) {
                prettyBody = requestBody;
            }
            rawString.append(prettyBody).append("\r\n");
        }
        return rawString.toString();
    }

    public String toHttpResponse() {
        StringBuilder rawString = new StringBuilder();
        // Response status line
        HttpStatus httpStatus = HttpStatus.resolve(Integer.parseInt(statusCode));
        if (httpStatus == null) {
            httpStatus = HttpStatus.OK;
        }
        rawString.append(protocol).append(" ").append(httpStatus.value()).append(" ").append(httpStatus.name()).append("\r\n");

        // Response headers
        if (StrUtil.isNotBlank(responseHeader) && !"UNKNOWN".equals(responseHeader)) {
            JSONUtil.parseObj(responseHeader).forEach((key, value) -> rawString.append(key).append(": ").append(value).append("\r\n"));
        }

        // Empty line to separate headers from body
        rawString.append("\r\n");

        // Response body
        if (StrUtil.isNotBlank(responseData)) {
            String prettyBody;
            try {
                JSONObject jsonObj = JSONUtil.parseObj(responseData);
                prettyBody = JSONUtil.toJsonPrettyStr(jsonObj);
            } catch (Exception e) {
                prettyBody = responseData;
            }
            rawString.append(prettyBody).append("\r\n");
        }

        return rawString.toString();
    }


    public String toHttpRawString() {
        StringBuilder rawString = new StringBuilder();

        String request = toHttpRequest();
        String response = toHttpResponse();

        rawString.append(request)
                .append("\r\n\r\n")
                .append(response);

        return rawString.toString();
    }

    /**
     * 白名单 - 黑名单 - 威胁拦截 - 异常拦截 - 威胁仅观察 - 异常告警
     * 那除了异常行为仅观察 和 威胁仅观察 会同时显示，其他的都只显示一个吧
     * 威胁仅观察和异常已拦截能不能也显示2个，然后其他的优先级高的检测先拦截了（白名单>黑名单>威胁>异常），后面的检测仅观察就不显示
     */
    @JsonIgnore
    public List<LogEnum.RequestState> getReqState() {
        if (LogEnum.CrsDetectStatus.PASS.name().equals(crsDetectStatus) &&
                LogEnum.WafDetectStatus.PASS.name().equals(wafDetectStatus) &&
                LogEnum.AbnormalBehaviorDetectStatus.PASS.name().equals(abnormalBehaviorDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.NORMAL);
        }

        if (LogEnum.WafDetectStatus.ALLOW.name().equals(wafDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.BLOCKLIST_ALLOW);
        }
        if (LogEnum.WafDetectStatus.REJECT.name().equals(wafDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.BLOCKLIST_REJECT);
        }
        if (LogEnum.CrsDetectStatus.REJECT.name().equals(crsDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.RISK_REJECT);
        }

        List<LogEnum.RequestState> result = new ArrayList<>();
        if (LogEnum.AbnormalBehaviorDetectStatus.REJECT.name().equalsIgnoreCase(abnormalBehaviorDetectStatus)) {
            result.add(LogEnum.RequestState.ABRT_REJECT);
        }

        if (LogEnum.CrsDetectStatus.LOG.name().equals(crsDetectStatus)) {
            result.add(LogEnum.RequestState.RISK_LOG);
        }

        if (LogEnum.AbnormalBehaviorDetectStatus.LOG.name().equalsIgnoreCase(abnormalBehaviorDetectStatus)) {
            result.add(LogEnum.RequestState.ABRT_LOG);
        }
        if (result.isEmpty()) {
            return Collections.singletonList(LogEnum.RequestState.ERROR);
        } else {
            return result;
        }
    }


    public boolean hasValidRequestHeader() {
        return isValidValue(requestHeader);
    }

    public static boolean isValidValue(String val) {
        if (StrUtil.isBlank(val)) return false;
        if (Constant.UNKNOWN_FIELD.equalsIgnoreCase(val)) return false;
        if ("{}".equals(val)) return false;
        if ("[]".equals(val)) return false;
        return true;
    }

}
