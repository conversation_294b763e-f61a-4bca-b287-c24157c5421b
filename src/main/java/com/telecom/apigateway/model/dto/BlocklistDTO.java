package com.telecom.apigateway.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BlocklistDTO {
    private String blockId;
    private String name;
    private String type;
    private String condition;
    private String status;
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    private String updater;

    @Schema(description = "优先级")
    private Integer priority;
    @Schema(description = "触发次数")
    private Long triggerCount;
}
