package com.telecom.apigateway.model.dto;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-23
 */
@Builder
@Data
public class LokiQueryDTO {

    private String keyword;
    private List<Element> params;
    private Integer queryCount;
    private LocalDateTime start;
    private LocalDateTime end;


    public LokiQueryDTO addQuery(String label, String value) {
        if (StrUtil.isBlank(value)) {
            return this;
        }
        if (this.params == null) {
            this.params = new ArrayList<>();
        }
        this.params.add(new Element(label, value));
        return this;
    }

    public LokiQueryDTO addFuzzyQuery(String label, String value) {
        if (this.params == null) {
            this.params = new ArrayList<>();
        }
        if (StrUtil.isBlank(value)) {
            return this;
        }
        // 避免重复处理或者单边模糊查询
        if (!value.startsWith(".*") && !value.endsWith(".*")) {
            value = ".*" + value + ".*";
        }
        this.params.add(new Element(label, value).toReg());
        return this;
    }

    public LokiQueryDTO addMultipleQuery(String label, Integer[] values) {
        if (this.params == null) {
            this.params = new ArrayList<>();
        }
        if (values == null || values.length == 0) {
            return this;
        }
        this.params.add(new Element(label, values).toReg());
        return this;
    }

    @Data
    public static class Element {
        private String label;
        private String value;
        /**
         * 是否模糊匹配
         */
        private boolean regQuery;

        public Element(String label, String value, boolean regQuery) {
            this.label = label;
            this.value = value;
            this.regQuery = regQuery;
        }

        public Element(String label, String value) {
            this.label = label;
            this.value = value;
            this.regQuery = false;
        }

        public Element(String label, Integer[] values) {
            this.label = label;
            this.value = ArrayUtil.join(values, "|");
            this.regQuery = true;
        }


        public Element toReg() {
            this.regQuery = true;
            return this;
        }
    }


    /**
     * 生成 loki 查询语句
     */
    public String toLokiQueryStr() {
        StringBuilder query = new StringBuilder();
        // format: {job="access", type="access"}
        query.append("{");
        for (Element param : this.params) {
            query.append(param.getLabel());
            query.append(param.isRegQuery() ? "=~" : "=");
            query.append("\"");
            query.append(param.getValue());
            query.append("\"");
            query.append(", ");
        }
        // 减去最后两个 [, ]
        query.delete(query.length() - 2, query.length());
        query.append("}");

        if (StrUtil.isNotBlank(this.keyword)) {
            //  format: |=`"keyword"`
            query.append(" |=");
            query.append("`");
            query.append("\"").append(this.keyword).append("\"");
            query.append("`");
        }
        return query.toString();
    }
}
