package com.telecom.apigateway.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 策略条件DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PolicyConditionDTO {
    
    @Schema(description = "IP/域名列表，支持通配符")
    private String host;
    
    @Schema(description = "端口列表")
    private String port;
} 