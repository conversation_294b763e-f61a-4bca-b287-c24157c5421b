package com.telecom.apigateway.model.dto;

import com.telecom.apigateway.model.enums.MergeTaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 历史数据合并任务DTO
 * 用于Redis存储
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@Schema(description = "历史数据合并任务")
public class HistoryMergeTaskDTO implements Serializable {

    @Schema(description = "任务ID")
    private String id;

    @Schema(description = "任务状态")
    private MergeTaskStatus status;

    @Schema(description = "任务进度描述")
    private String progressDescription;

    @Schema(description = "已处理API数量")
    private Integer processedApiCount;

    @Schema(description = "总API数量")
    private Integer totalApiCount;

    @Schema(description = "已处理应用数量")
    private Integer processedApplicationCount;

    @Schema(description = "总应用数量")
    private Integer totalApplicationCount;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "创建用户")
    private String createUser;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    public HistoryMergeTaskDTO() {
    }

    /**
     * 创建新任务
     */
    public static HistoryMergeTaskDTO createNew(String taskId, String createUser) {
        HistoryMergeTaskDTO task = new HistoryMergeTaskDTO();
        task.id = taskId;
        task.status = MergeTaskStatus.PENDING;
        task.progressDescription = "任务已创建，等待执行";
        task.processedApiCount = 0;
        task.totalApiCount = 0;
        task.processedApplicationCount = 0;
        task.totalApplicationCount = 0;
        task.createUser = createUser;
        LocalDateTime now = LocalDateTime.now();
        task.createTime = now;
        task.updateTime = now;
        return task;
    }

    /**
     * 更新任务状态
     */
    public void updateStatus(MergeTaskStatus status, String progressDescription) {
        this.status = status;
        this.progressDescription = progressDescription;
        this.updateTime = LocalDateTime.now();
        
        if (status == MergeTaskStatus.RUNNING && this.startTime == null) {
            this.startTime = LocalDateTime.now();
        }
        
        if (status == MergeTaskStatus.SUCCESS || status == MergeTaskStatus.FAILED) {
            this.endTime = LocalDateTime.now();
        }
    }

    /**
     * 更新API处理进度
     */
    public void updateApiProgress(int processedCount, int totalCount) {
        this.processedApiCount = processedCount;
        this.totalApiCount = totalCount;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 更新应用处理进度
     */
    public void updateApplicationProgress(int processedCount, int totalCount) {
        this.processedApplicationCount = processedCount;
        this.totalApplicationCount = totalCount;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 设置错误信息
     */
    public void setError(String errorMessage) {
        this.status = MergeTaskStatus.FAILED;
        this.errorMessage = errorMessage;
        this.endTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 计算总体进度百分比
     */
    public double getProgressPercentage() {
        int totalProcessed = (processedApiCount != null ? processedApiCount : 0) + 
                           (processedApplicationCount != null ? processedApplicationCount : 0);
        int totalCount = (totalApiCount != null ? totalApiCount : 0) + 
                        (totalApplicationCount != null ? totalApplicationCount : 0);
        
        if (totalCount == 0) {
            return 0.0;
        }
        
        return (double) totalProcessed / totalCount * 100;
    }

    /**
     * 检查任务是否完成
     */
    public boolean isCompleted() {
        return status == MergeTaskStatus.SUCCESS || status == MergeTaskStatus.FAILED;
    }

    /**
     * 检查任务是否正在运行
     */
    public boolean isRunning() {
        return status == MergeTaskStatus.RUNNING || 
               status == MergeTaskStatus.MERGING_API || 
               status == MergeTaskStatus.MERGING_APPLICATION;
    }
}
