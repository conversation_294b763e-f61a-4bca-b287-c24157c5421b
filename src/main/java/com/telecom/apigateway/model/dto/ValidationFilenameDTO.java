package com.telecom.apigateway.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@AllArgsConstructor
public class ValidationFilenameDTO {
    private String identifier;    // 改名为更通用的identifier
    private String code;
    private String description;
    private Boolean isValid;

    public static ValidationFilenameDTO validateAndParse(String fileName) {
        final String filenamePattern = "^(RULE-\\d+)\\((CVE-\\d+-\\d+)\\)\\[([^]]+)].poc$";
        if (fileName == null || fileName.trim().isEmpty()) {
            return new ValidationFilenameDTO(null, null, null, false);
        }

        Pattern pattern = Pattern.compile(filenamePattern);
        Matcher matcher = pattern.matcher(fileName);

        if (matcher.matches()) {
            String identifier = matcher.group(1).trim();
            String code = matcher.group(2).trim();
            String description = matcher.group(3).trim();

            // 确保两部分都不为空
            if (!identifier.isEmpty() && !code.isEmpty() && !description.isEmpty()) {
                return new ValidationFilenameDTO(identifier, code, description, true);
            }
        }

        return new ValidationFilenameDTO(null, null, null, false);
    }
}
