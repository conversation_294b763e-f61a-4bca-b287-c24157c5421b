package com.telecom.apigateway.model.dto;

import com.telecom.apigateway.model.vo.response.BaseLabelResponse;
import lombok.Data;

import java.util.List;

@Data
public class ApplicationAttackMapDTO {
    // 攻击国家 TOP5
    private List<BaseLabelResponse> attackCountries;
    private List<BaseLabelResponse> attackProvince;
    private List<AttackMap> chinaAttackMaps;
    private List<AttackMap> worldAttackMaps;

    @Data
    public static class AttackMap {
        private String city;
        private String longitude;
        private String latitude;
        private Long count;
        private String serverCity;
        private String serverLongitude;
        private String serverLatitude;
    }
}
