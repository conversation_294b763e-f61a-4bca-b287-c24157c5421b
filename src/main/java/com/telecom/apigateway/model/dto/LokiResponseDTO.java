package com.telecom.apigateway.model.dto;

import cn.hutool.json.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
public class LokiResponseDTO implements Serializable {

    private static final long serialVersionUID = 2866716018193919175L;
    private String status;
    private LokiDataDTO data;


    @Data
    public static class LokiDataDTO implements Serializable {
        private static final long serialVersionUID = -2998673712350923558L;
        private String resultType;
        private LokiResultDTO[] result;
    }

    @Data
    public static class LokiResultDTO implements Serializable {
        private static final long serialVersionUID = -6444771583880092660L;
        private LokiStreamDTO stream;
        private LokiMetricDTO metric;
        private String[][] values;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class LokiStreamDTO extends JSONObject implements Serializable {
        private static final long serialVersionUID = -3447070885026647054L;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class LokiMetricDTO extends JSONObject implements Serializable {
        private static final long serialVersionUID = -3447070885026647054L;
    }
}
