package com.telecom.apigateway.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * excel 格式 api
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Data
public class ApiImportFromFileDTO implements Serializable {
    private static final long serialVersionUID = 8645478407550423122L;

    private String appId;
    private String name;
    private String httpMethod;
    private String uri;
    /**
     * url
     */
    private String url;
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime onlineTime;
    private String remark;

    /**
     * host
     */
    private String host;
    /**
     * 端口
     */
    private Integer port;

    public void format() {
        uri = Optional.ofNullable(uri).orElse("").trim();
        name = Optional.ofNullable(name).orElse(uri).trim();
        remark = Optional.ofNullable(remark).orElse("").trim();
        if (!uri.startsWith("/")) {
            uri = "/" + uri;
        }
    }


    public boolean isPathVariable() {
        return uri.contains("{") && uri.contains("}");
    }
}
