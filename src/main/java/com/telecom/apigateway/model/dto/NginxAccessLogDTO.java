package com.telecom.apigateway.model.dto;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.HttpFieldEnum;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * nginx 访问日志
 *
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
public class NginxAccessLogDTO implements Serializable {
    private static final long serialVersionUID = -4011418832334001929L;

    private String uuid;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constant.DATE_TIME_PATTERN, timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime logTime;
    private String clientIp;
    private String clientPort;
    private String serverIp;
    private String domain;
    private String serverPort;
    private String scheme;
    private String uri;
    private String url;
    private String httpMethod;
    private String protocol;
    private String statusCode;
    private String userAgent;
    private String upstreamAddr;
    private String referer;
    private String requestHeader;
    private String requestBody;
    private String requestParam;
    private String requestResourceType;
    private String responseHeader;
    private long responseTime;
    private long requestSize;
    private long responseSize;
    private String responseData;

    private String appId;
    private String appName;

    private String apiId;
    private Integer sensitiveLevel;


    public boolean hasResponse() {
        return StrUtil.isNotBlank(responseData);
    }

    public String get(HttpFieldEnum field) {
        switch (field) {
            case REQUEST_HEADER:
                return requestHeader;
            case REQUEST_PARAM:
                return requestParam;
            case REQUEST_BODY:
                return requestBody;
            case RESPONSE_BODY:
                return responseData;
            default:
                return null;
        }
    }

    public String getProtocol() {
        if (StrUtil.isBlank(this.protocol)) {
            return "HTTP/1.1";
        }
        return protocol;
    }

    public LocalDate getDate() {
        return logTime.toLocalDate();
    }

    public String toHttpRawString() {
        StringBuilder rawString = new StringBuilder();

        // Request line
        rawString.append(httpMethod).append(" ").append(uri).append(" ").append(protocol).append("\r\n");

        // // Request headers
        if (StrUtil.isNotBlank(requestHeader)) {
            JSONUtil.parseObj(requestHeader).forEach((key, value) -> rawString.append(key).append(": ").append(value).append("\r\n"));
        }
        // Empty line to separate headers from body
        rawString.append("\r\n");

        // Request body
        if (StrUtil.isNotBlank(requestBody)) {
            rawString.append(requestBody).append("\r\n");
        }

        // Response status line
        rawString.append(protocol).append(" ").append(statusCode).append("\r\n");

        // Response headers
        if (StrUtil.isNotBlank(responseHeader)) {
            JSONUtil.parseObj(responseHeader).forEach((key, value) -> rawString.append(key).append(": ").append(value).append("\r\n"));
        }

        // Empty line to separate headers from body
        rawString.append("\r\n");

        // Response body
        if (StrUtil.isNotBlank(responseData)) {
            rawString.append(responseData).append("\r\n");
        }

        return rawString.toString();
    }
}
