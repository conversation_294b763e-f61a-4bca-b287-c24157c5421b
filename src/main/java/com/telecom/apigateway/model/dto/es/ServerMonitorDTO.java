package com.telecom.apigateway.model.dto.es;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@Data
public class ServerMonitorDTO {
    @Id
    private String id;
    /**
     * 时间戳（毫秒）
     */
    @Field(name = "@timestamp")
    private String timestamp;

    @Field(type = FieldType.Nested)
    private Host host;

    @Field(type = FieldType.Nested)
    private CPU cpu;

    @Field(type = FieldType.Nested)
    private Memory memory;

    @Field(type = FieldType.Nested)
    private Load load;

    @Field(type = FieldType.Nested)
    private Network network;


    @Data
    public static class Host {
        private String hostname;

        @Field(type = FieldType.Keyword)
        private String hostId;
    }

    @Data
    public static class CPU {
        private Integer counts;
        private Double percent;
    }

    @Data
    public static class Memory {
        private Long total;
        private Long available;
        private Long used;
        private Double usedPercent;
    }

    @Data
    public static class Load {
        private Double load1;
        private Double load5;
        private Double load15;
    }

    @Data
    public static class Network {
        private NetworkGress ingress;
        private NetworkGress egress;
    }

    @Data
    public static class NetworkGress {
        private Long bytes;
        private Long packets;
    }
}
