package com.telecom.apigateway.model.dto;

import com.telecom.apigateway.common.Constant;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
public class GenerateQueryDTO {
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime startTime;
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime endTime;
    private List<String> appId;
}
