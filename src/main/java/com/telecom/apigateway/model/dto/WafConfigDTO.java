package com.telecom.apigateway.model.dto;

import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-12-08
 */
@Data
public class WafConfigDTO implements Serializable {
    private static final long serialVersionUID = 7244053098508273989L;
    /**
     * id=[仅当mod=realIpFrom_Mod/denyMsg时使用]
     */
    private String id;
    /**
     * 自定义的id
     */
    private String blockId;
    /**
     * `
     * on/off
     */
    private String state;
    /**
     * 执行动作：拒绝（allow/deny/log）
     */
    private String[] action;
    /**
     * host匹配规则；第一个参数：匹配内容，第二个：匹配方式，字符串等于
     */
    private String[] hostname;
    /**
     * uri 匹配规则；第一个参数：匹配内容，第二个：匹配方式，字符串等于
     */
    private String[] uri;
    /**
     * 优先级
     */
    private Integer priority;
    @Schema(description = "更新时间,单位秒")
    private Long timestamp;

    @Schema(description = "生成配置时间,单位秒")
    private Long updateTime;
    /**
     * 匹配规则
     * <pre>
     *   [
     *     [
     *       "posts_all",
     *       [
     *         "爸爸是我",
     *         "in",
     *         false
     *       ],
     *       "and"
     *     ],
     *     [
     *       "posts_all",
     *       [
     *         ">>>+",
     *         "jio",
     *         false
     *       ],
     *       "and"
     *     ]
     *   ]
     * </pre>>
     */
    private Object[][] app_ext;

    private Long expireTime = -1L;


    public String toJsonStr() {
        return JSONUtil.toJsonStr(this);
    }

    public static WafConfigDTO standardConfig(String blockId, String action, int priority, long timestamp) {
        WafConfigDTO wafConfigDTO = new WafConfigDTO();
        wafConfigDTO.setId(blockId);
        wafConfigDTO.setBlockId(blockId);
        wafConfigDTO.setState("off"); // 默认不启用
        wafConfigDTO.setAction(new String[]{action});
        wafConfigDTO.setHostname(new String[]{"*", ""});
        wafConfigDTO.setUri(new String[]{"*", ""});
        wafConfigDTO.setPriority(priority);
        wafConfigDTO.setTimestamp(timestamp);
        return wafConfigDTO;
    }
}
