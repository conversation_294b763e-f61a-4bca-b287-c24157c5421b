package com.telecom.apigateway.model.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.InnerHitBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-03
 */
@Builder
@Data
public class EsQueryDTO {
    /**
     * 查询参数,字段参考====> {@link EsNginxDTO}
     */
    private List<Element> params;
    /**
     * or / should 查询, 只支持一个 a1 and (a2 or b2 or c2), 其他再复杂的查询, 可以考虑手动
     */
    private List<List<Element>> shouldParams;
    /**
     * 排序参数
     */
    private List<SortElement> sorts;
    /**
     * 查询数量
     */
    private Integer queryCount;
    /**
     * 查询时间范围
     */
    private LocalDateTime start;
    /**
     * 查询时间范围
     */
    private LocalDateTime end;
    /**
     * 分页参数
     */
    private Integer pageNum;
    /**
     * 分页参数
     */
    private Integer pageSize;
    /**
     * 查询字段
     */
    private List<String> queryFields;


    /**
     * 嵌套字段
     */
    public static final List<String> NESTED_FIELDS = Collections.unmodifiableList(
            Arrays.asList("sensitiveRules", "riskRules", "clientIpInfo", "rejectRiskRules", "falsePositive")
    );

    /**
     * 添加查询参数, 添加为 等值 查询
     */
    public EsQueryDTO addQuery(String label, Object value) {
        if (Objects.isNull(value)) {
            return this;
        }
        initParams();
        if (isNestedField(label)) {
            this.params.add(new Element(label, value, QueryType.NESTED));
        } else {
            this.params.add(new Element(label, value));
        }
        return this;
    }


    /**
     * add should query
     */
    public EsQueryDTO addShouldQuery(List<Element> elements) {
        if (CollUtil.isEmpty(elements)) {
            return this;
        }
        if (this.shouldParams == null) {
            this.shouldParams = new ArrayList<>();
        }
        this.shouldParams.add(elements);
        return this;
    }

    /**
     * 添加查询参数, 添加为 等值 查询
     */
    public EsQueryDTO addQuery(boolean condition, String label, Object value) {
        if (!condition) {
            return this;
        }
        return addQuery(label, value);
    }

    /**
     * 添加模糊查询
     */
    public EsQueryDTO addFuzzyQuery(String label, String value) {
        initParams();
        if (StrUtil.isBlank(value)) {
            return this;
        }
        // 避免重复处理或者单边模糊查询
        if (!value.startsWith("*") && !value.endsWith("*")) {
            value = "*" + value + "*";
        }
        if (isNestedField(label)) {
            this.params.add(new Element(label, value, QueryType.NESTED_LIKE));
        } else {
            this.params.add(new Element(label, value, QueryType.LIKE));
        }
        return this;
    }

    /**
     * 添加模糊查询
     */
    public EsQueryDTO addFuzzyQuery(boolean condition, String label, String value) {
        if (!condition) {
            return this;
        }
        return addFuzzyQuery(label, value);
    }


    /**
     * 添加模糊查询
     */
    public EsQueryDTO addMultiFuzzyQuery(String label, Collection<String> values) {
        initParams();
        if (CollUtil.isEmpty(values)) {
            return this;
        }
        values = values.stream()
                .map(s -> (!s.startsWith("*") && !s.endsWith("*")) ? "*" + s + "*" : s)
                .collect(Collectors.toList());

        if (isNestedField(label)) {
            this.params.add(new Element(label, values, QueryType.NESTED_LIKE_LIST));
        } else {
            this.params.add(new Element(label, values, QueryType.LIKE_LIST));
        }
        return this;
    }

    /**
     * 添加模糊查询
     */
    public EsQueryDTO addMultiFuzzyQuery(boolean condition, String label, Collection<String> values) {
        if (!condition) {
            return this;
        }
        return addMultiFuzzyQuery(label, values);
    }

    /**
     * 添加 in 查询
     */
    public EsQueryDTO addMultipleQuery(String label, Collection<?> values) {
        initParams();
        if (values == null || values.isEmpty()) {
            return this;
        }
        if (isNestedField(label)) {
            this.params.add(new Element(label, values, QueryType.NESTED_LIST));
        } else {
            this.params.add(new Element(label, values, QueryType.LIST));
        }
        return this;
    }

    /**
     * 添加 in 查询
     */
    public EsQueryDTO addMultipleQuery(boolean condition, String label, Collection<?> values) {
        if (!condition) {
            return this;
        }
        return addMultipleQuery(label, values);
    }

    /**
     * 添加 嵌套查询
     */
    public EsQueryDTO addNestedQuery(boolean condition, String label, Collection<?> values) {
        if (!condition) {
            return this;
        }
        initParams();
        if (values == null || values.isEmpty()) {
            return this;
        }
        this.params.add(new Element(label, values, QueryType.NESTED_LIST));
        return this;
    }

    /**
     * 添加嵌套字段的查询
     *
     * @param condition  条件
     * @param nestedPath 嵌套对象的路径，例如"riskRules"
     * @param fieldName  完整的字段名，例如"riskRules.type"
     * @param values     查询的值集合
     */
    public EsQueryDTO addNestedFieldQuery(boolean condition, String nestedPath, String fieldName,
                                          Collection<?> values) {
        if (!condition) {
            return this;
        }
        initParams();
        if (values == null || values.isEmpty()) {
            return this;
        }
        QueryBuilder nestedQuery = QueryBuilders.nestedQuery(
                nestedPath,
                QueryBuilders.termsQuery(fieldName, values),
                org.apache.lucene.search.join.ScoreMode.None
        );
        this.params.add(new Element("custom", nestedQuery, QueryType.CUSTOM));
        return this;
    }

    /**
     * 添加存在查询
     */
    public EsQueryDTO addExistQuery(String field) {
        if (StrUtil.isBlank(field)) {
            return this;
        }
        initParams();
        if (isNestedField(field)) {
            this.params.add(new Element(field, "", QueryType.NESTED_EXIST));
        } else {
            this.params.add(new Element(field, "", QueryType.EXIST));
        }
        return this;
    }

    /**
     * 添加不存在查询
     */
    public EsQueryDTO addNotExistQuery(String field) {
        if (StrUtil.isBlank(field)) {
            return this;
        }
        initParams();
        if (isNestedField(field)) {
            this.params.add(new Element(field, "", QueryType.NESTED_NOT_EXIST));
        } else {
            this.params.add(new Element(field, "", QueryType.NOT_EXIST));
        }
        return this;
    }

    public EsQueryDTO addRangeQuery(String field, Integer start, Integer end) {
        initParams();
        if (start == null || end == null) {
            return this;
        }
        this.params.add(new Element(field, start, end, QueryType.NESTED_LIST));
        return this;
    }

    public EsQueryDTO addGtQuery(String field, Integer start, Integer end) {
        initParams();
        if (start == null || end == null) {
            return this;
        }
        this.params.add(new Element(field, start, QueryType.GREATER_THAN));
        return this;
    }

    /**
     * 添加自定义原生查询
     *
     * @param queryBuilder Elasticsearch原生查询构建器
     * @return this
     */
    public EsQueryDTO addCustomQuery(QueryBuilder queryBuilder) {
        if (queryBuilder == null) {
            return this;
        }
        initParams();
        this.params.add(new Element("custom", queryBuilder, QueryType.CUSTOM));
        return this;
    }

    public EsQueryDTO addCustomQuery(boolean condition, QueryBuilder queryBuilder) {
        if (!condition) {
            return this;
        }
        if (queryBuilder == null) {
            return this;
        }
        initParams();
        this.params.add(new Element("custom", queryBuilder, QueryType.CUSTOM));
        return this;
    }

    /**
     * 添加inner hits查询
     *
     * @param path 嵌套对象的路径
     * @param size inner hits返回的大小
     */
    public EsQueryDTO addInnerHits(String path, int size) {
        initParams();
        QueryBuilder query = QueryBuilders.nestedQuery(
                path,
                QueryBuilders.matchAllQuery(),
                org.apache.lucene.search.join.ScoreMode.None
        ).innerHit(new InnerHitBuilder().setSize(size));
        this.params.add(new Element("custom", query, QueryType.CUSTOM));
        return this;
    }

    /**
     * 添加 排序
     *
     * @param field     字段名
     * @param sortOrder 排序方式
     */
    public EsQueryDTO orderBy(String field, SortOrder sortOrder) {
        if (this.sorts == null) {
            this.sorts = new ArrayList<>();
        }
        sorts.add(new SortElement(field, sortOrder));
        return this;
    }

    /**
     * 查询参数实体
     */
    @Data
    public static class Element {
        private String label;
        private Object value;
        private Object value2;
        /**
         * 查询方式, 区分嵌套与否
         */
        private QueryType queryType;


        public Element(String label, Object value) {
            this.label = label;
            this.value = value;
            if (isNestedField(label)) {
                this.queryType = QueryType.NESTED;
            } else {
                this.queryType = QueryType.EQUAL;
            }
        }

        public Element(String label, Object value, QueryType queryType) {
            this.label = label;
            this.value = value;
            this.queryType = queryType;
        }

        public Element(String label, Object value1, Object value2, QueryType queryType) {
            this.label = label;
            this.value = value1;
            this.value2 = value2;
            this.queryType = queryType;
        }

        public QueryBuilder toQueryBuilder() {
            switch (queryType) {
                case LIST:
                    // 列表查询
                    return QueryBuilders.termsQuery(label, (Collection<?>) getValue());
                case EQUAL:
                    // 等值查询
                    return QueryBuilders.termQuery(label, value);
                case LIKE:
                    // 模糊查询
                    return QueryBuilders.wildcardQuery(label, (String) value);
                case LIKE_LIST:
                    Collection<String> keywords = (Collection<String>) value;
                    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                    for (String keyword : keywords) {
                        boolQuery.filter(QueryBuilders.wildcardQuery(label, keyword));
                    }
                    // boolQuery.minimumShouldMatch(1);
                    return boolQuery;
                case EXIST:
                    // 存在 不为空
                    return QueryBuilders.existsQuery(label);
                case NOT_EXIST:
                    // 不存在
                    return QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery(label));
                case RANGE:
                    // 范围查询
                    return QueryBuilders.rangeQuery(label).gte(value).lte(value2);
                case GREATER_THAN:
                    // 大于查询
                    return QueryBuilders.rangeQuery(label).gt(value);
                case CUSTOM:
                    // 自定义原生查询
                    return (QueryBuilder) value;
                case NESTED:
                    // 嵌套等值查询
                    return QueryBuilders.nestedQuery(
                            label.split("\\.")[0],
                            QueryBuilders.termQuery(label, value),
                            ScoreMode.None);
                case NESTED_LIKE:
                    // 嵌套模糊查询
                    return QueryBuilders.nestedQuery(
                            label.split("\\.")[0],
                            QueryBuilders.wildcardQuery(label, (String) value),
                            ScoreMode.None);
                case NESTED_EXIST:
                    // 嵌套存在查询
                    return QueryBuilders.nestedQuery(
                            label.split("\\.")[0],
                            QueryBuilders.existsQuery(label),
                            ScoreMode.None);
                case NESTED_NOT_EXIST:
                    return QueryBuilders.boolQuery().mustNot(
                            QueryBuilders.nestedQuery(
                                    label.split("\\.")[0],
                                    QueryBuilders.existsQuery(label),
                                    ScoreMode.None)
                    );
                case NESTED_LIKE_LIST:
                    // 嵌套模糊列表查询
                    Collection<String> keywords1 = (Collection<String>) value;
                    BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
                    for (String keyword : keywords1) {
                        boolQuery1.filter(QueryBuilders.wildcardQuery(label, keyword));
                    }
                    // boolQuery1.minimumShouldMatch(1);
                    return QueryBuilders.nestedQuery(
                            label.split("\\.")[0],
                            boolQuery1,
                            ScoreMode.None);
                case NESTED_LIST:
                    // 嵌套列表查询
                    return QueryBuilders.nestedQuery(
                            label.split("\\.")[0],
                            QueryBuilders.termsQuery(label, (Collection<?>) value),
                            ScoreMode.None);
            }
            return null;
        }
    }

    /**
     * 排序参数实体
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SortElement {
        private String label;
        private SortOrder sortOrder;
    }


    public boolean queryRange() {
        return start != null && end != null;
    }

    /**
     * 查询方式
     */
    public enum QueryType {
        /**
         * 默认查询,值等于
         */
        EQUAL,
        /**
         * 模糊查询
         */
        LIKE,
        /**
         * 模糊查询 list
         */
        LIKE_LIST,
        /**
         * 列表查询,in 查询
         */
        LIST,
        /**
         * 嵌套列表查询
         */
        NESTED_LIST,
        NESTED_LIKE_LIST,
        /**
         * 嵌套查询
         */
        NESTED,
        /**
         * 嵌套模糊查询
         */
        NESTED_LIKE,
        /**
         * 存在查询, 不为空, != null
         */
        EXIST,
        /**
         * 嵌套存在查询, 不为空, != null
         */
        NESTED_EXIST,
        /**
         * 不存在查询, 为空, == null
         */
        NOT_EXIST,
        /**
         * 嵌套不存在查询, 为空, == null
         */
        NESTED_NOT_EXIST,

        /**
         * 范围查询
         */
        RANGE,

        /**
         * 大于查询
         */
        GREATER_THAN,

        /**
         * 自定义原生查询
         */
        CUSTOM
    }

    /**
     * 转换为 elasticsearch 查询对象
     */
    public NativeSearchQuery toNativeSearchQuery() {
        // 组合查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (this.queryRange()) {
            // 时间范围查询
            RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("logTime")
                    .gte(this.getStart().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                    .lt(this.getEnd().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            boolQueryBuilder.filter(timeRangeQuery);
        }
        if (this.params != null) {
            for (EsQueryDTO.Element param : this.getParams()) {
                Optional.ofNullable(param.toQueryBuilder()).ifPresent(boolQueryBuilder::filter);
            }
        }

        if (shouldParams != null) {
            for (List<Element> shouldParam : shouldParams) {
                BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                shouldQuery.minimumShouldMatch(1);
                for (EsQueryDTO.Element param : shouldParam) {
                    Optional.ofNullable(param.toQueryBuilder()).ifPresent(shouldQuery::should);
                }
                boolQueryBuilder.filter(shouldQuery);
            }
        }

        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(boolQueryBuilder);
        if (CollUtil.isNotEmpty(sorts)) {
            for (SortElement sort : sorts) {
                // 排序
                nativeSearchQueryBuilder.withSorts(SortBuilders.fieldSort(sort.getLabel()).order(sort.getSortOrder()));
            }

        }
        NativeSearchQuery query = nativeSearchQueryBuilder.build();
        if (CollUtil.isNotEmpty(queryFields)) {
            query.addSourceFilter(new FetchSourceFilter(queryFields.toArray(new String[0]), null)); // 第二个参数是 exclude 字段
        }

        // 分页
        if (this.pageNum != null && this.pageSize != null) {
            query.setPageable(PageRequest.of(this.pageNum - 1, this.pageSize));
        }
        // 查询数量限制
        Optional.ofNullable(queryCount).ifPresent(query::setMaxResults);
        return query;
    }

    private void initParams() {
        if (this.params == null) {
            this.params = new ArrayList<>();
        }
    }

    private static boolean isNestedField(String field) {
        return NESTED_FIELDS.stream().anyMatch(field::startsWith);
    }

    public void removeQuery(String label) {
        if (this.params != null) {
            this.params.removeIf(param -> param.getLabel().equals(label));
        }
    }
}
