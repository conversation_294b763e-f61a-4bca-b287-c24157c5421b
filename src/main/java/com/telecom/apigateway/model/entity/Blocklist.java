package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("blocklists")
public class Blocklist implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String blockId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "状态（例如：Active，Inactive")
    private String status;

    @Schema(description = "类型（例如：Black，White")
    private String type;

    @Schema(description = "条件")
    private String condition;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String updater;

    @TableField(value = "is_deleted")
    private Boolean deleted;

    @Schema(description = "优先级")
    private Integer priority;
}
