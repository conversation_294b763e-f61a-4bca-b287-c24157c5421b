package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.utils.IpUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IpInfo {
    @TableId(type = IdType.INPUT)
    private String ip;
    private String person;
    private String email;
    private String phone;
    private String isp;
    private String country;
    private String province;
    private String city;

    @Field(type = FieldType.Nested)
    @TableField(exist = false)
    private Location location;

    public IpInfo(String ip) {
        this.ip = ip;
    }

    @Data
    public static class Location {
        private float lon;
        private float lat;
    }

    public String getTranslatedCity() {
        return IpUtils.getTranslatedRegion(city);
    }

    public String getTranslatedProvince() {
        return IpUtils.getTranslatedRegion(province);
    }

    public String getTranslatedCountry() {
        return IpUtils.getTranslatedRegion(country);
    }

    public String getTranslatedIsp() {
        return IpUtils.getTranslatedRegion(isp);
    }

    public String getAddr() {
        String addr = getTranslatedCountry();
        List<String> list = Arrays.asList(
                Constant.INNER_REGION,
                Constant.UNKNOWN_REGION,
                Constant.UNKNOWN_REGION_CODE,
                Constant.INNER_REGION_CODE);
        if (!list.contains(province)) {
            addr += "-" + province;
        }
        if (!list.contains(city)) {
            addr += "-" + city;
        }
        return addr;
    }
}
