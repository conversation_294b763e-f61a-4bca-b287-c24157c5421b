package com.telecom.apigateway.model.entity;

import com.telecom.apigateway.model.enums.ApiMergeEnum;

import java.io.Serializable;


public class ApiMergeCondition implements Serializable {
    private ApiMergeEnum.Target target;
    private ApiMergeEnum.Operation operation;
    private String value;

    public ApiMergeEnum.Target getTarget() {
        return target;
    }

    public void setTarget(ApiMergeEnum.Target target) {
        this.target = target;
    }

    public ApiMergeEnum.Operation getOperation() {
        return operation;
    }

    public void setOperation(ApiMergeEnum.Operation operation) {
        this.operation = operation;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
