package com.telecom.apigateway.model.entity;

import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AbnormalBehaviorRuleCondition {
    /**
     * 目标
     */
    private AbnormalBehaviorRuleEnum.Target target;
    private AbnormalBehaviorRuleEnum.Operation operation;
    private String value;
    private String headerName;

    public void format() {
        if (headerName != null) {
            headerName = headerName.trim();
        }
        if (value != null) {
            value = value.trim();
        }
    }
}
