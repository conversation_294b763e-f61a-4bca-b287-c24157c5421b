package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.JsonbTypeHandler;
import com.telecom.apigateway.model.dto.NginxAccessLogDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 涉敏日志
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
@Data
@TableName(value = "sensitive_log", autoResultMap = true)
public class SensitiveLog {

    @Schema(description = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    @Schema(description = "日志id")
    private String logId;
    @Schema(description = "api的id")
    private String apiId;
    private String clientIp;
    @Schema(description = "涉敏规则id")
    private String sensitiveRuleId;
    @Schema(description = "涉敏信息")
    private String sensitiveContent;
    @Schema(description = "字段")
    private String field;
    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;
    @Schema(description = "日志时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime logTime;

    @Schema(description = "是否删除")
    @TableField(value = "is_deleted")
    private Boolean deleted;
    @Schema(description = "是否处理")
    @TableField(value = "is_dealt")
    private Boolean dealt;

    @TableField(typeHandler = JsonbTypeHandler.class)
    private NginxAccessLogDTO log;
}
