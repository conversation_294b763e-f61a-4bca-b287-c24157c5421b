package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.LogEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Data
@TableName
public class SensitiveLog {
    /**
     * 日志主键,也是es主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    private String logId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constant.DATE_TIME_PATTERN, timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime logTime;
    private String clientIp;
    private String httpMethod;
    private String scheme;
    private String uri;
    private String url;
    private String statusCode;
    private String userAgent;
    private String requestHeader;
    private String requestBody;
    /**
     * 解密请求体
     */
    private String decryptedRequestBody;
    private String requestParam;
    private String responseHeader;
    private String responseData;

    private String apiId;
    private String appId;

    /**
     * 客户端信息
     */
    private String clientCountry;
    private String clientProvince;
    private String clientCity;

    private String ruleId;
    private String content;
    private String httpField;

    @Schema(description = "waf拦截状态 PASS/ ALLOW(白名单)/ REJECT(黑名单) / ERROR")
    private String wafDetectStatus;
    @Schema(description = "威胁拦截状态 PASS / REJECT / ERROR")
    private String crsDetectStatus;
    @Schema(description = "异常行为拦截状态 PASS / REJECT / ERROR")
    private String abnormalBehaviorDetectStatus;

    private LocalDateTime createTime;

    @JsonIgnore
    public List<LogEnum.RequestState> getReqState() {
        if (LogEnum.CrsDetectStatus.PASS.name().equals(crsDetectStatus) &&
                LogEnum.WafDetectStatus.PASS.name().equals(wafDetectStatus) &&
                LogEnum.AbnormalBehaviorDetectStatus.PASS.name().equals(abnormalBehaviorDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.NORMAL);
        }

        if (LogEnum.WafDetectStatus.ALLOW.name().equals(wafDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.BLOCKLIST_ALLOW);
        }
        if (LogEnum.WafDetectStatus.REJECT.name().equals(wafDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.BLOCKLIST_REJECT);
        }
        if (LogEnum.CrsDetectStatus.REJECT.name().equals(crsDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.RISK_REJECT);
        }

        List<LogEnum.RequestState> result = new ArrayList<>();
        if (LogEnum.AbnormalBehaviorDetectStatus.REJECT.name().equalsIgnoreCase(abnormalBehaviorDetectStatus)) {
            result.add(LogEnum.RequestState.ABRT_REJECT);
        }

        if (LogEnum.CrsDetectStatus.LOG.name().equals(crsDetectStatus)) {
            result.add(LogEnum.RequestState.RISK_LOG);
        }

        if (LogEnum.AbnormalBehaviorDetectStatus.LOG.name().equalsIgnoreCase(abnormalBehaviorDetectStatus)) {
            result.add(LogEnum.RequestState.ABRT_LOG);
        }
        if (result.isEmpty()) {
            return Collections.singletonList(LogEnum.RequestState.ERROR);
        } else {
            return result;
        }
    }

    public String getAddr() {
        return new IpInfo(clientCountry, clientProvince, clientCity).getAddr();
    }
}
