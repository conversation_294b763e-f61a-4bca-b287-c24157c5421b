package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.telecom.apigateway.model.enums.AccountStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("users")
@Schema(description = "用户信息")
public class User {
    /**
     * 表id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    /**
     * 用户名，唯一
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 用户密码
     */
    @Schema(description = "密码")
    private String password;

    @Schema(description = "手机号也作为登录凭证")
    private String phone;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "账户状态")
    private String accountStatus;

    private Boolean changePassword;

    /**
     * 是否逻辑删除
     */
    @Schema(description = "是否删除")
    @TableField(value = "is_deleted")
    private Boolean deleted;

    public User(String username, String phone) {
        LocalDateTime now = LocalDateTime.now();
        this.username = username;
        this.phone = phone;
        this.password ="ChangeMe";
        this.createTime = now;
        this.updateTime = now;
        this.accountStatus = AccountStatusEnum.ACTIVE.getValue();
        this.deleted = false;
        this.changePassword = true;
    }

    public void resetPassword() {
        updatePassword("ChangeMe");
        this.changePassword = true;
    }

    public void updatePassword(String password) {
        this.password = password;
        this.updateTime = LocalDateTime.now();
        this.changePassword = false;
    }

    public void delete() {
        this.deleted = true;
        this.updateTime = LocalDateTime.now();
    }
}
