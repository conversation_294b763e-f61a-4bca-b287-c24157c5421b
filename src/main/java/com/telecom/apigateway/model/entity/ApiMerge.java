package com.telecom.apigateway.model.entity;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.telecom.apigateway.config.mybatisplus.JsonbListTypeHandler;
import com.telecom.apigateway.config.mybatisplus.StringListTypeHandler;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

@TableName(autoResultMap = true)
public class ApiMerge implements java.io.Serializable {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    private String name;
    private String apiName;

    /**
     * 策略
     */
    private ApiMergeEnum.Policy policy;

    @Schema(description = "匹配条件")
    @TableField(typeHandler = JsonbListTypeHandler.class)
    private List<ApiMergeCondition> condition;

    private String appId;
    private String uriReg;
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> httpMethods;
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> api;

    @TableField(value = "is_enable")
    private Boolean enable;
    @TableField(value = "is_deleted")
    private Boolean deleted;

    private String createUser;
    private LocalDateTime createTime;
    private String updateUser;
    private LocalDateTime updateTime;

    /*
     * 启用时间
     */
    private LocalDateTime enableTime;
    private String remark;

    public ApiMerge() {

    }

    public static ApiMerge ofIgnore(String name,
                                    List<ApiMergeCondition> condition) {
        ApiMerge apiMerge = new ApiMerge();
        apiMerge.policy = ApiMergeEnum.Policy.IGNORE;

        apiMerge.name = name;
        apiMerge.condition = condition;

        apiMerge.enable = false;
        apiMerge.deleted = false;

        apiMerge.createUser = StpUtil.getLoginIdAsString();
        apiMerge.updateUser = StpUtil.getLoginIdAsString();
        LocalDateTime now = LocalDateTime.now();
        apiMerge.createTime = now;
        apiMerge.updateTime = now;
        return apiMerge;
    }

    public static ApiMerge ofMerge(String appId,
                                   String name,
                                   String apiName,
                                   String urlReg,
                                   List<String> httpMethod,
                                   List<String> api) {
        ApiMerge apiMerge = new ApiMerge();
        apiMerge.policy = ApiMergeEnum.Policy.MERGE;

        apiMerge.appId = appId;
        apiMerge.name = name;
        apiMerge.apiName = apiName;
        apiMerge.uriReg = urlReg;
        apiMerge.httpMethods = httpMethod;
        apiMerge.api = api;

        apiMerge.enable = false;
        apiMerge.deleted = false;

        apiMerge.createUser = StpUtil.getLoginIdAsString();
        apiMerge.updateUser = StpUtil.getLoginIdAsString();
        LocalDateTime now = LocalDateTime.now();
        apiMerge.createTime = now;
        apiMerge.updateTime = now;
        return apiMerge;
    }

    public static ApiMerge ofUpdate(
            String id, String name,
            ApiMergeEnum.Policy policy,
            List<ApiMergeCondition> condition,
            String urlReg,
            List<String> httpMethod,
            String remark) {
        ApiMerge apiMerge = new ApiMerge();
        apiMerge.id = id;
        apiMerge.name = name;
        apiMerge.policy = policy;
        apiMerge.condition = condition;
        apiMerge.uriReg = urlReg;
        apiMerge.httpMethods = httpMethod;
        apiMerge.remark = remark;
        apiMerge.updateUser = StpUtil.getLoginIdAsString();
        apiMerge.updateTime = LocalDateTime.now();
        return apiMerge;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public ApiMergeEnum.Policy getPolicy() {
        return policy;
    }

    public List<ApiMergeCondition> getCondition() {
        return condition;
    }

    public String getUriReg() {
        return uriReg;
    }

    public List<String> getHttpMethods() {
        return httpMethods;
    }

    public Boolean getEnable() {
        return enable;
    }

    public String getCreateUser() {
        return createUser;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public String getApiName() {
        return apiName;
    }

    public List<String> getApi() {
        return api;
    }

    public String getAppId() {
        return appId;
    }

    public LocalDateTime getEnableTime() {
        return enableTime;
    }

    public String getDetail() {
        if (ApiMergeEnum.Policy.IGNORE == policy) {
            String detail = "";
            for (ApiMergeCondition condition : condition) {
                detail += condition.getTarget().getName() +
                        " " + condition.getOperation().getName() +
                        " " + condition.getValue() + ";";
            }
            return detail;
        } else if (ApiMergeEnum.Policy.MERGE == policy) {
            return uriReg;
        }
        return null;
    }

    public boolean getEditable() {
        if (enable) {
            return false;
        }
        if (enableTime != null) {
            return false;
        }
        return true;
    }
}
