package com.telecom.apigateway.model.entity;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.ibatis.type.ArrayTypeHandler;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName(value = "roles", autoResultMap = true)
@Schema(description = "角色信息")
public class Role implements Serializable {
    
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "角色id")
    private String roleId;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "角色描述")
    private String description;

    @Schema(description = "菜单权限码")
    @TableField(typeHandler = ArrayTypeHandler.class)
    private String[] menuPermissions;

    @Schema(description = "应用权限")
    @TableField(typeHandler = ArrayTypeHandler.class)
    private String[] applicationPermissions;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateUser;

    @Schema(description = "是否删除")
    @TableField(value = "is_deleted")
    private Boolean deleted;

    public Role() {
        this.roleId = IdUtil.simpleUUID();
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.deleted = false;
    }

    public Role(String name, String[] menuPermissions, String[] applicationPermissions) {
        this.roleId = IdUtil.simpleUUID();
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.deleted = false;
        this.updateUser = StpUtil.getLoginIdAsString();
        this.name = name;
        this.menuPermissions = menuPermissions;
        this.applicationPermissions = applicationPermissions;
        this.description = name;
    }

    public void delete() {
        this.deleted = true;
        this.updateTime = LocalDateTime.now();
        this.updateUser = StpUtil.getLoginIdAsString();
    }

    public void update(String name, String[] menuPermissions, String[] applicationPermissions) {
        this.name = name;
        this.menuPermissions = menuPermissions;
        this.description = name;
        this.updateTime = LocalDateTime.now();
        this.updateUser = StpUtil.getLoginIdAsString();
        this.applicationPermissions = applicationPermissions;
    }
} 