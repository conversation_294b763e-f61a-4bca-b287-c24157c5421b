package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.JsonbListTypeHandler;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import lombok.Data;
import org.apache.ibatis.type.ArrayTypeHandler;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Data
@TableName(autoResultMap = true)
public class AbnormalBehaviorRuleTrigger {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 规则名
     */
    private String ruleId;
    private String ruleName;
    /**
     * 资产类型
     */
    private AbnormalBehaviorRuleEnum.AssetType assetType;
    private String assetId;
    /**
     * 触发的客户端ip
     */
    private String clientIp;
    /**
     * 触发的客户端端口
     */
    private String clientPort;
    /**
     * 是否失效
     */
    @TableField("is_valid")
    private Boolean valid;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    /**
     * 失效时间
     */
    private LocalDateTime invalidTime;
    /**
     * 更新时间, 需要默认等于创建时间
     */
    private LocalDateTime updateTime;
    private String updateUser;
    private String remark;

    /**
     * 规则条件
     */
    @TableField(typeHandler = JsonbListTypeHandler.class)
    private List<AbnormalBehaviorRuleCondition> condition;
    private String wafCondition;
    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 限制策略
     */
    private AbnormalBehaviorRuleEnum.Policy policy;
    /**
     * 策略持续时间(分钟)
     */
    private Long policyDuration;
    private AbnormalBehaviorRuleEnum.AbnormalType abnormalType;
    private Integer abnormalDuration;
    private Integer abnormalThreshold;

    @TableField(value = "log_id", typeHandler = ArrayTypeHandler.class)
    private String[] logIds;

    @TableField(value = "is_deleted")
    private Boolean deleted;

    public String getAbnormalDetail() {
        return abnormalDuration + "秒内" + getAbnormalTypeDesc() + abnormalThreshold + "次动作";
    }

    public String getPolicyDetail() {
        return getPolicyDesc() + policyDuration + "分钟";
    }

    public String getAbnormalTypeDesc() {
        switch (abnormalType) {
            case VISIT:
                return "访问";
            case ATTACK:
                return "攻击";
            case ERROR:
                return "错误";
            case SENSITIVE:
                return "涉敏";
        }
        return "未知";
    }

    public String getPolicyDesc() {
        switch (policy) {
            case WARNING:
                return "警告";
            case BLOCK:
                return "拦截";
            case BANNED:
                return "封禁";
        }
        return "未知";
    }
}
