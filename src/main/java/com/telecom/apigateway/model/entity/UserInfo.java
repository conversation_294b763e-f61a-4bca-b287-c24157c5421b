package com.telecom.apigateway.model.entity;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName(value = "user_info", autoResultMap = true)
public class UserInfo implements Serializable {
    /**
     * 表id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户名，唯一
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 是否逻辑删除
     */
    @TableField(value = "is_deleted")
    private Boolean deleted;

    private String department;

    @Schema(description = "手机号")
    private String phone;

    private String email;

//    /**
//     * PASSWORD | VERIFY_CODE
//     */
//    private String[] loginMode;

    private LocalDateTime validStartTime;

    private LocalDateTime validEndTime;

    public void delete() {
        this.deleted = true;
        this.updateTime = LocalDateTime.now();
        this.updateUser = StpUtil.getLoginIdAsString();
    }
}
