package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.enums.ProtocolEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.ArrayTypeHandler;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;

@Getter
@TableName(value = "applications", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class Application implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    @Schema(description = "应用id")
    private String applicationId;

    @Schema(description = "创建应用的用户")
    private String createUserId;

    @Schema(description = "应用名称")
    private String name;

    @Schema(description = "域名")
    private String host;

    @Schema(description = "端口")
    private String port;

    @Schema(description = "uri")
    private String uri;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private final LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @TableField(value = "is_deleted")
    private Boolean deleted;

    @Schema(description = "协议")
    private ProtocolEnum protocol;

    @Schema(description = "应用负责人")
    private String owner;

    @Schema(description = "应用负责人电话")
    private String phone;

    @Schema(description = "应用负责人邮箱")
    private String email;

    @Schema(description = "应用类型")
    private ApplicationTypeEnum type;

    @Schema(description = "父应用id")
    private String parentId;
    /**
     * 前端的编号，是个数组，直接返回给前端，前端处理
     */
    @Schema(description = "资产地区")
    @TableField(typeHandler = ArrayTypeHandler.class)
    private String[] area;

    public Application(String applicationId,
                       String createUserId,
                       String name,
                       String host,
                       String port,
                       ProtocolEnum protocol,
                       String[] area
    ) {
        LocalDateTime now = LocalDateTime.now();
        this.applicationId = applicationId;
        this.createUserId = createUserId;
        this.name = name;
        this.host = host;
        this.port = port;
        this.createTime = now;
        this.updateTime = now;
        this.deleted = false;
        this.protocol = protocol;
        this.type = ApplicationTypeEnum.APPLICATION;
        this.area = area;
    }

    public Application(String applicationId, String createUserId, String name, String host, String port, String uri, String remark, ProtocolEnum protocol, String owner, String phone, String email, String[] area) {
        this(applicationId, createUserId, name, host, port, protocol, area);
        this.uri = uri;
        this.remark = remark;
        this.owner = owner;
        this.phone = phone;
        this.email = email;
    }

    public Application(
            Application application,
            String applicationId,
            String createUserId,
            String name,
            String uri,
            String remark,
            String owner,
            String phone,
            String email,
            ApplicationTypeEnum applicationType
    ) {
        this(applicationId, createUserId, name, application.host, application.port, application.protocol, application.area);
        if (!ApplicationTypeEnum.APPLICATION.equals(applicationType)) {
            throw new IllegalArgumentException("only generate sub-application");
        }
        this.uri = uri;
        this.remark = remark;
        this.owner = owner;
        this.phone = phone;
        this.email = email;
        this.type = applicationType;
        this.parentId = application.applicationId;
    }

    public Application(
            Application application,
            String applicationId,
            String createUserId,
            String name,
            String uri,
            ApplicationTypeEnum applicationType
    ) {
        this(applicationId, createUserId, name, application.host, application.port, application.protocol, application.area);
        if (!ApplicationTypeEnum.FEATURE.equals(applicationType)) {
            throw new IllegalArgumentException("only generate feature for application");
        }
        this.uri = uri;
        this.type = applicationType;
        this.parentId = application.applicationId;

        // feature information extends parent application
        this.remark = application.remark;
        this.owner = application.owner;
        this.phone = application.phone;
        this.email = application.email;

    }

    public Application delete() {
        this.updateTime = LocalDateTime.now();
        this.deleted = true;
        return this;
    }

    public Application update(String name, String remark, String owner, String phone, String email, String[] area) {
        this.name = name;
        this.updateTime = LocalDateTime.now();
        this.remark = remark;
        this.owner = owner;
        this.phone = phone;
        this.email = email;
        this.area = area;
        return this;
    }

    public Application(
            Application application,
            String applicationId
    ) {
        this(applicationId, "system", "基础分组", application.host, application.port, application.protocol, application.area);
        this.uri = application.getUri();
        this.type = ApplicationTypeEnum.BASE_API;
        this.parentId = application.applicationId;

        // feature information extends parent application
        this.remark = application.remark;
        this.owner = application.owner;
        this.phone = application.phone;
        this.email = application.email;
    }

    public String getHttpHost() {
        if (Arrays.asList("80", "443").contains(this.port)) {
            return this.host;
        }
        return this.host + ":" + this.port;
    }
}
