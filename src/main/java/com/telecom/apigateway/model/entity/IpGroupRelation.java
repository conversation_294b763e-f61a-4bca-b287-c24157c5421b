package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("ip_group_relation")
public class IpGroupRelation implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String ipRelationId;

    private String ipGroupId;

    private String ipAddress;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @TableField(value = "is_deleted")
    private Boolean deleted;

    // 无参构造函数，供MyBatis-Plus使用
    public IpGroupRelation() {
    }

    public IpGroupRelation(String ipRelationId, String ipGroupId, String ipAddress) {
        LocalDateTime now = LocalDateTime.now();
        this.ipRelationId = ipRelationId;
        this.ipGroupId = ipGroupId;
        this.ipAddress = ipAddress;
        this.createTime = now;
        this.updateTime = now;
        this.deleted = false;
    }

    public IpGroupRelation delete() {
        this.deleted = true;
        this.updateTime = LocalDateTime.now();
        return this;
    }
}
