package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import lombok.Getter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@TableName("tags")
public class Tag implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String tagId;

    private String createUserId;

    private String tagName;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @TableField(value = "is_deleted")
    private Boolean deleted;

    public Tag(String tagId, String createUserId, String tagName) {
        LocalDateTime now = LocalDateTime.now();
        this.tagId = tagId;
        this.createUserId = createUserId;
        this.tagName = tagName;
        this.createTime = now;
        this.updateTime = now;
        this.deleted = false;
    }

    public void updateName(String tagName) {
        this.tagName = tagName;
        this.updateTime = LocalDateTime.now();
    }
}
