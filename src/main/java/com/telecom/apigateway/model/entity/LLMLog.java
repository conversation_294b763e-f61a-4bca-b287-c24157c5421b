package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.JsonbTypeHandler;
import com.telecom.apigateway.model.dto.NginxAccessLogDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 大模型日志
 *
 * <AUTHOR>
 * @date 2025-1-23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TableName("llm_log")
public class LLMLog {

    @Schema(description = "主键")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "日志id")
    private String logId;

    @Schema(description = "客户端ip")
    private String clientIp;
    @Schema(description = "客户端端口")
    private Integer clientPort;


    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private Date createTime;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private Date updateTime;
    private String updateUser;

    @Schema(description = "使用LLM的账号")
    private String useUser;
    @Schema(description = "分析内容的请求")
    private String anaysisRequest;
    @Schema(description = "分析内容的响应")
    private String anaysisResponse;
    @Schema(description = "分析类型，1为威胁分析，2为敏感信息分析")
    private Integer anaysisType;
    @Schema(description = "状态，1为发起请求，2为正常响应，100为报错")
    private Integer status;
    @Schema(description = "描述内容，用于备注")
    private String msg;
}
