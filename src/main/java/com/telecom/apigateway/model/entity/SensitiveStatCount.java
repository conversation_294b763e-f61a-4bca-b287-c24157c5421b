package com.telecom.apigateway.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-12-05
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SensitiveStatCount {
    private String id;
    private String appId;
    private String time;
    private TimeType timeType;
    private Integer sensitiveApiCount;
    private Integer newSensitiveApiCount;
    private Integer lowSensitiveCount;
    private Integer midSensitiveCount;
    private Integer highSensitiveCount;
    private LocalDateTime statTime;

    public enum TimeType {
        DAY,
        MONTH,
        YEAR
    }
}
