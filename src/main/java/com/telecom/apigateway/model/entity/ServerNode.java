package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 服务器节点信息表
 */
@Data
@TableName("server_node")
public class ServerNode implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主机ID
     */
    private String hostId;

    /**
     * 主机名称
     */
    @Schema(description = "主机名称")
    private String hostName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remarks;

    /**
     * CPU总数
     */
    private Integer cpuTotal;

    /**
     * 内存总量（单位：字节）
     */
    private Long memoryTotal;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 