package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 防护规则配置
 */
@Data
public class RuleConfig {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 配置id
     */
    private String configId;

    /**
     * 对应的规则id
     */
    private String ruleId;

    /**
     * 防护规则配置类型，禁用、仅观察、高度防护
     */
    @Schema(description = "防护规则配置类型")
    private String type;

    /**
     * 规则来源，system-系统内置规则，custom-自定义规则
     */
    @Schema(description = "规则来源")
    private String category;

    /**
     * 内置crs规则文件名
     */
    @Schema(description = "crs规则文件名")
    private String filename;

    /**
     * 自定义规则内容
     */
    @Schema(description = "规则内容")
    private String directives;
}
