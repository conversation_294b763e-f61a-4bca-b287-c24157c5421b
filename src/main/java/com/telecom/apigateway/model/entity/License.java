package com.telecom.apigateway.model.entity;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @program: APIWG-Service
 * @ClassName License
 * @description:
 * @author: Levi
 * @create: 2025-05-22 18:51
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("license")
public class License {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField( fill = FieldFill.INSERT)
    private String licenseKey;

    @TableField(fill = FieldFill.INSERT)
    private String status;

    @TableField("company_name")
    private String companyName;

    @TableField("expiration_days")
    private Integer expirationDays;

    @TableField("expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expirationDate;

    @TableField("contact_name")
    private String contactName;

    @TableField("contact_phone")
    private String contactPhone;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}