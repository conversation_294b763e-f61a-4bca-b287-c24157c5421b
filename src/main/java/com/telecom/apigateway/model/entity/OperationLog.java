package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@TableName(value = "operation_logs", autoResultMap = true)
@Builder
@ToString
@Getter
public class OperationLog {

    /**
     * 考虑查询需求，使用自增主键再增加单独的日志id
     */
    @Schema(description = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "日志id")
    private String logId;

    @Schema(description = "操作类型")
    private OperationTypeEnum operationType;

    @Schema(description = "资源类型")
    private ResourceTypeEnum resourceType;

    @Schema(description = "操作描述")
    private String formatDescription;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    @Schema(description = "操作用户")
    private String operationUser;

    @Schema(description = "登录ip")
    @TableField("login_ip")
    private String loginIP;

}