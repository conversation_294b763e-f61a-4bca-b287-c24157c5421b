package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.JsonbTypeHandler;
import com.telecom.apigateway.model.dto.NginxAccessLogDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 日志风险
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TableName("risk_log")
public class RiskLog {

    @Schema(description = "主键")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    @Schema(description = "api id")
    private String apiId;
    @Schema(description = "日志id")
    private String logId;

    @Schema(description = "crs 级别")
    private String crsSeverity;
    @Schema(description = "crs 标签")
    private String crsTags;
    @Schema(description = "crs 规则id")
    private String crsRuleId;
    @Schema(description = "crs 短规则id 3位")
    private String crsShortRuleId;
    @Schema(description = "crs 风险信息")
    private String crsMessage;

    @Schema(description = "客户端ip")
    private String clientIp;
    @Schema(description = "客户端端口")
    private Integer clientPort;
    @Schema(description = "客户端国家")
    private String clientCountry;
    @Schema(description = "客户端城市")
    private String clientCity;
    @Schema(description = "攻击目标地址")
    private String targetAddr;
    private String targetCountry;
    private String targetCity;

    // @Schema(description = "风险关键词(暂无实现)")
    // @TableField(exist = false)
    // private String riskWord;

    @Schema(description = "是否删除")
    @TableField("is_deleted")
    private Boolean deleted;
    @TableField("is_dealt")
    private Boolean dealt;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;
    private String updateUser;

    @Schema(description = "风险分数")
    private Integer score;

    @TableField(typeHandler = JsonbTypeHandler.class)
    private NginxAccessLogDTO log;
}
