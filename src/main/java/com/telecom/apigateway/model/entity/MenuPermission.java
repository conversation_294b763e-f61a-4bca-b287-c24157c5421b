package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("menu_permissions")
@Schema(description = "菜单权限信息")
public class MenuPermission {
    
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "权限码")
    private String code;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "前端路由路径")
    private String routePath;

    @Schema(description = "分类")
    private String category;

    @Schema(description = "分类排序")
    private Integer categorySort;

    @Schema(description = "菜单排序")
    private Integer menuSort;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除")
    @TableField(value = "is_deleted")
    private Boolean deleted;

    @Schema(description = "前端路由路径")
    private String routeName;

    @Schema(description = "父级菜单ID")
    private String parentCode;

    /**
     * 分类名称
     */
    private String categoryName;

} 