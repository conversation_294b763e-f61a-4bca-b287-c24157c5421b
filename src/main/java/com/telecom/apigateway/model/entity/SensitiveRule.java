package com.telecom.apigateway.model.entity;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.HttpFieldEnumArrayTypeHandler;
import com.telecom.apigateway.model.enums.HttpFieldEnum;
import com.telecom.apigateway.model.enums.SensitiveRuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@TableName
public class SensitiveRule implements Serializable {
    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description = "主键")
    private String id;
    @Schema(description = "应用id")
    @Deprecated
    private String appId;
    @Schema(description = "规则代码, 自定义规则等于id, 内置规则为枚举")
    private String code;
    @Schema(description = "规则名称")
    private String name;
    @Schema(description = "规则等级")
    private Integer level;
    @Schema(description = "检测部分")
    @TableField(typeHandler = HttpFieldEnumArrayTypeHandler.class)
    private HttpFieldEnum[] detectParts;
    @Schema(description = "匹配类型")
    private SensitiveRuleEnum.MatchCase matchCase;
    @Schema(description = "匹配方式")
    private SensitiveRuleEnum.MatchMethod matchMethod;
    @Schema(description = "规则内容")
    private String keywords;
    @Schema(description = "是否启用")
    private Boolean isEnable;
    @Schema(description = "来源")
    private SensitiveRuleEnum.Source source;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "创建人")
    private String createUser;
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    @Schema(description = "更新人")
    private String updateUser;
    @Schema(description = "是否删除")
    private Boolean isDeleted;

    public static SensitiveRule newUpdateRule(String id, String name, Integer level, boolean isEnable){
        SensitiveRule rule = new SensitiveRule();
        rule.setId(id);
        rule.setName(name);
        rule.setLevel(level);
        rule.setIsEnable(isEnable);
        rule.setUpdateTime(LocalDateTime.now());
        rule.setUpdateUser(StpUtil.getLoginIdAsString());
        return rule;
    }
}
