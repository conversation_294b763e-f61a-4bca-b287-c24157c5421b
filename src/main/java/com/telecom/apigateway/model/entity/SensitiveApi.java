package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.telecom.apigateway.common.Constant;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-10-29
 */
@NoArgsConstructor
@Data
public class SensitiveApi implements Serializable {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    private String apiId;
    private String sensitiveRuleId;
    @TableField(value = "is_deleted")
    private Boolean deleted;
    private Integer nonsensitiveCount;
    private Integer sensitiveCount;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createUser;
    private String updateUser;
    @TableField(value = "is_dealt")
    private Boolean dealt;

    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime lastSensitiveTime;
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime dealSensitiveTime;

}
