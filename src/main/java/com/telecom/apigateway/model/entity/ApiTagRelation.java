package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@TableName("api_tag_relations")
public class ApiTagRelation implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String tagId;

    private String apiId;

    private String createUserId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @TableField(value = "is_deleted")
    private Boolean deleted;

    public ApiTagRelation(String tagId, String apiId, String createUserId) {
        LocalDateTime now = LocalDateTime.now();
        this.apiId = apiId;
        this.tagId = tagId;
        this.createUserId = createUserId;
        this.createTime = now;
        this.updateTime = now;
        this.deleted = false;
    }
}
