package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("regions")
@Data
public class Region {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String code;
    private String name;
    private Double longitude;
    private Double latitude;
}
