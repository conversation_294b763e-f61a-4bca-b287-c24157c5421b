package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.utils.ApiUrlUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * api info
 *
 * <AUTHOR> Denty
 * @date : 2024/8/13
 */
@Data
@TableName("api")
@Schema(description = "api info")
public class ApiInfo implements Serializable {
    private static final long serialVersionUID = 164514535681554941L;

    @Schema(description = "api id")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "api 名称, 默认等于 uri")
    private String name;

    @Schema(description = "所用应用 id")
    private String appId;

    @Schema(description = "域名 或 ip")
    private String host;

    @Schema(description = "端口")
    private Integer port;

    @Schema(description = "http method,GET POST PUT DELETE")
    private String httpMethod;

    /**
     * 无效字段, 请勿使用
     */
    @Schema(description = "风险等级,  3高危、2中危、1低危、0安全")
    @Deprecated
    private Integer riskLevel;

    /**
     * 具有不可信性, 原子性可能无保障, 建议关联查询进行匹配
     */
    @Schema(description = "涉敏等级, 0安全/1低/2中/3高")
    @Deprecated
    private Integer sensitiveLevel;

    @Schema(description = "是否在线")
    private Boolean isOnline;

    @Schema(description = "uri")
    private String uri;

    @Schema(description = "路径级别 0:根 1:一级 2:二级")
    private Integer pathLevel;

    @Schema(description = "是否激活")
    private Boolean isActive;

    @Schema(description = "来源, 0自动 1手动")
    private Integer source;

    @Schema(description = "发现时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime discoverTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建人")
    private String createUser;
    @Schema(description = "更新人")
    private String updateUser;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @Schema(description = "上线时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime onlineTime;

    @Schema(description = "是否删除")
    @TableField(value = "is_deleted")
    @JsonIgnore
    private Boolean deleted;

    @Schema(description = "是否加密")
    @TableField(value = "is_encrypt")
    private Boolean encrypted;

    public String getHttpHost() {
        if (Objects.isNull(port) || port == 80 || port == 443) {
            return host;
        }
        return host + ":" + port;
    }

    public String toReconizedString() {
        return httpMethod + ":" + getHttpHost() + uri;
    }

    public String getUrl() {
        return getHttpHost() + uri;
    }


    public boolean equals(EsNginxDTO ngLog) {
        return equals(ngLog.getHttpMethod(), ngLog.getHttpHost(), ngLog.getUri());
    }

    public boolean equals(String httpMethod, String httpHost, String uri) {
        if (!this.httpMethod.equalsIgnoreCase(httpMethod)) {
            return false;
        }
        if (!getHttpHost().equals(httpHost)) {
            return false;
        }
        if (this.uri.equals(uri)) {
            return true;
        }
        return uri.contains("{") && uri.contains("}") && ApiUrlUtils.isMatch(this.uri, uri);
    }

    public boolean equals(String httpMethod, String host, Integer port, String uri) {
        if (!this.httpMethod.equalsIgnoreCase(httpMethod)) {
            return false;
        }
        if (!this.host.equals(host)) {
            return false;
        }
        if (!this.port.equals(port)) {
            return false;
        }
        if (this.uri.equals(uri)) {
            return true;
        }
        return this.uri.contains("{") && this.uri.contains("}") && ApiUrlUtils.isMatch(this.uri, uri);
    }

    public boolean isPathVariable() {
        return this.uri.contains("{") && this.uri.contains("}");
    }
}
