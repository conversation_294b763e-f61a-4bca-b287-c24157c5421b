package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler;
import com.telecom.apigateway.model.enums.DecryptType;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@Getter
@TableName(autoResultMap = true)
public class ApiDecrypt {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String apiId;

//    @TableField(typeHandler = MybatisEnumTypeHandler.class)
    private DecryptType decryptType;

    private String decryptKey;

    private LocalDateTime createTime;

    public ApiDecrypt(String apiId, String decryptType, String decryptKey) {
        new ApiDecrypt(apiId, DecryptType.valueOf(decryptType), decryptKey);
    }

    public ApiDecrypt(String apiId, DecryptType decryptType, String decryptKey) {
        this.apiId = apiId;
        this.decryptType = decryptType;
        this.decryptKey = decryptKey;
        this.createTime = LocalDateTime.now();
    }

    public ApiDecrypt setDecryptType(String decryptType) {
        this.decryptType = DecryptType.valueOf(decryptType);
        return this;
    }

    public ApiDecrypt setDecryptType(DecryptType decryptType) {
        this.decryptType = decryptType;
        return this;
    }

    public ApiDecrypt setDecryptKey(String decryptKey) {
        this.decryptKey = decryptKey;
        return this;
    }
}
