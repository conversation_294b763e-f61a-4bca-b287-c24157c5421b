package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("rules")
public class Rule {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String ruleId;

    @Schema(description = "威胁类型")
    private String attackType;

    @Schema(description = "防护模块")
    private String module;

    @Schema(description = "分数检索关键字")
    private String scoreKeyword;

    @Schema(description = "类型编码")
    private String type;

    @Schema(description = "规则来源")
    private String category;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

}
