package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.telecom.apigateway.model.enums.IpGroupTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("ip_groups")
public class IpGroup implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String groupId;

    @Schema(description = "ip组名称")
    private String name;

    @Schema(description = "类型")
    private IpGroupTypeEnum type;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @TableField(value = "is_deleted")
    private Boolean deleted;

    public IpGroup(String groupId, String name) {
        LocalDateTime now = LocalDateTime.now();
        this.groupId = groupId;
        this.name = name;
        this.type = IpGroupTypeEnum.CUSTOMIZE;
        this.createTime = now;
        this.updateTime = now;
        this.deleted = false;
    }

    public IpGroup update(String name) {
        this.name = name;
        this.updateTime = LocalDateTime.now();
        return this;
    }
    public IpGroup delete() {
        this.deleted = true;
        this.updateTime = LocalDateTime.now();
        return this;
    }

}
