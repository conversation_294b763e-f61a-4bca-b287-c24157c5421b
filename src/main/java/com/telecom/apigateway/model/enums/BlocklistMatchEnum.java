package com.telecom.apigateway.model.enums;

import lombok.Getter;

public class BlocklistMatchEnum {

    /**
     * 匹配对象
     */
    @Getter
    public enum MatchTarget {
        SOURCE_IP("sourceIp", "源IP", "ip"),
        PATH("path", "路径", "uri"),
        HOST("host", "Host", "host"),
        HEADER("header", "Header", "headers"),
        BODY("body", "Body", "posts_all"),
        METHOD("method", "请求方式", "method"),
        API("api", "API", "api");

        private final String code;
        private final String desc;
        /**
         * waf 配置字段
         */
        private final String wafField;

        MatchTarget(String code, String desc, String wafField) {
            this.code = code;
            this.desc = desc;
            this.wafField = wafField;
        }

        public static MatchTarget getByCode(String code) {
            for (MatchTarget value : values()) {
                if (value.code.equals(code)) {
                    return value;
                }
            }
            return null;
        }
    }

    /**
     * 匹配方式
     */
    @Getter
    public enum MatchOperation {
        EQUALS("equals", "等于"),
        NOT_EQUALS("notEquals", "不等于"),
        CONTAINS("contains", "包含"),
        NOT_CONTAINS("notContains", "不包含"),
        IN_GROUP("inGroup", "属于组"),
        NOT_IN_GROUP("notInGroup", "不属于组"),
        REGEX("regex", "正则表达式"),
        FUZZY("fuzzy", "模糊匹配");

        private final String code;
        private final String desc;

        MatchOperation(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static MatchOperation getByCode(String code) {
            for (MatchOperation value : values()) {
                if (value.code.equals(code)) {
                    return value;
                }
            }
            return null;
        }
    }
}
