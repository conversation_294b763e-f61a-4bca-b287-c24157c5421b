package com.telecom.apigateway.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 应用修正策略状态枚举
 */
@Getter
public enum CorrectPolicyStatusEnum {
    ENABLED("ENABLED", "启用"),
    DISABLED("DISABLED", "禁用");

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    CorrectPolicyStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}