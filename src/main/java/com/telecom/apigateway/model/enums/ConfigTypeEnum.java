package com.telecom.apigateway.model.enums;

import java.util.Arrays;

public enum ConfigTypeEnum {
    DISABLE,
    NOTICE,
    GUARD,
    ;

    public static boolean checkValid(String type) {
        return Arrays.stream(ConfigTypeEnum.values()).anyMatch((value) -> value.name().equals(type));
    }

    public static ConfigTypeEnum getEnum(String type) {
        return Arrays.stream(ConfigTypeEnum.values()).filter((value) -> value.name().equals(type)).findFirst().orElse(null);
    }

    public String getText() {
        if (ConfigTypeEnum.DISABLE.equals(this)) {
            return "禁用";
        } else if (ConfigTypeEnum.NOTICE.equals(this)) {
            return "仅观察";
        } else if (ConfigTypeEnum.GUARD.equals(this)) {
            return "高度防护";
        }

        return null;
    }
}
