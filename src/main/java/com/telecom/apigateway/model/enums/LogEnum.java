package com.telecom.apigateway.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-01-20
 */
public class LogEnum {

    /**
     * 请求状态
     */
    @Getter
    public enum RequestState {
        /**
         * 正常
         */
        NORMAL("正常"),
        /**
         * 白名单
         */
        BLOCKLIST_ALLOW("白名单放行"),
        /**
         * 黑名单
         */
        BLOCKLIST_REJECT("黑名单拦截"),
        /**
         * 威胁拦截
         */
        RISK_REJECT("威胁已拦截"),
        /**
         * 威胁观察
         */
        RISK_LOG("威胁仅观察"),
        ERROR("ERROR"),
        ABRT_REJECT("异常行为已拦截"),
        ABRT_LOG("异常行为仅观察"),
        ;

        private final String msg;

        RequestState(String msg) {
            this.msg = msg;
        }
    }


    public enum WafDetectStatus {
        PASS, ALLOW, REJECT, ERROR
    }

    public enum CrsDetectStatus {
        PASS, REJECT, LOG, ERROR
    }

    public enum AbnormalBehaviorDetectStatus {
        PASS, REJECT, LOG
    }
}
