package com.telecom.apigateway.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OperationTypeEnum {
    UPDATE("{resourceName}: 将 {0} 的 {1} 属性从 {2} 修改为 {3}"),

    /**
     * 需要比较的对象需要实现@Serializable, 需要返回修改后的对象
     */
    FULL_UPDATE("{resourceName}： 修改了 {0} , 修改内容: {1}"),
    DELETE("删除 {resourceName} 的 {0}"),
    /**
     * 配置拦截器，SensitiveUrlLogInterceptor
     */
    VIEW("查看 {resourceName}"),
    LOGIN("登录系统"),
    INSERT("创建了 {resourceName}: {0}"),;

    private final String description;
}
