package com.telecom.apigateway.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
public class AbnormalBehaviorRuleEnum {

    /**
     * 异常类型
     */
    @Getter
    public enum AbnormalType {
        VISIT("高频访问",
                Arrays.asList(Policy.WARNING, Policy.BLOCK, Policy.BANNED),
                Arrays.asList(Target.header, Target.method)),
        ATTACK("高频攻击",
                Arrays.asList(Policy.WARNING, Policy.BANNED),
                Collections.singletonList(Target.riskRule)),
        ERROR("高频错误",
                Arrays.asList(Policy.WARNING, Policy.BANNED),
                Collections.singletonList(Target.errorCode)),
        SENSITIVE("高频涉敏",
                Arrays.asList(Policy.WARNING, Policy.BANNED),
                Collections.singletonList(Target.sensitiveRule));

        private final String desc;
        private final List<Policy> policies;
        private final List<Target> targets;

        AbnormalType(String desc, List<Policy> policies, List<Target> targets) {
            this.desc = desc;
            this.policies = policies;
            this.targets = targets;
        }

        @JsonCreator
        public static AbnormalType fromString(String value) {
            if (value == null || value.isEmpty()) {
                return null;  // 返回默认值
            }
            return AbnormalType.valueOf(value.toUpperCase());
        }
    }

    /**
     * 策略
     */
    public enum Policy {
        /**
         * 警告
         */
        WARNING,
        /**
         * 拦截
         */
        BLOCK,
        /**
         * 封禁
         */
        BANNED;

        @JsonCreator
        public static Policy fromString(String value) {
            if (value == null || value.isEmpty()) {
                return null;  // 返回默认值
            }
            return Policy.valueOf(value.toUpperCase());
        }
    }

    @Getter
    public enum Target {
        method("请求方式",
                Arrays.asList(Operation.equals, Operation.containsAny)),
        header("请求头",
                Arrays.asList(Operation.equals, Operation.notEquals, Operation.contains, Operation.notContains)),
        sensitiveRule("涉敏标签",
                Collections.singletonList(Operation.containsAny)),
        riskRule("威胁类型",
                Collections.singletonList(Operation.containsAny)),
        errorCode("错误码",
                Collections.singletonList(Operation.containsAny)),
        ;
        private final String desc;
        private final List<Operation> operations;

        Target(String desc, List<Operation> operations) {
            this.desc = desc;
            this.operations = operations;
        }
    }


    public enum AssetType {
        APPLICATION, API;

        @JsonCreator
        public static AssetType fromString(String value) {
            if (value == null || value.isEmpty()) {
                return null;  // 返回默认值
            }
            return AssetType.valueOf(value.toUpperCase());
        }
    }

    @Getter
    public enum Operation {
        /**
         * 等于, 右边多个时, 等于其一即可
         */
        equals("等于"),
        notEquals("不等于"),
        gt("大于"),
        gte("大于等于"),
        le("小于"),
        lte("小于等于"),
        containsAny("包含其一"),
        contains("包含"),
        notContains("不包含"),
        ;

        private final String desc;

        Operation(String desc) {
            this.desc = desc;
        }
    }

    public enum Source {
        SYSTEM, CUSTOM;

        @JsonCreator
        public static Source fromString(String value) {
            if (value == null || value.isEmpty()) {
                return null;  // 返回默认值
            }
            return Source.valueOf(value.toUpperCase());
        }
    }

}
