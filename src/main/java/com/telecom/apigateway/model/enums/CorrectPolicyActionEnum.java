package com.telecom.apigateway.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 应用修正策略动作枚举
 */
public enum CorrectPolicyActionEnum {
    EXCLUDE_FROM_ASSETS("EXCLUDE_FROM_ASSETS", "不计入应用资产，仅记录日志"),
    MERGE_TO_ONE_APP("MERGE_TO_ONE_APP", "合并为一个应用");

    @EnumValue
    @JsonValue
    private final String code;
    
    private final String description;

    CorrectPolicyActionEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
} 