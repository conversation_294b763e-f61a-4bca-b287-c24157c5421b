package com.telecom.apigateway.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-11-19
 */
@Getter
public enum CrsSeverityEnum {
    CRITICAL("critical"),
    EMERGENCY("emergency"),
    WARNING("warning");

    private final String code;

    CrsSeverityEnum(String code) {
        this.code = code;
    }

    public static CrsSeverityEnum getByCode(String code) {
        for (CrsSeverityEnum value : CrsSeverityEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
