package com.telecom.apigateway.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ResourceTypeEnum {
    APPLICATION("应用资产"),
    PROTECTION_CONFIG("防护配置"),
    ABNORMAL_BEHAVIOR("异常行为规则"),
    USER_MANAGEMENT("用户"),
    API_INFO("API资产"),
    BLOCK_LIST("黑白名单"),
    IP_GROUP("IP组"),
    ROLE_MANAGEMENT("角色"),
    SENSITIVE_RULE("涉敏规则"),
    SERVER_MONITOR("服务器节点"),
    THREAT_LIST("威胁清单"),
    ;

    private final String name;
}
