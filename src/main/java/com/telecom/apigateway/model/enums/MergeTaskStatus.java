package com.telecom.apigateway.model.enums;

import lombok.Getter;

/**
 * 历史数据合并任务状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@Getter
public enum MergeTaskStatus {
    /**
     * 待执行
     */
    PENDING("PENDING", "待执行"),
    
    /**
     * 执行中
     */
    RUNNING("RUNNING", "执行中"),
    
    /**
     * 合并API中
     */
    MERGING_API("MERGING_API", "合并API中"),
    
    /**
     * 合并应用中
     */
    MERGING_APPLICATION("MERGING_APPLICATION", "合并应用中"),
    
    /**
     * 执行成功
     */
    SUCCESS("SUCCESS", "执行成功"),
    
    /**
     * 执行失败
     */
    FAILED("FAILED", "执行失败");

    private final String code;
    private final String description;

    MergeTaskStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
