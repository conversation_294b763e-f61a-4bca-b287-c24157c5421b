package com.telecom.apigateway.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RiskLevelEnum {
    HIGH(3, "高危"),
    MEDIUM(2, "中危"),
    LOW(1, "低危"),
    SAFE(0, "安全"),
    ;

    private final Integer code;
    private final String label;

    /**
     * 打分标准
     * 高危（拦截）	得分 >=5
     * 中危	3<= 得分 <5
     * 低危	得分 <3
     *
     * @param score
     * @return
     */
    public static RiskLevelEnum getRiskLevel(Integer score) {
        RiskLevelEnum risk = RiskLevelEnum.SAFE;
        if (score < 3 && score > 0) {
            risk = RiskLevelEnum.LOW;
        } else if (score < 5 && score >= 3) {
            risk = RiskLevelEnum.MEDIUM;
        } else if (score >= 5) {
            risk = RiskLevelEnum.HIGH;
        }
        return risk;
    }

    public static String getNameByCode(Integer code) {
        for (RiskLevelEnum riskLevelEnum : RiskLevelEnum.values()) {
            if (riskLevelEnum.getCode().equals(code)) {
                return riskLevelEnum.getLabel();
            }
        }
        return null;
    }
}
