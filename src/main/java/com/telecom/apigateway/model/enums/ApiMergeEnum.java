package com.telecom.apigateway.model.enums;

import lombok.Getter;

public class ApiMergeEnum {
    public enum Policy {
        /**
         * 策略
         */
        IGNORE,
        /**
         * 策略
         */
        MERGE
    }

    @Getter
    public enum Target {
        URI("路径"),

        HTTP_METHOD("请求方式"),

        CONTENT_TYPE("Content-Type"),
        STATUS_CODE("状态码");

        private final String name;

        Target(String name) {
            this.name = name;
        }

    }

    @Getter
    public enum Operation {
        CONTAINS("contains", "包含"),
        EQUALS("equals", "等于"),
        NOT_EQUALS("notEquals", "不等于"),
        BELONGS_TO_RANGE("belongsToRange", "属于范围"),
        NOT_BELONGS_TO_RANGE("notBelongsToRange", "不属于范围"),
        WILDCARD("wildcard", "通配符"),
        REGEX("regex", "正则表达式");

        private final String code;
        private final String name;

        Operation(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}
