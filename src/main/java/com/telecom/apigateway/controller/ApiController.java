package com.telecom.apigateway.controller;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.dto.ApiImportFromFileDTO;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.vo.request.BatchUpdateApiRequest;
import com.telecom.apigateway.model.vo.request.QueryApiRequest;
import com.telecom.apigateway.model.vo.request.QueryLogRequest;
import com.telecom.apigateway.model.vo.request.UpdateApiRequest;
import com.telecom.apigateway.model.vo.response.ApiAttackStatResponse;
import com.telecom.apigateway.model.vo.response.ApiHttpInfoResponse;
import com.telecom.apigateway.model.vo.response.ApiQueryResponse;
import com.telecom.apigateway.model.vo.response.ApiRiskSummaryResponse;
import com.telecom.apigateway.model.vo.response.ApiStatResponse;
import com.telecom.apigateway.model.vo.response.ApiSummaryResponse;
import com.telecom.apigateway.model.vo.response.LogQueryResponse;
import com.telecom.apigateway.service.ApiInfoBizService;
import com.telecom.apigateway.service.ApiInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;

@RestController
@RequestMapping("/api/api")
@Tag(name = "api 资产接口")
@CheckApiPermission("API_LIST")
public class ApiController {

    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private ApiInfoBizService apiInfoBizService;

    @Operation(summary = "api 分页查询")
    @GetMapping("/page")
    public Result<IPage<ApiQueryResponse>> queryPage(@ModelAttribute @Validated QueryApiRequest request) {
        IPage<ApiQueryResponse> data = apiInfoBizService.queryPage(request);
        return Result.success(data);
    }

    @Operation(summary = "api 删除")
    @DeleteMapping("/{apiId}")
    public Result<String> delete(@Parameter(name = "api id", description = "api 的 id ") @PathVariable String apiId) {
        apiInfoBizService.deleteByApiId(apiId);
        return Result.success("success");
    }

    @Operation(summary = "api 批量删除")
    @DeleteMapping("/batch")
    public Result<String> batchDelete(@Parameter(name = "apiIds", description = "api 的 id ")
                                      @RequestParam(required = false) List<String> apiIds) {
        for (String apiId : apiIds) {
            apiInfoBizService.deleteByApiId(apiId);
        }
        return Result.success("success");
    }

    @Operation(summary = "api 批量删除")
    @DeleteMapping("/batch/re")
    public Result<String> batchDelete() {
        List<ApiInfo> list = apiInfoService.lambdaQuery().eq(ApiInfo::getDeleted, true).list();
        for (ApiInfo apiInfo : list) {
            apiInfoBizService.deleteByApiId(apiInfo.getId());
        }
        return Result.success("success");
    }

    @Operation(summary = "api 批量更新")
    @PostMapping("/update/batch")
    public Result<String> batchUpdate(@RequestBody List<ApiInfo> apiInfoList) {
        apiInfoService.updateBatchById(apiInfoList);
        return Result.success("success");
    }


    @Operation(summary = "api 更新")
    @PostMapping("/update")
    public Result<String> update(@RequestBody @Validated UpdateApiRequest request) {
        apiInfoService.updateByApiId(request);
        apiInfoBizService.updateDecryptInfo(request);
        return Result.success("success");
    }

    @Operation(summary = "api 上线")
    @PostMapping("/{id}/online")
    public Result<String> onlineApi(@PathVariable String id) {
        apiInfoService.online(id);
        return Result.success("success");
    }

    @Operation(summary = "api 下线")
    @PostMapping("/{id}/offline")
    public Result<String> updateStatus(@PathVariable String id) {
        apiInfoService.offline(id);
        return Result.success("success");
    }

    @Operation(summary = "api 批量更新状态")
    @PostMapping("/update-status/batch")
    public Result<String> updateStatus(@RequestBody @Validated BatchUpdateApiRequest param) {
        apiInfoService.batchUpdateApi(param);
        return Result.success("success");
    }

    @Operation(summary = "api 统计, 概览")
    @ApiResponse(content = {@Content(schema = @Schema(implementation = ApiSummaryResponse.class))})
    @GetMapping("/summary")
    public Result<ApiSummaryResponse> summary() {
        ApiSummaryResponse summary = apiInfoBizService.summary();
        return Result.success(summary);
    }

    @Operation(summary = "风险 api 统计")
    @ApiResponse(content = {@Content(schema = @Schema(implementation = ApiRiskSummaryResponse.class))})
    @GetMapping("/risk/summary")
    public Result<ApiRiskSummaryResponse> riskSummary() {
        ApiRiskSummaryResponse summary = apiInfoBizService.riskSummary();
        return Result.success(summary);
    }


    @Operation(summary = "api 导入")
    @PostMapping("/import")
    public Result<?> importApi(@RequestParam("file") MultipartFile file, @RequestParam String appId) {
        List<?> apis = apiInfoBizService.importApi(appId, file);
        return Result.success(apis);
    }


    @Operation(summary = "api 批量入")
    @PostMapping("/add/batch")
    public Result<String> batchAdd(@RequestBody List<ApiImportFromFileDTO> data) {
        apiInfoService.batchAdd(data, false);
        return Result.success("success");
    }

    @Operation(summary = "api 批量强制插入(覆盖已有)")
    @PostMapping("/add/batch/force")
    public Result<String> forceBatchAdd(@RequestBody List<ApiImportFromFileDTO> data) {
        apiInfoService.batchAdd(data, true);
        return Result.success("success");
    }

    @Operation(summary = "api 请求示例")
    @GetMapping("/{apiId}/http-detail")
    public Result<ApiHttpInfoResponse> getHttpDetail(@PathVariable String apiId) {
        ApiHttpInfoResponse httpDetail = apiInfoService.getHttpDetail(apiId);
        return Result.success(httpDetail);
    }

    @Operation(summary = "api 明细")
    @GetMapping("/{apiId}/detail")
    public Result<ApiQueryResponse> getDetail(@PathVariable String apiId) {
        ApiQueryResponse apiInfo = apiInfoBizService.queryById(apiId, true);
        return Result.success(apiInfo);
    }

    @Operation(summary = "api 请求历史")
    @GetMapping("/{apiId}/log/history")
    public Result<IPage<LogQueryResponse>> getApiLogHistory(@ModelAttribute QueryLogRequest request,
                                                            @PathVariable String apiId) {
        request.setApiId(apiId);
        IPage<LogQueryResponse> logs = apiInfoBizService.getLogHistory(request);
        return Result.success(logs);
    }

    @Operation(summary = "api 全量查询")
    @GetMapping("/query")
    @CheckApiPermission(value = {"BLOCK_LIST", "SENSITIVE_CONFIG"})
    public Result<List<ApiQueryResponse>> query(@ModelAttribute QueryApiRequest request) {
        List<ApiQueryResponse> apiInfos = apiInfoBizService.query(request);
        return Result.success(apiInfos);
    }

    @Operation(summary = "api 导出")
    @GetMapping("/export")
    public void exportExcel(@RequestParam List<String> ids,
                            HttpServletResponse response) throws IOException {
        List<LinkedHashMap<String, String>> apiInfos = apiInfoService.export(ids);

        // 创建 Excel writer 对象
        ExcelWriter writer = ExcelUtil.getWriter(true);
        // 写入数据
        writer.write(apiInfos, true);
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8");
        String timeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
        response.setHeader("Content-Disposition", "attachment; filename=" +
                URLEncoder.encode("API清单_" + timeStr + ".xlsx", "UTF-8"));
        // 输出到客户端
        writer.flush(response.getOutputStream());
        writer.close();
    }

    @Operation(summary = "api 模板")
    @GetMapping("/download/sample/{fileType}")
    public ResponseEntity<ClassPathResource> exportExcel(@PathVariable String fileType) throws IOException {
        // 构建文件路径，使用 ClassPathResource 来处理相对路径
        String fileName = apiInfoService.getFileNameByType(fileType);
        ClassPathResource resource = new ClassPathResource("download/" + fileName);

        // 检查文件是否存在
        if (!resource.exists()) {
            throw new IOException("File not found: " + fileName);
        }

        // 构建响应
        return ResponseEntity.ok()
                .contentType(apiInfoService.getMediaType(fileName))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                .body(resource);

    }

    @Operation(summary = "api 统计")
    @GetMapping("/{id}/stat")
    public Result<ApiStatResponse> stat(@PathVariable String id) {
        ApiStatResponse stat = apiInfoBizService.statById(id);
        return Result.success(stat);
    }

    @Operation(summary = "查询 uri")
    @GetMapping("/uri/query")
    public Result<List<String>> stat(@Parameter(name = "uri") @RequestParam(required = false) String uri,
                                     @Parameter(name = "pathLevel") @RequestParam(required = false) Integer pathLevel) {
        List<String> uris = apiInfoService.queryUri(uri, pathLevel);
        return Result.success(uris);
    }

    @Operation(summary = "api 攻击统计")
    @GetMapping("/{id}/attack/stat")
    public Result<ApiAttackStatResponse> statAttack(@PathVariable String id) {
        ApiAttackStatResponse stat = apiInfoBizService.statAttackById(id);
        return Result.success(stat);
    }
}

