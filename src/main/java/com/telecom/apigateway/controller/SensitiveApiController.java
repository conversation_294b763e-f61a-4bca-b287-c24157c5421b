package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.enums.SensitiveStatEnum;
import com.telecom.apigateway.model.vo.request.QuerySensitiveApiRequest;
import com.telecom.apigateway.model.vo.response.SensitiveApiDetailResponse;
import com.telecom.apigateway.model.vo.response.SensitiveApiQueryResponse;
import com.telecom.apigateway.model.vo.response.SensitiveKeywordResponse;
import com.telecom.apigateway.service.SensitiveApiService;
import com.telecom.apigateway.service.SensitiveBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/api/sensitive-api")
@Tag(name = "涉敏详情接口")
@CheckApiPermission("SENSITIVE_LIST")
public class SensitiveApiController {

    @Resource
    private SensitiveApiService sensitiveApiService;
    @Resource
    private SensitiveBizService sensitiveBizService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public Result<IPage<SensitiveApiQueryResponse>> queryPage(@ModelAttribute @Validated QuerySensitiveApiRequest request) {
        IPage<SensitiveApiQueryResponse> page = sensitiveApiService.queryPage(request);
        return Result.success(page);
    }

    @Operation(summary = "明细")
    @GetMapping("/{id}/detail")
    public Result<SensitiveApiDetailResponse> getDetail(@PathVariable String id) {
        SensitiveApiDetailResponse detail = sensitiveBizService.getDetail(id);
        return Result.success(detail);
    }

    @Operation(summary = "处置")
    @PostMapping("/{id}/deal")
    public Result<String> deal(@PathVariable String id) {
        sensitiveApiService.deal(id);
        return Result.success("success");
    }

    @Operation(summary = "ip 获取的涉敏信息")
    @GetMapping("/keyword-by-ip")
    public Result<List<SensitiveKeywordResponse>> getKeywordByIp(@RequestParam String ip) {
        List<SensitiveKeywordResponse> datas = sensitiveApiService.getKeywordByIp(ip);
        return Result.success(datas);
    }

    @Operation(summary = "统计")
    @GetMapping("/stat/{statType}")
    @CheckApiPermission(value = {"SCREEN", "RISK_OVERVIEW"})
    public Result<List<?>> stat(@RequestParam(required = false, defaultValue = "") String appId,
                                @RequestParam @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN) LocalDateTime startTime,
                                @RequestParam @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN) LocalDateTime endTime,
                                @PathVariable String statType) {
        SensitiveStatEnum sensitiveStatEnum = SensitiveStatEnum.valueOf(statType.toUpperCase());
        List<?> result = sensitiveBizService.stat(sensitiveStatEnum, appId, startTime, endTime);
        return Result.success(result);
    }

}
