package com.telecom.apigateway.controller;

import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.entity.MenuPermission;
import com.telecom.apigateway.model.vo.request.PermissionRequest;
import com.telecom.apigateway.service.MenuPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Tag(name = "菜单权限管理")
@RestController
@RequestMapping("/api/permission")
@RequiredArgsConstructor
public class PermissionController {

    private final MenuPermissionService menuPermissionService;

    @Operation(summary = "获取菜单列表")
    @GetMapping("/list")
    public Result<List<MenuPermission>> listPermissions() {
//        List<MenuPermission> list = menuPermissionService.lambdaQuery()
//                .eq(MenuPermission::getDeleted, false)
//                .orderByAsc(MenuPermission::getSort)
//                .list();
        return Result.success(Collections.EMPTY_LIST);
    }
} 