package com.telecom.apigateway.controller;

import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/health")
@Tag(name = "health check", description = "health check")
public class Health {

    @GetMapping("/check")
    public Result<String> checkHealth() {
        log.info("health check");
        return Result.success("OK");
    }

    @GetMapping("/error")
    public Result<Void> checkError() {
        throw new BusinessException(ResultCodeEnum.TEST_ERROR);
    }
}
