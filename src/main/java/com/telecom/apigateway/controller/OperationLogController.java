package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.QueryOperationLogRequest;
import com.telecom.apigateway.model.vo.response.OperationLogResponse;
import com.telecom.apigateway.service.OperationLogBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 操作日志控制器
 */
@RestController
@RequestMapping("/api/operation-log")
@Tag(name = "操作日志接口")
@RequiredArgsConstructor
@CheckApiPermission("OPERATION_LOGS")
public class OperationLogController {

    private final OperationLogBusinessService operationLogBusinessService;

    /**
     * 分页查询操作日志
     *
     * @param request 查询请求
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询操作日志")
    public Result<Page<OperationLogResponse>> queryPage(@ModelAttribute QueryOperationLogRequest request) {
        Page<OperationLogResponse> page = operationLogBusinessService.queryPage(request);
        return Result.success(page);
    }
}
