package com.telecom.apigateway.controller;

import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.BasePageQueryRequest;
import com.telecom.apigateway.model.vo.request.RoleRequest;
import com.telecom.apigateway.model.vo.request.UpdateRoleRequest;
import com.telecom.apigateway.model.vo.response.MenuPermissionResponse;
import com.telecom.apigateway.model.vo.response.RoleDetailResponse;
import com.telecom.apigateway.model.vo.response.RoleResponse;
import com.telecom.apigateway.service.impl.RoleBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "角色管理")
@RestController
@RequestMapping("/api/role")
@RequiredArgsConstructor
public class RoleController {

    private final RoleBusinessService roleBusinessService;

    @Operation(summary = "创建角色")
    @PostMapping("/create")
    @CheckApiPermission("ACCESS_CONTROL")
    public Result<Void> createRole(@Validated @RequestBody RoleRequest request) {
        roleBusinessService.createRole(request);
        return Result.success(null);
    }

    @Operation(summary = "更新角色")
    @PostMapping("/update")
    @CheckApiPermission("ACCESS_CONTROL")
    public Result<Void> updateRole(@Validated @RequestBody UpdateRoleRequest request) {
        roleBusinessService.updateRole(request);
        return Result.success(null);
    }

    @Operation(summary = "删除角色")
    @PostMapping("/delete/{roleId}")
    @CheckApiPermission("ACCESS_CONTROL")
    public Result<Void> deleteRole(@PathVariable String roleId) {
        roleBusinessService.deleteRole(roleId);
        return Result.success(null);
    }

    @Operation(summary = "分页查询角色")
    @GetMapping("/page")
    @CheckApiPermission(value = {"USER_MANAGE", "ACCESS_CONTROL"}, mode = SaMode.OR)
    public Result<Page<RoleResponse>> pageRoles(BasePageQueryRequest request) {
        Page<RoleResponse> page = roleBusinessService.pageRoles(request);
        return Result.success(page);
    }

    @Operation(summary = "查询菜单权限列表")
    @GetMapping("/permission/menu")
    @CheckApiPermission("ACCESS_CONTROL")
    public Result<List<MenuPermissionResponse>> getMenuPermissions() {
        List<MenuPermissionResponse> result = roleBusinessService.getMenuPermissions();
        return Result.success(result);
    }

    @Operation(summary = "角色详情")
    @GetMapping("/detail")
    @CheckApiPermission("ACCESS_CONTROL")
    public Result<RoleDetailResponse> getDetail(String roleId) {
        RoleDetailResponse result = roleBusinessService.getDetail(roleId);
        return Result.success(result);
    }
} 