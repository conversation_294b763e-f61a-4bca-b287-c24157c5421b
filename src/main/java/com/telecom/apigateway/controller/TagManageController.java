package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.model.vo.response.AddTagResponse;
import com.telecom.apigateway.model.vo.response.ApplicationOptionResponse;
import com.telecom.apigateway.model.vo.response.TagOptionResponse;
import com.telecom.apigateway.model.vo.response.TagResponse;
import com.telecom.apigateway.service.TagBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Tag(name = "标签管理", description = "用于管理标签的相关操作")
@RestController
@RequestMapping("/api/tag")
@AllArgsConstructor
public class TagManageController {
    private final TagBusinessService tagBusinessService;
    // TODO userId
    private static final String userId = "admin";


    @PostMapping
    @Operation(summary = "新增标签")
    public Result<AddTagResponse> addTag(
            @RequestBody @Valid AddOrUpdateTag tag) {
        AddTagResponse response = tagBusinessService.addTag(userId, tag);
        return Result.success(response);
    }

    @GetMapping
    @Operation(
            summary = "模糊查询标签",
            description = "根据名称模糊查询标签"
    )
    public Result<Page<TagResponse>> getTagsLikeName(QueryTagRequest request) {
        Page<TagResponse> list = tagBusinessService.getTagsLikeName(request);
        return Result.success(list);
    }

    @GetMapping("/list")
    @Operation(
            summary = "获取标签列表",
            description = "分页获取所有标签"
    )
    public Result<Page<TagResponse>> getTags(BasePageQueryRequest request) {
        Page<TagResponse> list = tagBusinessService.getTags(request);
        return Result.success(list);
    }

    @DeleteMapping
    @Operation(
            summary = "删除标签",
            description = "删除指定的标签"
    )
    public Result<Void> deleteTag(@Valid DeleteTagRequest request) {
        tagBusinessService.deleteTag(request);
        return Result.success(null);
    }

    @DeleteMapping("/batch")
    @Operation(
            summary = "批量删除标签",
            description = "批量删除多个标签"
    )
    public Result<Void> batchDeleteTag(
            @RequestBody @Valid BatchDeleteTagRequest request) {
        tagBusinessService.deleteTags(request);
        return Result.success(null);
    }

    @PutMapping
    @Operation(
            summary = "更新标签",
            description = "更新指定的标签信息"
    )
    public Result<Void> updateTag(
            @RequestBody @Valid @Parameter(description = "更新标签信息", required = true) AddOrUpdateTag tag) {
        tagBusinessService.updateTag(tag);
        return Result.success(null);
    }

    @PostMapping("/unlink")
    @Operation(
            summary = "解除当前标签与所有API的关联",
            description = "解除指定标签与所有API的关联关系"
    )
    public Result<Void> unlinkCurrentTagAllApi(
            @RequestBody @Valid DeleteTagRequest request) {
        tagBusinessService.unlinkCurrentTagAllApi(request);
        return Result.success(null);
    }

    @PostMapping("/link")
    @Operation(
            summary = "关联API",
            description = "将标签与指定的API建立关联"
    )
    public Result<Void> linkApi(
            @RequestBody @Valid BatchLinkTagRequest request) {
        tagBusinessService.linkApi(userId, request);
        return Result.success(null);
    }

    @GetMapping("/option")
    @Operation(summary = "获取下拉框查询选项")
    public Result<List<TagOptionResponse>> option() {
        List<TagOptionResponse> result = tagBusinessService.getOption();
        return Result.success(result);
    }
}