package com.telecom.apigateway.controller;

import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.entity.IpInfo;
import com.telecom.apigateway.model.vo.response.IpInfoResponse;
import com.telecom.apigateway.service.IpInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024-11-15
 */
@RestController
@RequestMapping("/api/ip")
@Tag(name = "ip 信息接口")
@CheckApiPermission("ATTACKER_PORTRAIT")
public class IpInfoController {

    @Resource
    private IpInfoService ipInfoService;

    @Operation(summary = "ip 信息明细")
    @GetMapping("/{ip}/detail")
    public Result<IpInfoResponse> getDetail(@PathVariable String ip) {
        IpInfoResponse ipInfo = ipInfoService.getInfoById(ip);
        return Result.success(ipInfo);
    }

    @Operation(summary = "ip 信息 whois")
    @GetMapping("/{ip}/whois")
    public Result<String> getWhois(@PathVariable String ip) {
        String ipInfo = ipInfoService.getWhois(ip);
        return Result.success(ipInfo);
    }
}
