package com.telecom.apigateway.controller;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.telecom.apigateway.model.dto.LicenseInfo;
import com.telecom.apigateway.model.entity.License;
import com.telecom.apigateway.service.LicenseService;
import com.telecom.apigateway.utils.LicenseGenerator;
import com.telecom.apigateway.utils.LicenseValidator;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: APIWG-Service
 * @ClassName LicenseController
 * @description:
 * @author: Levi
 * @create: 2025-03-28 11:20
 * @Version 1.0
 **/

@RestController
@RequestMapping("/api/license")
public class LicenseController {

    private final LicenseGenerator licenseGenerator;
    private final LicenseValidator licenseValidator;
    @Resource
    private LicenseService licenseService;

    public LicenseController(LicenseGenerator licenseGenerator, LicenseValidator licenseValidator) {
        this.licenseGenerator = licenseGenerator;
        this.licenseValidator = licenseValidator;
    }

    @PostMapping("/generateToLongAndNotBeGuess")
    public ResponseEntity<String> generateLicense(@RequestParam("companyName") String companyName,
                                                  @RequestParam("expirationDays") int expirationDays,
                                                  @RequestParam("contactName") String contactName,
                                                  @RequestParam("contactPhone") String contactPhone,
                                                  @RequestParam("privateKey") String privateKey) {
        try {
            Date expirationDate = new Date(System.currentTimeMillis() + expirationDays * 24 * 60 * 60 * 1000);
            String licenseContent = licenseGenerator.generateLicenseContent(companyName, expirationDate, contactName, contactPhone, privateKey);
            return new ResponseEntity<>(licenseContent, HttpStatus.OK);
        } catch (Exception e) {
            System.out.println("generateToLongAndNotBeGuess类：生成授权文件失败");
            return new ResponseEntity<>("生成授权文件失败", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    @PostMapping("/checkLicense")
    public ResponseEntity<Map<String, Object>> checkLicense() {
        Map<String, Object> result = new HashMap<>();
        List<License> list = licenseService.list(new LambdaQueryWrapper<License>() .eq(License::getStatus, "0").orderByDesc(License::getExpirationDays).last("LIMIT 1") ) ;// 假设 status 是字符串类型  // 按过期天数降序 // 只取第一条（最大值）);
        if(CollectionUtils.isEmpty(list)){
            result.put("isValid", false);
            result.put("error", "未授权");
            return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }else{
            result.put("isValid", true);
            License license = list.get(0);
            license.setLicenseKey("");
            result.put("licenseInfo",license);
            return new ResponseEntity<>(result, HttpStatus.OK);
        }
    }
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateLicense(@RequestParam("licenseFile") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        try {
            String licenseContent = new String(file.getBytes());
            boolean isValid = licenseValidator.validateLicense(licenseContent);
            result.put("isValid", isValid);
            if (isValid) {//授权文件校验成功，就是说通过校验。
                LicenseInfo licenseInfo = licenseValidator.extractLicenseInfo(licenseContent);
                System.out.println("验证成功");
                List<License> list = licenseService.list();
                boolean exists = list.stream()
                        .anyMatch(license -> licenseContent.equals(license.getLicenseKey())); // 假设比较 licenseKey 字段

                System.out.println(exists ? "许可证已存在" : "许可证不存在");
                System.out.println(list);
                result.put("licenseInfo", licenseInfo);
                if(!exists){//许可证不存在才需要插入
                    License license = new License();
                    license.setLicenseKey(licenseContent);
                    license.setStatus("0");
                    license.setCreateTime( new Date());
                    license.setUpdateTime( new Date());
                    license.setCompanyName(licenseInfo.getCompanyName());
                    license.setContactName(licenseInfo.getContactName());
                    license.setContactPhone(licenseInfo.getContactPhone());
                    license.setExpirationDays(licenseInfo.getExpirationDate());
                    licenseService.save(license);
                }else{
                    License existingLicense = list.stream()
                            .filter(license -> licenseContent.equals(license.getLicenseKey()))
                            .findFirst()
                            .orElseThrow(() -> new IllegalStateException("许可证存在但未找到"));

// 获取当前时间和过期时间
                    Date now = new Date();
                    Date expirationDate = licenseInfo.getExpirationDate();

// 根据过期时间设置状态（0=未过期，1=已过期）
                    existingLicense.setStatus(expirationDate.before(now) ? "1" : "0");
                    existingLicense.setUpdateTime(now);
                    existingLicense.setExpirationDays(expirationDate);

// 保存更新
                    licenseService.updateById(existingLicense);
                    System.out.println("许可证信息已更新，状态：" +
                            (expirationDate.before(now) ? "已过期" : "未过期"));
                }

                return new ResponseEntity<>(result, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            result = new HashMap<>();
            result.put("isValid", false);
            result.put("error", "验证授权文件失败");
            return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}