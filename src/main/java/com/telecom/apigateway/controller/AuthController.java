package com.telecom.apigateway.controller;

import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.RefreshTokenRequest;
import com.telecom.apigateway.model.vo.request.UserLoginRequest;
import com.telecom.apigateway.model.vo.response.AuthResponse;
import com.telecom.apigateway.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 这个路由下所有接口都不需要 token 校验
 */
@Tag(name = "用户接口")
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<AuthResponse> login(@Validated @RequestBody UserLoginRequest request) {
        return Result.success(authService.login(request));
    }

    @Operation(summary = "刷新令牌")
    @PostMapping("/refresh-token")
    public Result<AuthResponse> refreshToken(@Validated @RequestBody RefreshTokenRequest request) {
        return Result.success(authService.refreshToken(request.getRefreshToken()));
    }
}
