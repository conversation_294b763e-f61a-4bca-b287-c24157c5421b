package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.dto.BlocklistDTO;
import com.telecom.apigateway.model.entity.Blocklist;
import com.telecom.apigateway.model.vo.request.BlocklistRequest;
import com.telecom.apigateway.model.vo.request.BlocklistQueryRequest;
import com.telecom.apigateway.model.vo.request.BlocklistResponse;
import com.telecom.apigateway.service.impl.BlocklistBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@RestController
@RequestMapping("/api/blocklist")
@Tag(name = "黑白名单管理", description = "用于管理黑白名单规则的相关操作")
@AllArgsConstructor
@CheckApiPermission("BLOCK_LIST")
public class BlocklistController {
    
    private final BlocklistBusinessService blocklistBusinessService;

    @GetMapping("/list")
    @Operation(summary = "分页查询")
    public Result<Page<BlocklistDTO>> list(@ModelAttribute BlocklistQueryRequest request) {
        Page<BlocklistDTO> result = blocklistBusinessService.list(request);
        return Result.success(result);
    }

    @PostMapping
    @Operation(summary = "添加规则")
    public Result<String> add(@RequestBody @Valid BlocklistRequest request) {
        Blocklist result = blocklistBusinessService.add(request);
        return Result.success(result.getBlockId());
    }

    @PostMapping("/{id}/enable")
    @Operation(summary = "更新状态")
    public Result<Void> updateStatus(
            @Parameter(description = "规则ID") @PathVariable String id,
            @Parameter(description = "状态") @RequestParam boolean status) {
        blocklistBusinessService.updateStatus(id, status);
        return Result.success(null);
    }

    @PostMapping("/batch-enable")
    @Operation(summary = "批量更新状态")
    public Result<Void> batchUpdateStatus(
            @Parameter(description = "规则ID列表") @RequestBody @NotEmpty List<String> ids,
            @Parameter(description = "状态") @RequestParam boolean status) {
        blocklistBusinessService.batchUpdateStatus(ids, status);
        return Result.success(null);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除规则")
    public Result<Void> delete(@Parameter(description = "规则ID") @PathVariable String id) {
        blocklistBusinessService.delete(id);
        return Result.success(null);
    }

    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除规则")
    public Result<Void> batchDelete(
            @Parameter(description = "规则ID列表") @RequestBody @NotEmpty List<String> ids) {
        blocklistBusinessService.batchDelete(ids);
        return Result.success(null);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取规则详情")
    public Result<BlocklistResponse> getDetail(@Parameter(description = "规则ID") @PathVariable String id) {
        BlocklistResponse result = blocklistBusinessService.getDetail(id);
        return Result.success(result);
    }

    @PostMapping("/update/{id}")
    @Operation(summary = "更新规则")
    public Result<String> update(
            @Parameter(description = "规则ID") @PathVariable String id,
            @RequestBody @Valid BlocklistRequest request) {
        Blocklist result = blocklistBusinessService.update(id, request);
        return Result.success(result.getBlockId());
    }
} 