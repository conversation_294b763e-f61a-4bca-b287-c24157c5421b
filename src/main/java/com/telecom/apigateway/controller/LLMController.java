package com.telecom.apigateway.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.entity.LLMLog;
import com.telecom.apigateway.model.vo.request.LLMRequest;
import com.telecom.apigateway.model.vo.request.UserLoginRequest;
import com.telecom.apigateway.model.vo.response.AuthResponse;
import com.telecom.apigateway.service.LLMLogService;
import com.telecom.apigateway.service.ThreatBusinessService;
import com.telecom.apigateway.utils.LLMUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.internal.util.UrlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;

/**
 * @program: APIWG-Service
 * @ClassName LLMController
 * @description:
 * @author: Levi
 * @create: 2025-01-22 15:27
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/llm")
@Tag(name = "大模型接口")
public class LLMController {
    @Resource
    private LLMLogService llmLogService;
    @Resource
    private  ThreatBusinessService threatBusinessService;

    @Operation(summary = "威胁日志智能分析")
    @PostMapping("/threatAnalysis")
    @CheckApiPermission(value = {"THREAT_LIST", "SENSITIVE_LIST"})
    public Object threatAnalysis(@RequestBody LLMRequest request, HttpServletRequest httpRequest) {
        return  llmLogService.theaatAnalysis(request,httpRequest);

    }
}