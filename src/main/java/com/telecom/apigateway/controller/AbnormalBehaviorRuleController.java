package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.AbRuleQueryRequest;
import com.telecom.apigateway.model.vo.request.AddAbRuleRequest;
import com.telecom.apigateway.model.vo.request.UpdateAbRuleRequest;
import com.telecom.apigateway.model.vo.request.UpdateSystemAbRuleRequest;
import com.telecom.apigateway.model.vo.response.QueryCustomAbrResponse;
import com.telecom.apigateway.model.vo.response.QuerySystemAbrResponse;
import com.telecom.apigateway.service.AbnormalBehaviorBizService;
import com.telecom.apigateway.service.AbnormalBehaviorRuleService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/api/abr")
@CheckApiPermission("ABNORMAL_MODE_SETTING")
public class AbnormalBehaviorRuleController {

    @Resource
    private AbnormalBehaviorRuleService abrService;
    @Resource
    private AbnormalBehaviorBizService abBizService;

    @Operation(summary = "添加规则")
    @PostMapping
    public Result<String> addRule(@RequestBody @Validated AddAbRuleRequest ruleRequest) {
        String ruleId = abBizService.addRule(ruleRequest);
        return Result.success(ruleId);
    }

    @Operation(summary = "修改规则")
    @PutMapping
    public Result<String> update(@RequestBody @Validated UpdateAbRuleRequest ruleRequest) {
        String ruleId = abBizService.updateRule(ruleRequest).getId();
        return Result.success(ruleId);
    }

    @Operation(summary = "修改系统规则")
    @PutMapping("/system")
    public Result<String> updateSystemRule(@RequestBody @Validated UpdateSystemAbRuleRequest ruleRequest) {
        String ruleId = abBizService.updateRule(ruleRequest).getId();
        return Result.success(ruleId);
    }

    @Operation(summary = "获取系统规则")
    @GetMapping("/list/system")
    public Result<List<QuerySystemAbrResponse>> listSystem() {
        List<QuerySystemAbrResponse> systemRules = abrService.getSystemRules();
        return Result.success(systemRules);
    }

    @Operation(summary = "获取自定义规则")
    @GetMapping("/list/custom")
    public Result<Page<QueryCustomAbrResponse>> listCustom(@ModelAttribute @Validated AbRuleQueryRequest request) {
        Page<QueryCustomAbrResponse> listCustom = abBizService.pageCustomRule(request);
        return Result.success(listCustom);
    }

    @Operation(summary = "启用规则")
    @PostMapping("/enable")
    public Result<List<String>> enable(@RequestBody Map<String, List<String>> req) {
        abBizService.enableRule(req.get("ruleId"));
        return Result.success(req.get("ruleId"));
    }

    @Operation(summary = "禁用规则")
    @PostMapping("/disable")
    public Result<List<String>> disable(@RequestBody Map<String, List<String>> req) {
        abBizService.disableRule(req.get("ruleId"));
        return Result.success(req.get("ruleId"));
    }

    @Operation(summary = "启用规则")
    @PutMapping("/enable")
    public Result<List<String>> enable(@RequestBody List<String> ruleIds) {
        abBizService.enableRule(ruleIds);
        return Result.success(ruleIds);
    }

    @Operation(summary = "禁用规则")
    @PutMapping("/disable")
    public Result<List<String>> disable(@RequestBody List<String> ruleIds) {
        abBizService.disableRule(ruleIds);
        return Result.success(ruleIds);
    }

    @Operation(summary = "启用规则")
    @PutMapping("/{id}/enable")
    public Result<String> enable(@PathVariable String id) {
        abBizService.enableRule(Collections.singletonList(id));
        return Result.success(id);
    }

    @Operation(summary = "禁用规则")
    @PutMapping("/{id}/disable")
    public Result<String> disable(@PathVariable String id) {
        abBizService.disableRule(Collections.singletonList(id));
        return Result.success(id);
    }

    @Operation(summary = "删除规则")
    @PostMapping("/delete")
    public Result<List<String>> delete(@RequestBody List<String> ruleIds) {
        abBizService.deleteRule(ruleIds);
        return Result.success(ruleIds);
    }
}
