package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.dto.NginxErrorLogDTO;
import com.telecom.apigateway.model.vo.request.QueryLogRequest;
import com.telecom.apigateway.model.vo.response.LogQueryResponse;
import com.telecom.apigateway.model.vo.response.StatCount;
import com.telecom.apigateway.model.vo.response.StatCountResponse;
import com.telecom.apigateway.service.LogBizService;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import com.telecom.apigateway.service.impl.NginxErrorLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/log")
@Tag(name = "日志接口")
public class LogController {

    @Resource
    private NginxErrorLogService errorLogService;
    @Resource
    private NginxAccessLogService nginxAccessLogService;
    @Resource
    private LogBizService logBizService;

    @Operation(summary = "访问日志查询")
    @GetMapping("/access/list")
    @CheckApiPermission("LOG_LIST")
    public Result<IPage<LogQueryResponse>> queryAccessLog(@ModelAttribute @Validated QueryLogRequest request) {
        IPage<LogQueryResponse> o = logBizService.queryPageLog(request);
        return Result.success(o);
    }

    @Operation(summary = "访问日志查询")
    @GetMapping("/access/page")
    @CheckApiPermission("LOG_LIST")
    public Result<IPage<LogQueryResponse>> queryPageAccessLog(@ModelAttribute QueryLogRequest request) {
        IPage<LogQueryResponse> o = logBizService.queryPageLog(request);
        return Result.success(o);
    }

    @Operation(summary = "访问日志查询")
    @GetMapping("/access/{logId}")
    @CheckApiPermission("LOG_LIST")
    public Result<LogQueryResponse> queryPageAccessLog(@PathVariable String logId ) {
        LogQueryResponse o = logBizService.detail(logId);
        return Result.success(o);
    }

    @Operation(summary = "错误日志查询")
    @GetMapping("/error/list")
    public Result<List<NginxErrorLogDTO>> queryErrorLog(@Parameter(name = "number", description = "查询数量") @RequestParam(required = false, defaultValue = "1000") Integer number,
                                                        @Parameter(name = "uri", description = "uri") @RequestParam(required = false) String uri,
                                                        @Parameter(name = "range", description = "时间范围") @RequestParam(required = false) Integer range,
                                                        @Parameter(name = "appId", description = "应用id") @RequestParam(required = false) Integer appId,
                                                        @Parameter(name = "httpMethod", description = "http方法") @RequestParam(required = false) String httpMethod,
                                                        @Parameter(name = "statusCode", description = "状态码") @RequestParam(required = false) Integer statusCode,
                                                        @Parameter(name = "sourceIp", description = "源ip") @RequestParam(required = false) String sourceIp,
                                                        @Parameter(name = "serverIp", description = "目标ip") @RequestParam(required = false) String serverIp,
                                                        @Parameter(name = "startTime", description = "起始时间") @RequestParam(required = false) String startTime,
                                                        @Parameter(name = "endTime", description = "终止时间") @RequestParam(required = false) String endTime) {
        List<NginxErrorLogDTO> data = errorLogService.queryErrorLog(uri, range, number);

        return Result.success(data);
    }

    @Operation(summary = "访问次数查询")
    @GetMapping("/access/count")
    @CheckApiPermission(value = {"SCREEN"})
    public Result<Integer> statAccessCount(@Parameter(name = "startTime", description = "起始时间")
                                           @RequestParam(required = false) @DateTimeFormat(pattern =
                                                   Constant.DATE_TIME_PATTERN) LocalDateTime startTime,
                                           @Parameter(name = "endTime", description = "终止时间")
                                           @RequestParam(required = false) @DateTimeFormat(pattern =
                                                   Constant.DATE_TIME_PATTERN) LocalDateTime endTime,
                                           @Parameter(name = "appId", description = "appId")
                                           @RequestParam(required = false) String appId) {
        int count = nginxAccessLogService.count(appId, startTime, endTime);
        return Result.success(count);
    }

    @Operation(summary = "访问趋势")
    @GetMapping("/access/stat/trend")
    @CheckApiPermission(value = {"SCREEN"})
    public Result<List<StatCountResponse>> statAccessTrend(@Parameter(name = "startTime", description = "起始时间")
                                                           @RequestParam(required = false) @DateTimeFormat(pattern =
                                                                   Constant.DATE_TIME_PATTERN) LocalDateTime startTime,
                                                           @Parameter(name = "endTime", description = "终止时间")
                                                           @RequestParam(required = false) @DateTimeFormat(pattern =
                                                                   Constant.DATE_TIME_PATTERN) LocalDateTime endTime,
                                                           @Parameter(name = "appId", description = "appId")
                                                           @RequestParam(required = false) String appId) {
        List<StatCountResponse> list = nginxAccessLogService.statTrend(appId, startTime, endTime);
        return Result.success(list);
    }

    @Operation(summary = "响应码统计")
    @GetMapping("/access/stat/status-code")
    @CheckApiPermission(value = {"SCREEN"})
    public Result<List<StatCount>> statStatusCode(@Parameter(name = "startTime", description = "起始时间")
                                                  @RequestParam(required = false) @DateTimeFormat(pattern =
                                                          Constant.DATE_TIME_PATTERN) LocalDateTime startTime,
                                                  @Parameter(name = "endTime", description = "终止时间")
                                                  @RequestParam(required = false) @DateTimeFormat(pattern =
                                                          Constant.DATE_TIME_PATTERN) LocalDateTime endTime,
                                                  @Parameter(name = "appId", description = "appId")
                                                  @RequestParam(required = false) String appId) {
        String apiId = null;
        List<StatCount> list = nginxAccessLogService.statStatusCode(appId, apiId, startTime, endTime);
        return Result.success(list);
    }

    @Operation(summary = "访客数")
    @GetMapping("/access/stat/visit-count")
    @CheckApiPermission(value = {"SCREEN"})
    public Result<Integer> statVisitCount(@Parameter(name = "startTime", description = "起始时间")
                                          @RequestParam(required = false) @DateTimeFormat(pattern =
                                                  Constant.DATE_TIME_PATTERN) LocalDateTime startTime,
                                          @Parameter(name = "endTime", description = "终止时间")
                                          @RequestParam(required = false) @DateTimeFormat(pattern =
                                                  Constant.DATE_TIME_PATTERN) LocalDateTime endTime,
                                          @Parameter(name = "appId", description = "appId")
                                          @RequestParam(required = false) String appId) {
        String apiId = null;
        int count = nginxAccessLogService.statVisitCount(appId, apiId, startTime, endTime);
        return Result.success(count);
    }
}
