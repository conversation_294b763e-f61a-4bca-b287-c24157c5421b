package com.telecom.apigateway.controller;

import cn.dev33.satoken.annotation.SaMode;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.CreateIpGroupRequest;
import com.telecom.apigateway.model.vo.request.JoinIpGroupRequest;
import com.telecom.apigateway.model.vo.request.UpdateIpGroupRequest;
import com.telecom.apigateway.model.vo.response.IpGroupDetailResponse;
import com.telecom.apigateway.model.vo.response.IpGroupResponse;
import com.telecom.apigateway.service.IpGroupBusinessService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/ip-group")
@AllArgsConstructor
@CheckApiPermission("IP_GROUP")
public class IpGroupController {
    private final IpGroupBusinessService ipGroupBusinessService;

    @PostMapping()
    @CheckApiPermission("PROTECTION_CONFIG")
    public Result<String> create(@RequestBody @Valid CreateIpGroupRequest request) {
        String ipGroupId = ipGroupBusinessService.createIpGroup(request);
        return Result.success(ipGroupId);
    }

    @GetMapping
    @CheckApiPermission(value = {"PROTECTION_CONFIG", "BLOCK_LIST"}, mode = SaMode.OR)
    public Result<List<IpGroupResponse>> list() {
        List<IpGroupResponse> groups = ipGroupBusinessService.list();
        return Result.success(groups);
    }

    @GetMapping("/detail/{groupId}")
    @CheckApiPermission("PROTECTION_CONFIG")
    public Result<IpGroupDetailResponse> detail(@PathVariable String groupId) {
        IpGroupDetailResponse detail = ipGroupBusinessService.detail(groupId);
        return Result.success(detail);
    }

    @PostMapping("/update")
    @CheckApiPermission("PROTECTION_CONFIG")
    public Result<String> update(@RequestBody @Valid UpdateIpGroupRequest request) {
        UpdateIpGroupRequest ipGroup = ipGroupBusinessService.update(request);
        return Result.success(ipGroup.getGroupId());
    }

    @DeleteMapping("/{groupId}")
    @CheckApiPermission("PROTECTION_CONFIG")
    public Result<Void> delete(@PathVariable String groupId) {
        ipGroupBusinessService.delete(groupId);
        return Result.success(null);
    }

    @GetMapping("/query")
    @CheckApiPermission(value = {"PROTECTION_CONFIG"}, mode = SaMode.OR)
    public Result<List<IpGroupResponse>> query(@RequestParam String ip) {
        List<IpGroupResponse> groups = ipGroupBusinessService.query(ip);
        return Result.success(groups);
    }

    @PostMapping("/add-ip")
    @CheckApiPermission("PROTECTION_CONFIG")
    public Result<Void> addIp(@RequestBody JoinIpGroupRequest request) {
        ipGroupBusinessService.addIp(request);
        return Result.success(null);
    }
}
