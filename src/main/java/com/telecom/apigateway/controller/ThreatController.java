package com.telecom.apigateway.controller;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.enums.RecordTypeEnum;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.model.vo.response.*;
import com.telecom.apigateway.service.ThreatBusinessService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@RestController
@AllArgsConstructor
@RequestMapping("/api/threat")
@CheckApiPermission("THREAT_LIST")
public class ThreatController {

    private final ThreatBusinessService threatBusinessService;

    @GetMapping
    @CheckApiPermission("SCREEN")
    public Result<Page<QueryThreatResponse>> pageOfThreat(@ModelAttribute QueryThreatRequest query) {
        Page<QueryThreatResponse> result = threatBusinessService.query(query);
        return Result.success(result);
    }

    @GetMapping("/export")
    public void exportExcel(QueryThreatRequest request, HttpServletResponse response) throws IOException {
        List<ThreatExcelResponse> export = threatBusinessService.export(request);

        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("uri", "被攻击API");
        writer.addHeaderAlias("belongApplication", "所属应用");
        writer.addHeaderAlias("attackType", "威胁类型");
        writer.addHeaderAlias("dealtName", "拦截状态");
        writer.addHeaderAlias("clientIp", "攻击者源IP");
        writer.addHeaderAlias("clientPort", "源端口");
        writer.addHeaderAlias("clientRegion", "源IP所属地");
        writer.addHeaderAlias("targetAddress", "被攻击地址");
//        writer.addHeaderAlias("targetRegion", "攻击目标区域");
        writer.addHeaderAlias("attackedTime", "攻击时间");
        writer.addHeaderAlias("riskLevelName", "威胁等级");
        writer.addHeaderAlias("logId", "日志id");

        if (RecordTypeEnum.FALSE_POSITIVE.equals(request.getRecordType())) {
            writer.addHeaderAlias("falsePositiveReason", "误报原因");
            writer.addHeaderAlias("processSuggestion", "修复建议");
            writer.addHeaderAlias("attackedTime", "误报时间");
            writer.addHeaderAlias("requestLog", "原始日志(请求)");
            writer.addHeaderAlias("responseLog", "原始日志(响应)");
        }

        // 默认的，未添加alias的属性也会写出，如果想只写出加了别名的字段，可以调用此方法排除之
        writer.setOnlyAlias(true);

        writer.write(export, true);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        String timeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
        String filename = "威胁API列表_" + timeStr +  ".xlsx";
        //        The Unicode character [威] at code point [23,041] cannot be encoded as it is outside the permitted range of 0 to 255
        response.setHeader("Content-Disposition","attachment;filename="+ URLEncoder.encode(filename, "UTF-8"));

        writer.flush(response.getOutputStream());
        writer.close();
    }

    @GetMapping("/{riskId}")
    public Result<ThreatDetailResponse> threatDetail(@PathVariable String riskId) {
        ThreatDetailResponse detail = threatBusinessService.getByRiskId(riskId);
        return Result.success(detail);
    }

    @GetMapping("/options")
    @CheckApiPermission(exclude = true)
    public Result<List<AttackTypeQueryResponse>> options() {
        List<AttackTypeQueryResponse> options = threatBusinessService.options();
        return Result.success(options);
    }

    @GetMapping("/count")
    @CheckApiPermission(value = {"SCREEN", "RISK_OVERVIEW"})
    public Result<Map<String, List<? extends BaseLabelResponse>>> count(@ModelAttribute ThreatCountRequest request) {
        Map<String, List<? extends BaseLabelResponse>> result = threatBusinessService.threatCount(request);
        return Result.success(result);
    }

    @PostMapping("/false-positive-report")
    public Result<Void> falsePositiveReport(@RequestBody @Valid FalsePositiveAndAccessListRequest request) {
        threatBusinessService.falsePositiveReport(request);
        return Result.success(null);
    }

    @PostMapping("/false-positive/{logId}/cancel")
    public Result<Void> falsePositiveCancel(@PathVariable String logId) {
        threatBusinessService.falsePositiveCancel(logId);
        return Result.success(null);
    }

    @PostMapping("/false-positive/batch-report")
    public Result<Void> falsePositiveBatchReport(@RequestBody @Valid BatchFalsePositiveAndAccessListRequest request) {
        threatBusinessService.falsePositiveBatchReport(request);
        return Result.success(null);
    }
}
