package com.telecom.apigateway.controller;

import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.entity.SensitiveRule;
import com.telecom.apigateway.model.entity.SensitiveWhiteList;
import com.telecom.apigateway.model.vo.request.QuerySensitiveRuleRequest;
import com.telecom.apigateway.model.vo.response.SensitiveRuleLessResponse;
import com.telecom.apigateway.service.SensitiveBizService;
import com.telecom.apigateway.service.SensitiveRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/api/sensitive-rule")
@Tag(name = "涉敏规则接口")
@CheckApiPermission("SENSITIVE_CONFIG")
public class SensitiveRuleController {

    @Resource
    private SensitiveRuleService sensitiveRuleService;
    @Resource
    private SensitiveBizService sensitiveBizService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public Result<IPage<SensitiveRule>> queryPage(@ModelAttribute @Validated QuerySensitiveRuleRequest request) {
        IPage<SensitiveRule> page = sensitiveRuleService.queryPage(request);
        return Result.success(page);
    }

    @Operation(summary = "新增规则")
    @PostMapping("/add")
    public Result<String> add(@RequestBody SensitiveRule request) {
        sensitiveRuleService.add(request);
        return Result.success("success");
    }

    @Operation(summary = "修改规则")
    @PostMapping("/update")
    public Result<String> update(@RequestBody SensitiveRule request) {
        sensitiveBizService.updateAndResetLevel(request);
        return Result.success("success");
    }

    @Operation(summary = "删除规则")
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable String id) {
        sensitiveBizService.deleteRule(id);
        return Result.success("success");
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch")
    public Result<String> batchDelete(@RequestParam(required = false) List<String> ids) {
        sensitiveBizService.deleteRules(ids);
        return Result.success("success");
    }

    @Operation(summary = "启用规则")
    @PostMapping("/{id}/enable")
    public Result<String> enable(@PathVariable String id) {
        sensitiveRuleService.enable(id);
        return Result.success("success");
    }

    @Operation(summary = "批量启用规则")
    @PostMapping("/enable/batch")
    public Result<String> batchEnable(@RequestBody(required = false) String[] ids) {
        sensitiveRuleService.enable(ids);
        return Result.success("success");
    }

    @Operation(summary = "禁用规则")
    @PostMapping("/{id}/disable")
    public Result<String> disable(@PathVariable String id) {
        sensitiveRuleService.disable(id);
        return Result.success("success");
    }

    @Operation(summary = "批量禁用规则")
    @PostMapping("/disable/batch")
    public Result<String> batchDisable(@RequestBody(required = false) String[] ids) {
        sensitiveRuleService.disable(ids);
        return Result.success("success");
    }

    @Operation(summary = "规则列表(简要)")
    @GetMapping("/list/less")
    @CheckApiPermission(value = {"API_LIST", "LOG_LIST", "SENSITIVE_LIST", "ABNORMAL_MODE_SETTING"}, mode = SaMode.OR)
    public Result<List<SensitiveRuleLessResponse>> getNameList(@RequestParam(required = false) @Parameter(description = "模糊查询关键词") String name) {
        List<SensitiveRuleLessResponse> list = sensitiveRuleService.queryName(name);
        return Result.success(list);
    }

    @Operation(summary = "新增白名单 api")
    @PostMapping("/{id}/add-whitelist-api")
    public Result<String> addWhitelistApi(@PathVariable String id, @RequestBody String[] apiIds) {
        sensitiveRuleService.addWhitelistApi(id, apiIds);
        return Result.success("success");
    }

    @Operation(summary = "获取白名单 api")
    @GetMapping("/{id}/whitelist")
    public Result<List<SensitiveWhiteList>> getWhiteList(@PathVariable String id) {
        List<SensitiveWhiteList> whiteList = sensitiveRuleService.getWhiteList(id);
        return Result.success(whiteList);
    }
}
