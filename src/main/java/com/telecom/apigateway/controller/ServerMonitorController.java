package com.telecom.apigateway.controller;

import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.entity.ServerNode;
import com.telecom.apigateway.model.vo.ServerMonitorVO;
import com.telecom.apigateway.model.vo.request.UpdateServerNodeRequest;
import com.telecom.apigateway.service.ServerMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 服务器监控控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/server")
@Api(tags = "服务器监控")
@CheckApiPermission("NODE_MANAGE")
public class ServerMonitorController {

    @Autowired
    private ServerMonitorService serverMonitorService;

    /**
     * 手动添加节点接口，通常不需要使用，系统会自动添加节点
     */
    @PostMapping("/node/manual")
    @ApiOperation(value = "手动添加服务器节点", notes = "通常不需要使用，系统会自动添加节点")
    public Result<Boolean> addServerNodeManually(@RequestBody ServerNode serverNode) {
        boolean result = serverMonitorService.addServerNode(serverNode);
        return Result.success(result);
    }

    @PutMapping("/node")
    @ApiOperation("更新服务器节点信息")
    public Result<Boolean> updateServerNode(@RequestBody @Valid UpdateServerNodeRequest request) {
        boolean result = serverMonitorService.updateServerNode(request);
        return Result.success(result);
    }

    @DeleteMapping("/node/{id}")
    @ApiOperation("删除服务器节点")
    public Result<Boolean> deleteServerNode(@PathVariable Long id) {
        boolean result = serverMonitorService.deleteServerNodeById(id);
        return Result.success(result);
    }

    @GetMapping("/node/list")
    @ApiOperation("获取所有服务器节点")
    public Result<List<ServerNode>> getAllServerNodes() {
        List<ServerNode> nodes = serverMonitorService.getAllServerNodes();
        return Result.success(nodes);
    }

    @GetMapping("/monitor/list")
    @ApiOperation("获取所有服务器监控信息")
    public Result<List<ServerMonitorVO>> getAllServerMonitorInfo() {
        List<ServerMonitorVO> monitorData = serverMonitorService.getAllServerMonitorInfo();
        return Result.success(monitorData);
    }

    @GetMapping("/monitor/{hostId}")
    @ApiOperation("根据主机ID获取监控信息")
    public Result<ServerMonitorVO> getServerMonitorInfoByHostId(@PathVariable String hostId) {
        ServerMonitorVO monitorData = serverMonitorService.getServerMonitorInfoByHostId(hostId);
        return Result.success(monitorData);
    }
} 