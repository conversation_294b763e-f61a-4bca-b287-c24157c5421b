package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.QueryAbRequest;
import com.telecom.apigateway.model.vo.response.QueryAbResponse;
import com.telecom.apigateway.service.AbnormalBehaviorBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/api/ab")
@Tag(name = "行为清单")
@CheckApiPermission("ABNORMAL_LIST")
public class AbnormalBehaviorController {

    @Resource
    private AbnormalBehaviorBizService abnormalBehaviorBizService;


    @Operation(summary = "分页行为清单")
    @GetMapping
    public Result<Page<QueryAbResponse>> queryPage(@ModelAttribute @Validated QueryAbRequest request) {
        Page<QueryAbResponse> data = abnormalBehaviorBizService.pageList(request);
        return Result.success(data);
    }


    @Operation(summary = "解封")
    @PostMapping("/trigger/{triggerId}/disable")
    public Result<String> disableTrigger(@PathVariable String triggerId) {
        abnormalBehaviorBizService.disableTrigger(triggerId);
        return Result.success(triggerId);
    }

}
