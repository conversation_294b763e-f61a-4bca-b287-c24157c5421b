package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.BatchConfigCveRuleRequest;
import com.telecom.apigateway.model.vo.request.ConfigCveRuleRequest;
import com.telecom.apigateway.model.vo.request.DeleteCveRuleRequest;
import com.telecom.apigateway.model.vo.request.QueryCveRuleRequest;
import com.telecom.apigateway.model.vo.response.QueryCveRuleResponse;
import com.telecom.apigateway.service.CveRuleBusinessService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

import static com.telecom.apigateway.common.Result.success;

@RestController
@RequestMapping("/api/cve/rule")
@RequiredArgsConstructor
@CheckApiPermission("PROTECTION_CONFIG")
public class CveRuleController {

    private final CveRuleBusinessService cveRuleBusinessService;

    @PostMapping("/check")
    public Result<Boolean> checkCveRule(@RequestParam("files") MultipartFile[] files) {
        cveRuleBusinessService.checkCveRule(files);
        return Result.success(true);
    }

    @PostMapping("/import")
    public Result<Void> importCveRule(@RequestParam("files") MultipartFile[] files) {
        cveRuleBusinessService.saveCveRule(files);
        return Result.success(null);
    }

    @GetMapping("/query")
    public Result<Page<QueryCveRuleResponse>> queryCveRule(QueryCveRuleRequest request) {
        Page<QueryCveRuleResponse> queryCveRuleResponses = cveRuleBusinessService.queryCveRule(request);
        return Result.success(queryCveRuleResponses);
    }

    @GetMapping("/detail")
    public Result<String> queryCveRuleDetail(String cveId) {
        String content = cveRuleBusinessService.queryCveRuleDetail(cveId);
        return Result.success(content);
    }

    @PostMapping("/config")
    public Result<Void> configCveRule(@RequestBody ConfigCveRuleRequest request) {
        cveRuleBusinessService.configCveRule(request);
        return Result.success(null);
    }

    @PostMapping("/delete")
    public Result<Void> deleteCveRule(@RequestBody DeleteCveRuleRequest request) {
        cveRuleBusinessService.deleteCveRule(request);
        return Result.success(null);
    }

    @PostMapping("/config/batch")
    public Result<Void> batchConfigCveRule(@RequestBody BatchConfigCveRuleRequest request) {
        // 日志复用，有性能问题再优化
        request.getCveIds()
                .stream()
                .map((id) -> new ConfigCveRuleRequest(id, request.getConfigType()))
                .forEach(cveRuleBusinessService::configCveRule)
        ;
        return Result.success(null);
    }
}
