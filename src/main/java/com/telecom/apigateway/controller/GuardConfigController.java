package com.telecom.apigateway.controller;

import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.GuardAllConfigRequest;
import com.telecom.apigateway.model.vo.request.GuardConfigRequest;
import com.telecom.apigateway.model.vo.response.GuardResponse;
import com.telecom.apigateway.service.GuardConfigService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/guard")
@AllArgsConstructor
public class GuardConfigController {
    private final GuardConfigService guardConfigService;

    @GetMapping("/list")
    @CheckApiPermission("PROTECTION_CONFIG")
    public Result<List<GuardResponse>> list() {
        List<GuardResponse> responses = guardConfigService.list();
        return Result.success(responses);
    }

    @PostMapping("/config")
    @CheckApiPermission("PROTECTION_CONFIG")
    public Result<Void> config(@RequestBody GuardConfigRequest request) {
        guardConfigService.config(request);
        return Result.success(null);
    }

    @PostMapping("/config/all")
    @CheckApiPermission("PROTECTION_CONFIG")
    public Result<Void> configAll(@RequestBody GuardAllConfigRequest request) {
        guardConfigService.configAll(request.getType());
        return Result.success(null);
    }
}
