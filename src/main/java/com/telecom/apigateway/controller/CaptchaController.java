package com.telecom.apigateway.controller;


import cloud.tianai.captcha.application.ImageCaptchaApplication;
import cloud.tianai.captcha.application.vo.CaptchaResponse;
import cloud.tianai.captcha.application.vo.ImageCaptchaVO;
import cloud.tianai.captcha.cache.CacheStore;
import cloud.tianai.captcha.cache.impl.LocalCacheStore;
import cloud.tianai.captcha.common.AnyMap;
import cloud.tianai.captcha.common.constant.CaptchaTypeConstant;
import cloud.tianai.captcha.common.response.ApiResponse;
import cloud.tianai.captcha.spring.plugins.secondary.SecondaryVerificationApplication;

import cloud.tianai.captcha.validator.common.model.dto.ImageCaptchaTrack;
import com.telecom.apigateway.common.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/captcha")
public class CaptchaController {


    @Resource
    private ImageCaptchaApplication imageCaptchaApplication;
    @Resource
    private  CacheStore cacheStore = new LocalCacheStore();

    @RequestMapping("/gen")
    @ResponseBody
    public CaptchaResponse<ImageCaptchaVO> genCaptcha(HttpServletRequest request, @RequestParam(value = "type", required = false)String type) {
        if (StringUtils.isBlank(type)) {
            type = CaptchaTypeConstant.SLIDER;
        }
        if ("RANDOM".equals(type)) {
            int i = ThreadLocalRandom.current().nextInt(0, 4);
            if (i == 0) {
                type = CaptchaTypeConstant.SLIDER;
            } else if (i == 1) {
                type = CaptchaTypeConstant.CONCAT;
            } else if (i == 2) {
                type = CaptchaTypeConstant.ROTATE;
            } else{
                type = CaptchaTypeConstant.WORD_IMAGE_CLICK;
            }
        }
        CaptchaResponse<ImageCaptchaVO> response = imageCaptchaApplication.generateCaptcha(type);
        return response;
    }

    @PostMapping("/check")
    @ResponseBody
    public ApiResponse<?> checkCaptcha(@RequestBody Data data,
                                     HttpServletRequest request) {
        ApiResponse<?> response = imageCaptchaApplication.matching(data.getId(), data.getData());
        if (response.isSuccess()) {

            String tokenCaptcha = UUID.randomUUID().toString().replace("-", "");
            AnyMap anyMap = new AnyMap(Collections.singletonMap(tokenCaptcha, data.getId()));
            cacheStore.setCache(tokenCaptcha,anyMap,1L, TimeUnit.MINUTES);
            System.out.println("** 验证码正确："+tokenCaptcha+"-"+data.getId()+"-"+ response.getMsg() +"-"+ response.getData());
            return ApiResponse.ofSuccess(Collections.singletonMap("id", tokenCaptcha));

        }
        System.out.println("## 验证码错误："+data.getId()+"-"+ response.getMsg() +"-"+ response.getData());
        return ApiResponse.ofError("验证码错误");
    }

    @lombok.Data
    public static class Data {
        private String  id;
        private ImageCaptchaTrack data;
    }


}
