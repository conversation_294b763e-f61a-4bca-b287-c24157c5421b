package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.model.vo.response.*;
import com.telecom.apigateway.service.impl.UserBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/user")
@AllArgsConstructor
public class UserManageController {

    private final UserBusinessService userBusinessService;
    private RedisTemplate<String, String> redisTemplate;

    @GetMapping("/who-am-i")
    public Result<UserLoginResponse> whoAmI() {
        UserLoginResponse userLoginResponse = userBusinessService.whoAmI();
        return Result.success(userLoginResponse);
    }

    @Operation(summary = "退出登录")
    @PostMapping("/logout")
    public Result<Boolean> logout() {
        userBusinessService.logout();
        return Result.success(true);
    }

    @Operation(summary = "创建用户")
    @PostMapping("/create")
    @CheckApiPermission("USER_MANAGE")
    public Result<Boolean> createUser(@Validated @RequestBody CreateUserRequest request) {
        userBusinessService.createUser(request);
        return Result.success(true);
    }

    @Operation(summary = "重置密码")
    @PostMapping("/reset-password")
    @CheckApiPermission("USER_MANAGE")
    public Result<Boolean> resetPassword(@Validated @RequestBody BaseUserRequest request) {
        userBusinessService.resetPassword(request.getUsername());
        return Result.success(true);
    }

    @Operation(summary = "删除用户")
    @PostMapping("/delete")
    @CheckApiPermission("USER_MANAGE")
    public Result<Boolean> deleteUser(@Validated @RequestBody BaseUserRequest request) {
        userBusinessService.deleteUser(request.getUsername());
        return Result.success(true);
    }

    @Operation(summary = "禁用用户")
    @PostMapping("/lock")
    @CheckApiPermission("USER_MANAGE")
    public Result<Boolean> lockUser(@Validated @RequestBody BaseUserRequest request) {
        userBusinessService.lockUser(request.getUsername());
        return Result.success(true);
    }

    @Operation(summary = "解封用户")
    @PostMapping("/unlock")
    @CheckApiPermission("USER_MANAGE")
    public Result<Boolean> unlockUser(@Validated @RequestBody BaseUserRequest request) {
        String username = request.getUsername();
        userBusinessService.unlockUser(username);
        redisTemplate.delete("login:fail:" + username);
        return Result.success(true);
    }

    @Operation(summary = "用户概览")
    @GetMapping("/overview")
    @CheckApiPermission("USER_MANAGE")
    public Result<List<BaseLabelResponse>> overview() {
        List<BaseLabelResponse> overview = userBusinessService.overview();
        return Result.success(overview);
    }

    @Operation(summary = "账号状态")
    @GetMapping("/status")
    @CheckApiPermission("USER_MANAGE")
    public Result<List<BaseLabelResponse>> status() {
        List<BaseLabelResponse> overview = userBusinessService.status();
        return Result.success(overview);
    }

    @Operation(summary = "分页查询")
    @GetMapping("/query")
    @CheckApiPermission("USER_MANAGE")
    public Result<Page<QueryUserResponse>> query(QueryUserRequest request) {
        Page<QueryUserResponse> query = userBusinessService.query(request);
        return Result.success(query);
    }

    @Operation(summary = "查询部门")
    @GetMapping("/query/department")
    @CheckApiPermission("USER_MANAGE")
    public Result<List<String>> department() {
        List<String> query = userBusinessService.department();
        return Result.success(query);
    }

    @Operation(summary = "查询用户详情")
    @GetMapping("/detail")
    @CheckApiPermission("USER_MANAGE")
    public Result<UserDetailResponse> detail(String username) {
        UserDetailResponse query = userBusinessService.getByUsername(username);
        return Result.success(query);
    }

    @Operation(summary = "修改密码")
    @PostMapping("/update-password")
    public Result<Boolean> updatePwd(@Validated @RequestBody UpdatePwdRequest request) {
        userBusinessService.updatePwd(request);
        return Result.success(true);
    }

    @Operation(summary = "编辑用户")
    @PostMapping("/update")
    @CheckApiPermission("USER_MANAGE")
    public Result<Boolean> updateUser(@Validated @RequestBody UpdateUserRequest request) {
        userBusinessService.updateUser(request);
        return Result.success(true);
    }
}
