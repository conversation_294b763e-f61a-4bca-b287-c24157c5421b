package com.telecom.apigateway.controller;

import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.dto.HistoryMergeTaskDTO;
import com.telecom.apigateway.service.HistoryMergeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * 历史数据合并控制器
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/api/history/merge")
@Tag(name = "历史数据合并")
@Slf4j
@RequiredArgsConstructor
public class HistoryMergeCont {

    private final HistoryMergeService historyMergeService;

    /**
     * 启动历史数据合并任务
     * 这是一个异步接口，前端需要轮询查询任务状态
     */
    @Operation(summary = "启动历史数据合并任务")
    @PostMapping
    public Result<String> historyMerge() throws ExecutionException, InterruptedException {
        if (historyMergeService.hasRunningTask()) {
            throw new BusinessException("合并任务正在执行中，请等待完成后再试");
        }

        // 异步执行合并任务
        CompletableFuture<String> future = historyMergeService.historyMerge();
        String taskId = future.get();

        return Result.success(taskId);
    }

    /**
     * 获取最新任务状态
     * 前端可以通过此接口获取最新的合并任务状态
     */
    @Operation(summary = "获取最新任务状态")
    @GetMapping("/status/latest")
    public Result<HistoryMergeTaskDTO> getLatestTaskStatus() {
        HistoryMergeTaskDTO task = historyMergeService.getLatestTask();
        if (task == null) {
            return Result.success(null);
        }

        return Result.success(task);
    }
}
