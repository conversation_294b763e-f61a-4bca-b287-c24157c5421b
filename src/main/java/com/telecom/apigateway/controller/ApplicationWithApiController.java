package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.UnknownInterfaceRequest;
import com.telecom.apigateway.model.vo.response.UnknownInterfaceResponse;
import com.telecom.apigateway.service.ApplicationBusinessService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/application/interface")
@AllArgsConstructor
public class ApplicationWithApiController {
    private final ApplicationBusinessService applicationBusinessService;

    @GetMapping("/unknown")
    @CheckApiPermission("APP_LIST")
    Result<Page<UnknownInterfaceResponse>> getUnknownApi(UnknownInterfaceRequest request) {
        Page<UnknownInterfaceResponse> unknownInterfaceResponsePage = applicationBusinessService.queryUncategorizedApisByParentId(request);
        return Result.success(unknownInterfaceResponsePage);
    }
}
