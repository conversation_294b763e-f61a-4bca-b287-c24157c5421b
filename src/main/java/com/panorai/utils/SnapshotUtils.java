package com.panorai.utils;

import com.panorai.model.entity.Snapshot;
import com.panorai.service.SnapshotService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 快照工具类
 * 提供便捷的快照操作方法
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SnapshotUtils {
    
    private final SnapshotService snapshotService;
    
    /**
     * 记录更新前快照
     * @param tableName 表名
     * @param originalData 原始数据
     * @param userId 用户ID
     * @param request HTTP请求对象
     * @return 快照实体
     */
    public Snapshot recordUpdateSnapshot(String tableName, Object originalData, String userId, HttpServletRequest request) {
        try {
            String ipAddress = RequestUtil.getClientIpAddress(request);
            String userAgent = RequestUtil.getUserAgent(request);
            
            return snapshotService.createUpdateSnapshot(tableName, originalData, userId, ipAddress, userAgent);
        } catch (Exception e) {
            log.error("记录更新前快照失败 - 表名: {}, 用户ID: {}, 错误: {}", tableName, userId, e.getMessage(), e);
            // 不抛出异常，避免影响主业务流程
            return null;
        }
    }
    
    /**
     * 记录删除前快照
     * @param tableName 表名
     * @param originalData 原始数据
     * @param userId 用户ID
     * @param request HTTP请求对象
     * @return 快照实体
     */
    public Snapshot recordDeleteSnapshot(String tableName, Object originalData, String userId, HttpServletRequest request) {
        try {
            String ipAddress = RequestUtil.getClientIpAddress(request);
            String userAgent = RequestUtil.getUserAgent(request);
            
            return snapshotService.createDeleteSnapshot(tableName, originalData, userId, ipAddress, userAgent);
        } catch (Exception e) {
            log.error("记录删除前快照失败 - 表名: {}, 用户ID: {}, 错误: {}", tableName, userId, e.getMessage(), e);
            // 不抛出异常，避免影响主业务流程
            return null;
        }
    }
    
    /**
     * 记录更新前快照（无HTTP请求上下文）
     * @param tableName 表名
     * @param originalData 原始数据
     * @param userId 用户ID
     * @return 快照实体
     */
    public Snapshot recordUpdateSnapshot(String tableName, Object originalData, String userId) {
        try {
            return snapshotService.createUpdateSnapshot(tableName, originalData, userId, null, null);
        } catch (Exception e) {
            log.error("记录更新前快照失败 - 表名: {}, 用户ID: {}, 错误: {}", tableName, userId, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 记录删除前快照（无HTTP请求上下文）
     * @param tableName 表名
     * @param originalData 原始数据
     * @param userId 用户ID
     * @return 快照实体
     */
    public Snapshot recordDeleteSnapshot(String tableName, Object originalData, String userId) {
        try {
            return snapshotService.createDeleteSnapshot(tableName, originalData, userId, null, null);
        } catch (Exception e) {
            log.error("记录删除前快照失败 - 表名: {}, 用户ID: {}, 错误: {}", tableName, userId, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 记录系统操作的更新前快照
     * @param tableName 表名
     * @param originalData 原始数据
     * @return 快照实体
     */
    public Snapshot recordSystemUpdateSnapshot(String tableName, Object originalData) {
        return recordUpdateSnapshot(tableName, originalData, null);
    }
    
    /**
     * 记录系统操作的删除前快照
     * @param tableName 表名
     * @param originalData 原始数据
     * @return 快照实体
     */
    public Snapshot recordSystemDeleteSnapshot(String tableName, Object originalData) {
        return recordDeleteSnapshot(tableName, originalData, null);
    }
    

} 