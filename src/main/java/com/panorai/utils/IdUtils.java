package com.panorai.utils;

import lombok.experimental.UtilityClass;

import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;


@UtilityClass
public class IdUtils {
    private static final AtomicLong SEQUENCE = new AtomicLong(0);
    private static volatile long lastTimestamp = -1L;


    public static String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static synchronized String generateShortId() {
        long timestamp = System.currentTimeMillis();

        if (timestamp < lastTimestamp) {
            // 时钟回拨处理
            timestamp = lastTimestamp;
        }

        if (timestamp == lastTimestamp) {
            // 同一毫秒内，序列号递增
            long sequence = SEQUENCE.incrementAndGet();
            if (sequence >= 1000) {
                // 序列号超过3位，等待下一毫秒
                timestamp = waitForNextMillis(lastTimestamp);
                SEQUENCE.set(0);
                sequence = 0;
            }

            // 时间戳压缩：取后5位 + 序列号3位
            long compressedTime = timestamp % 100000;
            return String.format("%05d%03d", compressedTime, sequence);
        } else {
            // 新的毫秒，重置序列号
            SEQUENCE.set(0);
            lastTimestamp = timestamp;

            long compressedTime = timestamp % 100000;
            return String.format("%05d%03d", compressedTime, 0);
        }
    }

    private static long waitForNextMillis(long lastTimestamp) {
        long timestamp = System.currentTimeMillis();
        while (timestamp <= lastTimestamp) {
            timestamp = System.currentTimeMillis();
        }
        return timestamp;
    }
}
