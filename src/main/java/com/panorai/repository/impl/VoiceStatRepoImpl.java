package com.panorai.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.model.entity.VoiceStat;
import com.panorai.mapper.VoiceStatMapper;
import com.panorai.repository.VoiceStatRepo;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public class VoiceStatRepoImpl extends ServiceImpl<VoiceStatMapper, VoiceStat> implements VoiceStatRepo {
    @Override
    public List<String> getPopularVoices(Integer limit, Collection<String> excludeIds) {
        QueryWrapper<VoiceStat> wrapper = new QueryWrapper<>();
        if (excludeIds != null && !excludeIds.isEmpty()) {
            wrapper.notIn("voice_id", excludeIds);
        }
        // 计算热度分数并排序
        wrapper.orderByDesc("(liked_count * 1 + replied_count * 3 + forwarded_count * 2)");
        wrapper.last("limit " + limit);

        // 查询VoiceStat
        List<VoiceStat> stats = this.list(wrapper);
        if (stats.isEmpty()) {
            return List.of();
        }
        return stats.stream().map(VoiceStat::getVoiceId).toList();
    }

    public List<VoiceStat> getVoiceStats(Collection<String> candidateVoices) {
        if (candidateVoices.isEmpty()) {
            return List.of();
        }
        return this.lambdaQuery()
                .in(VoiceStat::getVoiceId, candidateVoices)
                .list();

    }

    @Override
    public void updateLikedCount(String voiceId, Integer likedCount) {
        this.lambdaUpdate()
                .eq(VoiceStat::getVoiceId, voiceId)
                .set(VoiceStat::getLikedCount, likedCount)
                .set(VoiceStat::getUpdatedAt, Instant.now())
                .update();
    }

    @Override
    public void updateForwardedCount(String voiceId, Integer forwardedCount) {
        this.lambdaUpdate()
                .eq(VoiceStat::getVoiceId, voiceId)
                .set(VoiceStat::getForwardedCount, forwardedCount)
                .set(VoiceStat::getUpdatedAt, Instant.now())
                .update();
    }

    @Override
    public void updateRepliedCount(String voiceId, Integer repliedCount) {
        this.lambdaUpdate()
                .eq(VoiceStat::getVoiceId, voiceId)
                .set(VoiceStat::getRepliedCount, repliedCount)
                .set(VoiceStat::getUpdatedAt, Instant.now())
                .update();
    }

    @Override
    public Optional<VoiceStat> getVoiceStat(String voiceId) {
        return this.lambdaQuery()
                .eq(VoiceStat::getVoiceId, voiceId)
                .oneOpt();
    }

    @Override
    public void updateAiScore(String voiceId, Double aiScore) {
        this.lambdaUpdate()
                .eq(VoiceStat::getVoiceId, voiceId)
                .set(VoiceStat::getAiScore, aiScore)
                .set(VoiceStat::getCreatedAt, Instant.now())
                .set(VoiceStat::getUpdatedAt, Instant.now())
                .update();
    }
}
