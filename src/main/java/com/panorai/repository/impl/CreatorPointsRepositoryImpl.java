package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.CreatorPointsMapper;
import com.panorai.model.entity.CreatorPoints;
import com.panorai.repository.CreatorPointsRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CreatorPointsRepositoryImpl extends ServiceImpl<CreatorPointsMapper, CreatorPoints> implements CreatorPointsRepository {
    @Override
    public CreatorPoints findByUserId(String userId) {
        return this.lambdaQuery()
                .eq(CreatorPoints:: getUserId, userId)
                .oneOpt()
                .orElse(null);
    }

    @Override
    public List<CreatorPoints> findTopByTotalPoints(int limit) {
        return List.of();
    }

    @Override
    public List<CreatorPoints> findByAvailablePointsGreaterThan(long points) {
        return List.of();
    }

    @Override
    public long countAll() {
        return 0;
    }

    @Override
    public long sumTotalPoints() {
        return 0;
    }
}
