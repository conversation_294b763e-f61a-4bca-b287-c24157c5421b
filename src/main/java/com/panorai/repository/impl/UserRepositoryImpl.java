package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.UserMapper;
import com.panorai.model.entity.User;
import com.panorai.repository.UserRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public class UserRepositoryImpl extends ServiceImpl<UserMapper, User> implements UserRepository {
    @Override
    public Optional<User> getByEmail(String email) {
        return this.lambdaQuery()
                .eq(User::getEmail, email)
                .oneOpt();
    }
}