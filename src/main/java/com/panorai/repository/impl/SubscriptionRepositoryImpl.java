package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.SubscriptionMapper;
import com.panorai.model.entity.Subscription;
import com.panorai.repository.SubscriptionRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public class SubscriptionRepositoryImpl extends ServiceImpl<SubscriptionMapper, Subscription> implements SubscriptionRepository {
    
    @Override
    public Optional<Subscription> getActiveSubscriptionByUserId(String userId) {
        return lambdaQuery()
                .eq(Subscription::getUserId, userId)
                .in(Subscription::getStatus, "active", "trialing")
                .orderByDesc(Subscription::getCreatedAt)
                .oneOpt();
    }
    
    @Override
    public Optional<Subscription> getByStripeSubscriptionId(String stripeSubscriptionId) {
        return lambdaQuery()
                .eq(Subscription::getStripeSubscriptionId, stripeSubscriptionId)
                .oneOpt();
    }
    
    @Override
    public Optional<Subscription> getByStripeCustomerId(String stripeCustomerId) {
        return lambdaQuery()
                .eq(Subscription::getStripeCustomerId, stripeCustomerId)
                .orderByDesc(Subscription::getCreatedAt)
                .oneOpt();
    }
    
    @Override
    public Optional<Subscription> getByUserId(String userId) {
        return lambdaQuery()
                .eq(Subscription::getUserId, userId)
                .orderByDesc(Subscription::getCreatedAt)
                .oneOpt();
    }
    
    @Override
    public boolean hasValidSubscription(String userId) {
        long count = lambdaQuery()
                .eq(Subscription::getUserId, userId)
                .in(Subscription::getStatus, "active", "trialing")
                .count();
        return count > 0;
    }
}