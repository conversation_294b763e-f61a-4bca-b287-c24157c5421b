package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.SnapshotMapper;
import com.panorai.model.entity.Snapshot;
import com.panorai.repository.SnapshotRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 快照数据仓库实现类
 */
@Repository
@RequiredArgsConstructor
public class SnapshotRepositoryImpl extends ServiceImpl<SnapshotMapper, Snapshot> implements SnapshotRepository {
    
    private final SnapshotMapper snapshotMapper;
    
    @Override
    public List<Snapshot> findByTableNameAndRecordId(String tableName, String recordId) {
        return snapshotMapper.findByTableNameAndRecordId(tableName, recordId);
    }
    
    @Override
    public List<Snapshot> findByTableName(String tableName) {
        return snapshotMapper.findByTableName(tableName);
    }
    
    @Override
    public List<Snapshot> findByUserId(String userId) {
        return snapshotMapper.findByUserId(userId);
    }
} 