package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.ArticleMapper;
import com.panorai.model.entity.Article;
import com.panorai.repository.ArticleRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public class ArticleRepositoryImpl extends ServiceImpl<ArticleMapper, Article> implements ArticleRepository {

    @Override
    public Optional<Article> findByArticleId(String articleId) {
        return lambdaQuery()
                .eq(Article::getArticleId, articleId)
                .oneOpt();
    }

    @Override
    public List<String> findActiveContentIds() {
        return baseMapper.findActiveContentIds();
    }

    @Override
    public List<Article> findActiveContentWithOutContent() {
        return this.lambdaQuery()
                .select(Article::getId, Article::getArticleId, Article::getTitle,
                        Article::getAuthorId, Article::getCoverImage, Article::getSummary,
                        Article::getTags, Article::getCreatedAt, Article::getUpdatedAt,
                        Article::getContentType, Article::getApplyForIncentive, Article::getIsFeatured)
                .isNull(Article::getDeletedAt)
                .eq(Article::getApplyForIncentive, true)
                .orderByDesc(Article::getCreatedAt)
                .list();
    }
} 