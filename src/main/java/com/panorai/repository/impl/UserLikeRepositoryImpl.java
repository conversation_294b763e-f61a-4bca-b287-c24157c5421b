package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.model.entity.UserLike;
import com.panorai.mapper.UserLikeMapper;
import com.panorai.repository.UserLikeRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UserLikeRepositoryImpl extends ServiceImpl<UserLikeMapper, UserLike> implements UserLikeRepository {

    @Override
    public boolean likeTweet(String userId, String tweetId) {
        UserLike userLike = new UserLike();
        userLike.setUserId(userId);
        userLike.setVoiceId(tweetId);
        return save(userLike);
    }

    @Override
    public boolean unlikeTweet(String userId, String tweetId) {
        return lambdaUpdate()
                .eq(UserLike::getUserId, userId)
                .eq(UserLike::getVoiceId, tweetId)
                .remove();
    }

    @Override
    public List<UserLike> getUserLikes(String userId) {
        return lambdaQuery()
                .eq(UserLike::getUserId, userId)
                .list();
    }

    @Override
    public List<UserLike> getTweetLikes(String tweetId) {
        return lambdaQuery()
                .eq(UserLike::getVoiceId, tweetId)
                .list();
    }

    @Override
    public boolean hasLiked(String userId, String tweetId) {
        return lambdaQuery()
                .eq(UserLike::getUserId, userId)
                .eq(UserLike::getVoiceId, tweetId)
                .exists();
    }

    @Override
    public long getVoiceLikeCount(String voiceId) {
        return lambdaQuery()
                .eq(UserLike::getVoiceId, voiceId)
                .count();
    }

    @Override
    public long getUserTotalLikes(String userId) {
        return lambdaQuery()
                .eq(UserLike::getUserId, userId)
                .count();
    }
}
