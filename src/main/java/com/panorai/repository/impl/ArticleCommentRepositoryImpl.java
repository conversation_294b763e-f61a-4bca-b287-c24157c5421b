package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.ArticleCommentMapper;
import com.panorai.model.entity.ArticleComment;
import com.panorai.repository.ArticleCommentRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;

@Repository
public class ArticleCommentRepositoryImpl extends ServiceImpl<ArticleCommentMapper, ArticleComment> implements ArticleCommentRepository {

    @Override
    public int countByArticleId(String articleId) {
        return baseMapper.countByArticleId(articleId);
    }

    @Override
    public int countByArticleIdAfterTime(String articleId, Instant afterTime) {
        return 0;
    }

    @Override
    public int countByArticleIdBetweenTime(String articleId, Instant startTime, Instant endTime) {
        return 0;
    }
} 