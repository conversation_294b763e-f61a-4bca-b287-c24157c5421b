package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.PointsConfigMapper;
import com.panorai.model.entity.PointsConfig;
import com.panorai.repository.PointsConfigRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class PointsConfigRepositoryImpl extends ServiceImpl<PointsConfigMapper, PointsConfig> implements PointsConfigRepository {
    @Override
    public PointsConfig findByConfigKey(String configKey) {
        return null;
    }

    @Override
    public Map<String, String> findAllConfigs() {
        List<PointsConfig> configs = this.lambdaQuery().list();
        return configs.stream()
                .collect(
                        Collectors.toMap(
                                PointsConfig::getConfigKey,
                                PointsConfig::getConfigValue,
                                (o1, o2) -> o1
                        )
                );
    }

    @Override
    public String getConfigValue(String configKey) {
        return "";
    }

    @Override
    public void updateConfigValue(String configKey, String configValue) {

    }
}
