package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.model.entity.BlockedUser;
import com.panorai.mapper.BlockedUserMapper;
import com.panorai.repository.BlockedUserRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class BlockedUserRepositoryImpl extends ServiceImpl<BlockedUserMapper, BlockedUser> implements BlockedUserRepository {

    @Override
    public boolean blockUser(String userId, String blockedUserId) {
        BlockedUser blockedUser = new BlockedUser();
        blockedUser.setUserId(userId);
        blockedUser.setBlockedUserId(blockedUserId);
        blockedUser.setCreatedAt(Instant.now());
        blockedUser.setUpdatedAt(Instant.now());
        return save(blockedUser);
    }

    @Override
    public boolean unblockUser(String userId, String blockedUserId) {
        return lambdaUpdate()
                .eq(BlockedUser::getUserId, userId)
                .eq(BlockedUser::getBlockedUserId, blockedUserId)
                .remove();
    }

    @Override
    public List<BlockedUser> getBlockedUsers(String userId) {
        return lambdaQuery()
                .eq(BlockedUser::getUserId, userId)
                .orderByDesc(BlockedUser::getCreatedAt)
                .list();
    }

    @Override
    public boolean isBlocked(String userId, String blockedUserId) {
        return lambdaQuery()
                .eq(BlockedUser::getUserId, userId)
                .eq(BlockedUser::getBlockedUserId, blockedUserId)
                .exists();
    }

    @Override
    public long getBlockedCount(String userId) {
        return lambdaQuery()
                .eq(BlockedUser::getUserId, userId)
                .count();
    }

    @Override
    public List<String> getBlockedUserIds(String userId) {
        return lambdaQuery()
                .eq(BlockedUser::getUserId, userId)
                .list()
                .stream()
                .map(BlockedUser::getBlockedUserId)
                .collect(Collectors.toList());
    }

    @Override
    public List<BlockedUser> getInviableUserIds(String userId) {
        return this.lambdaQuery()
                .and(
                        lambda -> lambda.eq(BlockedUser::getUserId, userId)
                                .or()
                                .eq(BlockedUser::getBlockedUserId, userId)
                )
                .list();
    }
}