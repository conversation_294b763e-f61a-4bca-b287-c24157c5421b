package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.model.entity.UserProfile;
import com.panorai.mapper.UserProfileMapper;
import com.panorai.repository.UserProfileRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public class UserProfileRepositoryImpl extends ServiceImpl<UserProfileMapper, UserProfile> implements UserProfileRepository {

    @Override
    public UserProfile getByUserId(String userId) {
        return optByUserId(userId)
                .orElseThrow(() -> new RuntimeException("User profile not found"));
    }

    @Override
    public Optional<UserProfile> optByUserId(String userId) {
        return this.lambdaQuery()
                .eq(UserProfile::getUserId, userId)
                .oneOpt();
    }

    @Override
    public List<UserProfile> getPrivateUsers() {
        return this.lambdaQuery()
                .eq(UserProfile::getVisibility, "private")
                .list();
    }
}
