package com.panorai.repository.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.panorai.model.entity.Voice;
import com.panorai.repository.RecommendationCacheRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;

@Repository
@RequiredArgsConstructor
@Slf4j
public class RecommendationCacheRepositoryImpl implements RecommendationCacheRepository {
    
    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    
    private static final String CACHE_KEY_PREFIX = "user_recommendations:";
    private static final Duration BASE_TTL = Duration.ofMinutes(30);
    private static final int RANDOM_RANGE_SECONDS = 300; // 随机范围：±5分钟
    
    @Override
    public void cacheUserRecommendations(String userId, List<Voice> voices) {
        try {
            String key = buildCacheKey(userId);
            String jsonValue = objectMapper.writeValueAsString(voices);
            
            // 添加随机值防止缓存雪崩：基础TTL ± 5分钟
            Duration randomTtl = calculateRandomTtl();
            
            redisTemplate.opsForValue().set(key, jsonValue, randomTtl);
            log.info("Cached recommendations for user: {}, size: {}, TTL: {}ms", 
                    userId, voices.size(), randomTtl.toMillis());
        } catch (Exception e) {
            log.error("Failed to cache recommendations for user: {}", userId, e);
        }
    }
    
    @Override
    public Optional<List<Voice>> getCachedUserRecommendations(String userId) {
        try {
            String key = buildCacheKey(userId);
            String jsonValue = redisTemplate.opsForValue().get(key);
            
            if (jsonValue == null) {
                log.debug("No cached recommendations found for user: {}", userId);
                return Optional.empty();
            }
            
            List<Voice> voices = objectMapper.readValue(jsonValue, new TypeReference<>() {
            });
            log.info("Retrieved cached recommendations for user: {}, size: {}", userId, voices.size());
            return Optional.of(voices);
        } catch (Exception e) {
            log.error("Failed to retrieve cached recommendations for user: {}", userId, e);
            return Optional.empty();
        }
    }
    
    @Override
    public void updateUserRecommendations(String userId, List<Voice> remainingVoices) {
        try {
            String key = buildCacheKey(userId);
            
            if (remainingVoices.isEmpty()) {
                clearUserRecommendations(userId);
                return;
            }
            
            String jsonValue = objectMapper.writeValueAsString(remainingVoices);
            
            // 保持原有TTL
            long ttl = redisTemplate.getExpire(key);
            if (ttl > 0) {
                redisTemplate.opsForValue().set(key, jsonValue, Duration.ofSeconds(ttl));
                log.info("Updated cached recommendations for user: {}, remaining: {}", 
                        userId, remainingVoices.size());
            } else {
                // 如果TTL已过期或获取失败，使用默认TTL
                Duration randomTtl = calculateRandomTtl();
                redisTemplate.opsForValue().set(key, jsonValue, randomTtl);
                log.info("Updated cached recommendations with new TTL for user: {}, remaining: {}", 
                        userId, remainingVoices.size());
            }
        } catch (Exception e) {
            log.error("Failed to update cached recommendations for user: {}", userId, e);
        }
    }
    
    @Override
    public void clearUserRecommendations(String userId) {
        try {
            String key = buildCacheKey(userId);
            redisTemplate.delete(key);
            log.info("Cleared cached recommendations for user: {}", userId);
        } catch (Exception e) {
            log.error("Failed to clear cached recommendations for user: {}", userId, e);
        }
    }
    
    private String buildCacheKey(String userId) {
        return CACHE_KEY_PREFIX + userId;
    }
    
    /**
     * 计算随机TTL以防止缓存雪崩
     * 基础TTL ± RANDOM_RANGE_MINUTES 分钟
     */
    private Duration calculateRandomTtl() {
        int randomMinutes = ThreadLocalRandom.current().nextInt(-RANDOM_RANGE_SECONDS, RANDOM_RANGE_SECONDS + 1);
        return BASE_TTL.plusSeconds(randomMinutes);
    }
} 