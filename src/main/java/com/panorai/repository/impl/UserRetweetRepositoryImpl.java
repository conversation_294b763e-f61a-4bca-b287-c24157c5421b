package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.model.entity.UserRetweet;
import com.panorai.mapper.UserRetweetMapper;
import com.panorai.repository.UserRetweetRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UserRetweetRepositoryImpl extends ServiceImpl<UserRetweetMapper, UserRetweet> implements UserRetweetRepository {

    @Override
    public boolean retweet(String userId, String voiceId) {
        UserRetweet userRetweet = new UserRetweet();
        userRetweet.setUserId(userId);
        userRetweet.setVoiceId(voiceId);
        return save(userRetweet);
    }

    @Override
    public boolean unretweet(String userId, String voiceId) {
        return lambdaUpdate()
                .eq(UserRetweet::getUserId, userId)
                .eq(UserRetweet::getVoiceId, voiceId)
                .remove();
    }

    @Override
    public List<UserRetweet> getUserRetweets(String userId) {
        return lambdaQuery()
                .eq(UserRetweet::getUserId, userId)
                .list();
    }

    @Override
    public List<UserRetweet> getTweetRetweets(String voiceId) {
        return lambdaQuery()
                .eq(UserRetweet::getVoiceId, voiceId)
                .list();
    }

    @Override
    public boolean hasRetweeted(String userId, String voiceId) {
        return lambdaQuery()
                .eq(UserRetweet::getUserId, userId)
                .eq(UserRetweet::getVoiceId, voiceId)
                .exists();
    }

    @Override
    public long getForwardCount(String voiceId) {
        return lambdaQuery()
                .eq(UserRetweet::getVoiceId, voiceId)
                .count();
    }

    @Override
    public long getUserTotalRetweets(String userId) {
        return lambdaQuery()
                .eq(UserRetweet::getUserId, userId)
                .count();
    }
}
