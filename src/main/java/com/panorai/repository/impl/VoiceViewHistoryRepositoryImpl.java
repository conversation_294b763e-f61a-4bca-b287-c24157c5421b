package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.model.dto.LastViewedTimeDTO;
import com.panorai.model.entity.VoiceViewHistory;
import com.panorai.mapper.VoiceViewHistoryMapper;
import com.panorai.repository.VoiceViewHistoryRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class VoiceViewHistoryRepositoryImpl extends ServiceImpl<VoiceViewHistoryMapper, VoiceViewHistory> implements VoiceViewHistoryRepository {
    @Override
    public List<VoiceViewHistory> getRecentViewed(String userId, Integer days) {
        Instant cutoff = Instant.now().minusSeconds(days * 24 * 60 * 60);
        return this.lambdaQuery()
                .eq(VoiceViewHistory::getUserId, userId)
                .ge(VoiceViewHistory::getCreatedAt, cutoff)
                .orderByDesc(VoiceViewHistory::getCreatedAt)
                .list();
    }

    @Override
    public List<VoiceViewHistory> getRecent3DayViewed(String userId) {
        return getRecentViewed(userId, 3);
    }

    @Override
    public Map<String, Instant> getLastViewedTime(Collection<String> voiceIds, String userId) {
        if (voiceIds == null || voiceIds.isEmpty()) {
            return Map.of();
        }
        List<LastViewedTimeDTO> lastViewedTime = this.baseMapper.getLastViewedTime(voiceIds, userId);
        if (Objects.isNull(lastViewedTime)) {
            return Map.of();
        }
        return lastViewedTime.stream().collect(Collectors.toMap(LastViewedTimeDTO::voiceId, LastViewedTimeDTO::lastViewedTime));
    }

    @Override
    public Map<String, LastViewedTimeDTO> getViewStatistics(Collection<String> voiceIds, String userId) {
        if (voiceIds == null || voiceIds.isEmpty()) {
            return Map.of();
        }

        List<LastViewedTimeDTO> results = this.baseMapper.getViewStatistics(voiceIds, userId);
        if (Objects.isNull(results)) {
            return Map.of();
        }
        return results.stream()
                .collect(Collectors.toMap(LastViewedTimeDTO::voiceId, (i) -> i));
    }
}
