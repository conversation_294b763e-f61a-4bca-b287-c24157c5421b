package com.panorai.repository.impl;

import com.panorai.repository.LimitRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Repository
@RequiredArgsConstructor
public class LimitRepositoryImpl implements LimitRepository {
    private final StringRedisTemplate redisTemplate;
    private static final String IP_LIMIT_KEY_PREFIX = "limit:opt:ip:";
    private static final String EMAIL_LIMIT_KEY_PREFIX = "limit:opt:email:";
    private static final String SIGN_IN_LIMIT_KEY_PREFIX = "limit:signin:";
    private static final String SIGN_IN_FAIL_COUNT_KEY_PREFIX = "limit:signin:fail:";

    // 登录失败次数限制：5次
    private static final int MAX_SIGN_IN_FAIL_COUNT = 5;
    // 冷却时间：12小时
    private static final int SIGN_IN_COOLDOWN_SECONDS = 60 * 60 * 12;
    private static final String SIGN_IN_LIMIT_KEY_PREFIX = "limit:sign_in:";

    @Override
    public long checkIpLimit(String ipAddress) {
        String key = IP_LIMIT_KEY_PREFIX + ipAddress;
        return getTtl(key);
    }

    @Override
    public long checkEmailLimit(String email) {
        String key = EMAIL_LIMIT_KEY_PREFIX + email;
        return getTtl(key);
    }

    @Override
    public void setIpLimit(String ipAddress) {
        String key = IP_LIMIT_KEY_PREFIX + ipAddress;
        tryAcquire(key, 60);
    }

    @Override
    public void setEmailLimit(String email) {
        String key = EMAIL_LIMIT_KEY_PREFIX + email;
        tryAcquire(key, 60);
    }

    @Override
    public void setSignInLimit(String userId) {
        String key = SIGN_IN_LIMIT_KEY_PREFIX + userId;
        tryAcquire(key, 60 * 60 * 12);
    }

    private long getTtl(String key) {
        Boolean existed = redisTemplate.hasKey(key);
        if (!existed) {
            return -2;
        }
        return redisTemplate.getExpire(key);
    }

    public void tryAcquire(String key, int seconds) {
        redisTemplate.opsForValue().setIfAbsent(key, "1", seconds, TimeUnit.SECONDS);
    }

    @Override
    public long checkSignInLimit(String userId) {
        String key = SIGN_IN_LIMIT_KEY_PREFIX + userId;
        return getTtl(key);
    }

    @Override
    public int incrementSignInFailCount(String userId) {
        String key = SIGN_IN_FAIL_COUNT_KEY_PREFIX + userId;

        // 使用 Redis 的 incr 命令递增计数器
        Long count = redisTemplate.opsForValue().increment(key);

        // 如果是第一次设置，设置过期时间为24小时（失败计数的有效期）
        if (count == 1) {
            redisTemplate.expire(key, 24 * 60 * 60, TimeUnit.SECONDS);
        }

        // 如果失败次数达到限制，设置冷却时间
        if (count >= MAX_SIGN_IN_FAIL_COUNT) {
            setSignInLimit(userId);
        }

        return count.intValue();
    }

    @Override
    public void setSignInLimit(String userId) {
        String key = SIGN_IN_LIMIT_KEY_PREFIX + userId;
        redisTemplate.opsForValue().set(key, "1", SIGN_IN_COOLDOWN_SECONDS, TimeUnit.SECONDS);
    }

    @Override
    public void clearSignInFailCount(String userId) {
        String failCountKey = SIGN_IN_FAIL_COUNT_KEY_PREFIX + userId;
        String limitKey = SIGN_IN_LIMIT_KEY_PREFIX + userId;

        // 清除失败计数和冷却限制
        redisTemplate.delete(failCountKey);
        redisTemplate.delete(limitKey);
    }

    @Override
    public int getSignInFailCount(String userId) {
        String key = SIGN_IN_FAIL_COUNT_KEY_PREFIX + userId;
        String count = redisTemplate.opsForValue().get(key);
        return count != null ? Integer.parseInt(count) : 0;
    }
}
