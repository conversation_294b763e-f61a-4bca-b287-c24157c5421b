package com.panorai.repository.impl;

import com.panorai.repository.OptRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.time.Duration;

@Repository
@RequiredArgsConstructor
public class OptRepositoryImpl implements OptRepository {
    private final StringRedisTemplate redisTemplate;
    private static final String OPT_PREFIX = "opt:email:";

    @Override
    public void saveOTP(String email, String opt) {
        redisTemplate.opsForValue().set(
                OPT_PREFIX + email,
                opt,
                Duration.ofMinutes(5)
        );
    }

    @Override
    public String getOTP(String email) {
        return redisTemplate.opsForValue().get(OPT_PREFIX + email);
    }

    @Override
    public void deleteOTP(String email) {
        redisTemplate.delete(OPT_PREFIX + email);
    }
}
