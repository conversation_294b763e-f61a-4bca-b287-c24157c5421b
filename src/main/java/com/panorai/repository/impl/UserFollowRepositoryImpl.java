package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.model.entity.UserFollow;
import com.panorai.mapper.UserFollowMapper;
import com.panorai.repository.UserFollowRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UserFollowRepositoryImpl extends ServiceImpl<UserFollowMapper, UserFollow> implements UserFollowRepository {

    @Override
    public boolean followUser(String followerId, String followedId) {
        UserFollow userFollow = new UserFollow();
        userFollow.setUserId(followerId);
        userFollow.setFollowedId(followedId);
        return save(userFollow);
    }

    @Override
    public boolean unfollowUser(String followerId, String followedId) {
        return lambdaUpdate()
                .eq(UserFollow::getUserId, followerId)
                .eq(UserFollow::getFollowedId, followedId)
                .remove();
    }

    @Override
    public List<UserFollow> getFollowings(String userId) {
        return lambdaQuery()
                .eq(UserFollow::getUserId, userId)
                .list();
    }

    @Override
    public List<UserFollow> getFollowers(String userId) {
        return lambdaQuery()
                .eq(UserFollow::getFollowedId, userId)
                .list();
    }

    @Override
    public boolean isFollowing(String followerId, String followedId) {
        return lambdaQuery()
                .eq(UserFollow::getUserId, followerId)
                .eq(UserFollow::getFollowedId, followedId)
                .exists();
    }

    @Override
    public UserFollow getFollowRelation(String followerId, String followedId) {
        return lambdaQuery()
                .eq(UserFollow::getUserId, followerId)
                .eq(UserFollow::getFollowedId, followedId)
                .one();
    }

    @Override
    public long getFollowingCount(String userId) {
        return lambdaQuery()
                .eq(UserFollow::getUserId, userId)
                .count();
    }

    @Override
    public long getFollowerCount(String userId) {
        return lambdaQuery()
                .eq(UserFollow::getFollowedId, userId)
                .count();
    }
}
