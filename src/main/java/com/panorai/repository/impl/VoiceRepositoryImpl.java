package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.model.entity.Voice;
import com.panorai.mapper.VoiceMapper;
import com.panorai.repository.VoiceRepository;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public class VoiceRepositoryImpl extends ServiceImpl<VoiceMapper, Voice> implements VoiceRepository {

    @Override
    public List<Voice> getUserVoices(String userId) {
        return lambdaQuery()
                .eq(Voice::getAuthorId, userId)
                .orderByDesc(Voice::getCreatedAt)
                .list();
    }

    @Override
    public Voice createVoice(Voice voice) {
        save(voice);
        return voice;
    }

    @Override
    public List<Voice> getTimeline(String userId) {
        return lambdaQuery()
                .eq(Voice::getAuthorId, userId)
                .eq(Voice::getIsHidden, false)
                .orderByDesc(Voice::getCreatedAt)
                .list();
    }

    @Override
    public void toggleVoiceVisibility(String voiceId, boolean isHidden) {
        lambdaUpdate()
                .eq(Voice::getId, voiceId)
                .set(Voice::getIsHidden, isHidden)
                .update();
    }

    @Override
    public List<Voice> getRecentVoices(int limit, Set<String> excludeIds, Collection<String> excludeUser) {
        return lambdaQuery()
                .eq(Voice::getIsRetweet, false)
                .eq(Voice::getIsReply, false)
                .notIn(!CollectionUtils.isEmpty(excludeUser), Voice::getAuthorId, excludeUser)
                .eq(Voice::getVisibility, "public")
                .notIn(!CollectionUtils.isEmpty(excludeIds), Voice::getId, excludeIds)
                .orderByDesc(Voice::getCreatedAt)
                .last("LIMIT " + limit)
                .list();
    }

    @Override
    public List<Voice> getVoicesByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Set<String> excludeIds) {
        var query = lambdaQuery()
                .between(Voice::getCreatedAt, startTime, endTime)
                .eq(Voice::getIsHidden, false)
                .orderByDesc(Voice::getCreatedAt);

        if (excludeIds != null && !excludeIds.isEmpty()) {
            query.notIn(Voice::getId, excludeIds);
        }

        return query.list();
    }

    @Override
    public List<Voice> getVoicesExcludingUser(String userId, int limit, Set<String> excludeIds) {
        var query = lambdaQuery()
                .ne(Voice::getAuthorId, userId)
                .eq(Voice::getIsHidden, false)
                .orderByDesc(Voice::getCreatedAt)
                .last("LIMIT " + limit);

        if (excludeIds != null && !excludeIds.isEmpty()) {
            query.notIn(Voice::getId, excludeIds);
        }

        return query.list();
    }

    @Override
    public long getVoiceReplyCount(String voiceId) {
        return lambdaQuery()
                .eq(Voice::getRepliedToId, voiceId)
                .eq(Voice::getIsReply, true)
                .count();
    }

    @Override
    public List<Voice> getRepliedVoiceOrForwardedByUserId(String userId) {
        return this.lambdaQuery()
                .eq(Voice::getAuthorId, userId)
                .and(lambda -> lambda.eq(Voice::getIsReply, true)
                        .or()
                        .eq(Voice::getIsRetweet, true))
                .list();
    }

    @Override
    public List<Voice> listPublicByUserIds(List<String> followingIds, Integer limit, Set<String> excludeIds, Collection<String> excludeUserIds) {
        return this.lambdaQuery()
                .in(Voice::getAuthorId, followingIds)
                .eq(Voice::getIsReply, false)
                .eq(Voice::getIsRetweet, false)
                .eq(Voice::getVisibility, "public")
                .notIn(!CollectionUtils.isEmpty(excludeIds), Voice::getId, excludeIds)
                .notIn(!CollectionUtils.isEmpty(excludeUserIds), Voice::getAuthorId, excludeUserIds)
                .orderByDesc(Voice::getCreatedAt)
                .last("LIMIT " + limit)
                .list();
    }

    @Override
    public List<Voice> listPublicByIds(Collection<String> ids, Collection<String> excludeUserIds) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }

        return this.lambdaQuery()
                .in(Voice::getId, ids)
                .eq(Voice::getIsReply, false)
                .eq(Voice::getIsRetweet, false)
                .eq(Voice::getVisibility, "public")
                .notIn(!CollectionUtils.isEmpty(excludeUserIds), Voice::getAuthorId, excludeUserIds)
                .orderByDesc(Voice::getCreatedAt)
                .list();
    }

    @Override
    public Optional<Voice> getVoice(String voiceId) {
        return this.lambdaQuery()
                .eq(Voice::getId, voiceId)
                .eq(Voice::getIsReply, false)
                .oneOpt();
    }

    @Override
    public Optional<Voice> getReply(String voiceId) {
        return this.lambdaQuery()
                .eq(Voice::getRepliedToId, voiceId)
                .eq(Voice::getIsReply, true)
                .oneOpt();
    }

    @Override
    public List<Voice> getLast12HoursVoices(Set<String> excludeUser){
        return this.lambdaQuery()
                .eq(Voice::getIsRetweet, false)
                .eq(Voice::getIsReply, false)
                .notIn(!CollectionUtils.isEmpty(excludeUser), Voice::getAuthorId, excludeUser)
                .eq(Voice::getVisibility, "public")
                .ge(Voice::getCreatedAt, Instant.now().minusSeconds(12 * 60 * 60))
                .orderByDesc(Voice::getCreatedAt)
                .list();
    }

    @Override
    public List<Voice> getLast30DaysVoices(String userId) {
        return this.lambdaQuery()
                .eq(Voice::getAuthorId, userId)
                .ne(Voice::getVisibility, "private")
                .ge(Voice::getCreatedAt, Instant.now().minusSeconds(30 * 24 * 60 * 60))
                .list();
    }
}
