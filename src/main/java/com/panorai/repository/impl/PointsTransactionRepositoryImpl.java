package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.PointsTransactionMapper;
import com.panorai.model.entity.PointsTransaction;
import com.panorai.repository.PointsTransactionRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public class PointsTransactionRepositoryImpl extends ServiceImpl<PointsTransactionMapper, PointsTransaction> implements PointsTransactionRepository {

    @Override
    public List<PointsTransaction> findByUserId(String userId, int limit) {
        return baseMapper.findByUserIdWithLimit(userId, limit);
    }

    @Override
    public List<PointsTransaction> findByUserIdAndType(String userId, String type, int limit) {
        return baseMapper.findByUserIdAndTypeWithLimit(userId, type, limit);
    }

    @Override
    public List<PointsTransaction> findByCreatedAtBetween(Instant startTime, Instant endTime) {
        return baseMapper.findByCreatedAtBetween(startTime, endTime);
    }

    @Override
    public long sumAmountByUserIdAndType(String userId, String type) {
        return baseMapper.sumAmountByUserIdAndType(userId, type);
    }

    @Override
    public List<PointsTransaction> findByContentId(String contentId) {
        return baseMapper.findByContentId(contentId);
    }

    @Override
    public List<PointsTransaction> findLastContentInteractionTransaction(String contentId) {
        return baseMapper.findLastContentInteractionTransaction(contentId);
    }

    @Override
    public List<PointsTransaction> findByContentIdAndSubType(String contentId, String subType) {
        return baseMapper.findByContentIdAndSubType(contentId, subType);
    }

    @Override
    public long sumPointsByContentId(String contentId) {
        return baseMapper.sumPointsByContentId(contentId);
    }
} 