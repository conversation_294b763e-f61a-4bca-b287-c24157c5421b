package com.panorai.repository;

public interface LimitRepository {
    long checkIpLimit(String ipAddress);

    long checkEmailLimit(String email);

    void setIpLimit(String ipAddress);

    void setEmailLimit(String email);

    // 登录失败次数限制相关方法
    /**
     * 检查用户登录失败冷却时间
     * @param userId 用户ID
     * @return 剩余冷却时间（秒），-2表示无限制，>0表示还在冷却中
     */
    long checkSignInLimit(String userId);

    /**
     * 增加用户登录失败次数
     * @param userId 用户ID
     * @return 当前失败次数
     */
    int incrementSignInFailCount(String userId);

    /**
     * 设置用户登录失败冷却（12小时）
     * @param userId 用户ID
     */
    void setSignInLimit(String userId);

    /**
     * 清除用户登录失败记录（登录成功时调用）
     * @param userId 用户ID
     */
    void clearSignInFailCount(String userId);

    /**
     * 获取用户当前登录失败次数
     * @param userId 用户ID
     * @return 失败次数
     */
    int getSignInFailCount(String userId);
}
