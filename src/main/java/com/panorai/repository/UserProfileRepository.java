package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.UserProfile;

import java.util.List;
import java.util.Optional;

public interface UserProfileRepository extends IService<UserProfile> {
    UserProfile getByUserId(String userId);

    Optional<UserProfile> optByUserId(String userId);

    List<UserProfile> getPrivateUsers();
}
