package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.ArticleLog;

public interface ArticleLogRepository extends IService<ArticleLog> {
    
    /**
     * 统计文章浏览次数
     */
    int countByArticleId(String articleId);
    
    /**
     * 统计指定时间后的文章浏览次数（按日去重）
     */
    int countByArticleIdAfterTime(String articleId, java.time.Instant afterTime);
    
    /**
     * 统计指定时间区间内的文章浏览次数（按日去重）
     */
    int countByArticleIdBetweenTime(String articleId, java.time.Instant startTime, java.time.Instant endTime);
} 