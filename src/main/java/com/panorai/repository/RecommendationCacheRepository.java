package com.panorai.repository;

import com.panorai.model.entity.Voice;
import java.util.List;
import java.util.Optional;

public interface RecommendationCacheRepository {
    
    /**
     * 存储用户推荐列表到Redis缓存
     * @param userId 用户ID
     * @param voices 推荐的Voice列表
     */
    void cacheUserRecommendations(String userId, List<Voice> voices);
    
    /**
     * 获取用户推荐列表的缓存
     * @param userId 用户ID
     * @return 缓存的Voice列表，如果不存在返回空Optional
     */
    Optional<List<Voice>> getCachedUserRecommendations(String userId);
    
    /**
     * 更新用户推荐列表缓存（移除已返回的部分）
     * @param userId 用户ID
     * @param remainingVoices 剩余的Voice列表
     */
    void updateUserRecommendations(String userId, List<Voice> remainingVoices);
    
    /**
     * 删除用户推荐缓存
     * @param userId 用户ID
     */
    void clearUserRecommendations(String userId);
} 