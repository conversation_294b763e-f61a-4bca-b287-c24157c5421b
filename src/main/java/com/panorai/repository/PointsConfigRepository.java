package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.PointsConfig;

import java.util.Map;

/**
 * 积分配置Repository接口
 */
public interface PointsConfigRepository extends IService<PointsConfig> {
    
    /**
     * 根据配置键查找配置
     */
    PointsConfig findByConfigKey(String configKey);
    
    /**
     * 获取所有配置的键值对
     */
    Map<String, String> findAllConfigs();
    
    /**
     * 根据配置键获取配置值
     */
    String getConfigValue(String configKey);
    
    /**
     * 更新配置值
     */
    void updateConfigValue(String configKey, String configValue);
} 