package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.dto.LastViewedTimeDTO;
import com.panorai.model.entity.VoiceViewHistory;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface VoiceViewHistoryRepository extends IService<VoiceViewHistory> {
    List<VoiceViewHistory> getRecentViewed(String userId, Integer days);

    List<VoiceViewHistory> getRecent3DayViewed(String userId);

    Map<String, Instant> getLastViewedTime(Collection<String> voiceIds, String userId);

    /**
     * 获取指定voice IDs的浏览统计信息（最后浏览时间 + 浏览次数）
     * @param voiceIds voice ID集合
     * @return 浏览统计信息列表
     */
    Map<String, LastViewedTimeDTO> getViewStatistics(Collection<String> voiceIds, String userId);
}
