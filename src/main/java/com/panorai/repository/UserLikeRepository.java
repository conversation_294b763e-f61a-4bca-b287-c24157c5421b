package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.UserLike;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface UserLikeRepository extends IService<UserLike> {
    // 点赞推文
    boolean likeTweet(String userId, String tweetId);

    // 取消点赞
    boolean unlikeTweet(String userId, String tweetId);

    // 获取用户点赞的推文列表
    List<UserLike> getUserLikes(String userId);

    // 获取推文的点赞列表
    List<UserLike> getTweetLikes(String tweetId);

    // 检查用户是否已点赞某推文
    boolean hasLiked(String userId, String tweetId);

    // 获取推文的点赞数量
    long getVoiceLikeCount(String voiceId);

    // 获取用户的点赞总数
    long getUserTotalLikes(String userId);
}
