package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.UserFollow;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface UserFollowRepository extends IService<UserFollow> {
    // 关注用户
    boolean followUser(String followerId, String followedId);

    // 取消关注用户
    boolean unfollowUser(String followerId, String followedId);

    // 获取用户的关注列表
    List<UserFollow> getFollowings(String userId);

    // 获取用户的粉丝列表
    List<UserFollow> getFollowers(String userId);

    // 检查是否已关注
    boolean isFollowing(String followerId, String followedId);

    // 获取两个用户之间的关注关系
    UserFollow getFollowRelation(String followerId, String followedId);

    // 获取用户的关注数量
    long getFollowingCount(String userId);

    // 获取用户的粉丝数量
    long getFollowerCount(String userId);
}
