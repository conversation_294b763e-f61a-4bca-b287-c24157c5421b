package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.Subscription;

import java.util.Optional;

public interface SubscriptionRepository extends IService<Subscription> {
    
    // 根据用户ID获取活跃订阅
    Optional<Subscription> getActiveSubscriptionByUserId(String userId);
    
    // 根据Stripe订阅ID获取订阅
    Optional<Subscription> getByStripeSubscriptionId(String stripeSubscriptionId);
    
    // 根据Stripe客户ID获取订阅
    Optional<Subscription> getByStripeCustomerId(String stripeCustomerId);
    
    // 根据用户ID获取最新订阅
    Optional<Subscription> getByUserId(String userId);
    
    // 检查用户是否有有效订阅
    boolean hasValidSubscription(String userId);
}