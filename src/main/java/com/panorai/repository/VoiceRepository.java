package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.Voice;
import com.panorai.model.dto.CommentReplyCountDTO;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface VoiceRepository extends IService<Voice> {
    // 获取用户的推文列表
    List<Voice> getUserVoices(String userId);

    // 发布新推文
    Voice createVoice(Voice voice);

    // 获取用户时间线
    List<Voice> getTimeline(String userId);

    // 隐藏/取消隐藏推文
    void toggleVoiceVisibility(String voiceId, boolean isHidden);

    // ========== 推荐系统相关方法 ==========

    // 获取最新推文（时间倒序）
    List<Voice> getRecentVoices(int limit, Set<String> excludeIds, Collection<String> excludeUser);

    // 获取指定时间范围内的推文
    List<Voice> getVoicesByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Set<String> excludeIds);

    // 排除用户自己的推文
    List<Voice> getVoicesExcludingUser(String userId, int limit, Set<String> excludeIds);

    // 获取推文的回复数量
    long getVoiceReplyCount(String voiceId);

    List<Voice> getRepliedVoiceOrForwardedByUserId(String userId);

    List<Voice> listPublicByUserIds(List<String> followingIds, Integer limit, Set<String> excludeIds, Collection<String> excludeUserIds);
    List<Voice> listPublicByIds(Collection<String> ids, Collection<String> excludeUserIds);

    Optional<Voice> getVoice(String voiceId);

    Optional<Voice> getReply(String voiceId);

    List<Voice> getLast12HoursVoices(Set<String> excludeUserIds);

    List<Voice> getLast30DaysVoices(String userId);
    
    // ========== 评论树相关方法 ==========
    
    // 获取主帖的一级评论（mainReplyId为null的评论）
    List<Voice> getFirstLevelComments(String mainVoiceId, int limit, int offset);
    
    // 获取某个一级评论下的所有回复（通过mainReplyId）
    List<Voice> getRepliesByMainReplyId(String mainReplyId);
    
    // 批量获取多个Voice的信息（用于构建回复链）
    List<Voice> getVoicesByIds(List<String> voiceIds);
    
    // 统计主帖的一级评论数
    long countFirstLevelComments(String mainVoiceId);
    
    // 批量统计多个一级评论的回复数
    List<CommentReplyCountDTO> batchCountReplies(List<String> mainReplyIds);
}

