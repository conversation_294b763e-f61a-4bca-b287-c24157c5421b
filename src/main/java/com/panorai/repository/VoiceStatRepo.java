package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.VoiceStat;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface VoiceStatRepo extends IService<VoiceStat> {
    List<String> getPopularVoices(Integer limit, Collection<String> excludeIds);

    List<VoiceStat> getVoiceStats(Collection<String> candidateVoices);

    void updateLikedCount(String voiceId, Integer likedCount);

    void updateForwardedCount(String voiceId, Integer forwardedCount);

    void updateRepliedCount(String voiceId, Integer repliedCount);

    Optional<VoiceStat> getVoiceStat(String voiceId);

    void updateAiScore(String voiceId, Double aiScore);
}
