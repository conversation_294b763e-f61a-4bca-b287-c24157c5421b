package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.Snapshot;

import java.util.List;

/**
 * 快照数据仓库接口
 */
public interface SnapshotRepository extends IService<Snapshot> {
    
    /**
     * 根据表名和记录ID查询快照
     * @param tableName 表名
     * @param recordId 记录ID
     * @return 快照列表
     */
    List<Snapshot> findByTableNameAndRecordId(String tableName, String recordId);
    
    /**
     * 根据表名查询快照
     * @param tableName 表名
     * @return 快照列表
     */
    List<Snapshot> findByTableName(String tableName);
    
    /**
     * 根据用户ID查询快照
     * @param userId 用户ID
     * @return 快照列表
     */
    List<Snapshot> findByUserId(String userId);
} 