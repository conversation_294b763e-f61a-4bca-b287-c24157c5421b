package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.ArticleLike;

import java.time.Instant;

public interface ArticleLikeRepository extends IService<ArticleLike> {
    
    /**
     * 统计文章点赞数量
     */
    int countByArticleId(String articleId);
    
    /**
     * 统计指定时间后的文章点赞数量
     */
    int countByArticleIdAfterTime(String articleId, Instant afterTime);
    
    /**
     * 统计指定时间区间内的文章点赞数量
     */
    int countByArticleIdBetweenTime(String articleId, Instant startTime, Instant endTime);
} 