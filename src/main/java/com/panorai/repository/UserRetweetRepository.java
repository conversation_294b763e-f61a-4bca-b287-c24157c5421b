package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.UserRetweet;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface UserRetweetRepository extends IService<UserRetweet> {
    // 转发推文
    boolean retweet(String userId, String voiceId);

    // 取消转发
    boolean unretweet(String userId, String voiceId);

    // 获取用户的转发列表
    List<UserRetweet> getUserRetweets(String userId);

    // 获取推文的转发列表
    List<UserRetweet> getTweetRetweets(String voiceId);

    // 检查用户是否已转发某推文
    boolean hasRetweeted(String userId, String voiceId);

    // 获取推文的转发数量
    long getForwardCount(String voiceId);

    // 获取用户的转发总数
    long getUserTotalRetweets(String userId);
}
