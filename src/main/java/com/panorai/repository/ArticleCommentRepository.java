package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.ArticleComment;

public interface ArticleCommentRepository extends IService<ArticleComment> {
    
    /**
     * 统计文章评论数量
     */
    int countByArticleId(String articleId);
    
    /**
     * 统计指定时间后的文章评论数量
     */
    int countByArticleIdAfterTime(String articleId, java.time.Instant afterTime);
    
    /**
     * 统计指定时间区间内的文章评论数量
     */
    int countByArticleIdBetweenTime(String articleId, java.time.Instant startTime, java.time.Instant endTime);
} 