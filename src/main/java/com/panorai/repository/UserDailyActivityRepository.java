package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.UserDailyActivity;

import java.time.LocalDate;
import java.util.Optional;

public interface UserDailyActivityRepository extends IService<UserDailyActivity> {
    
    /**
     * 根据用户ID和日期查询活动记录
     */
    Optional<UserDailyActivity> getByUserIdAndDate(String userId, LocalDate activityDate);
    
    /**
     * 记录用户活动
     */
    void recordUserActivity(String userId, String ipAddress);
    
    /**
     * 获取用户最近7天的登录天数
     */
    long getRecentLoginDays(String userId);
} 