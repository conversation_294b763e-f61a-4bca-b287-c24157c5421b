package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.BlockedUser;

import java.util.List;

public interface BlockedUserRepository extends IService<BlockedUser> {
    // 拉黑用户
    boolean blockUser(String userId, String blockedUserId);
    
    // 取消拉黑
    boolean unblockUser(String userId, String blockedUserId);
    
    // 获取用户拉黑列表
    List<BlockedUser> getBlockedUsers(String userId);
    
    // 检查是否已拉黑某用户
    boolean isBlocked(String userId, String blockedUserId);
    
    // 获取拉黑用户数量
    long getBlockedCount(String userId);
    
    // 获取被拉黑的用户ID列表
    List<String> getBlockedUserIds(String userId);

    List<BlockedUser> getInviableUserIds(String userId);
}