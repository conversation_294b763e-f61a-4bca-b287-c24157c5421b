package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("voice_stats")
public class VoiceStat {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String voiceId;

    /**
     * AI基础分，0.0-1.0
     */
    private Double aiScore;

    private Integer likedCount;

    private Integer forwardedCount;

    private Integer repliedCount;

    private Instant createdAt;

    private Instant updatedAt;
}
