package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.panorai.utils.IdUtils;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("subscriptions")
public class Subscription {
    @TableId
    private String id;
    
    // 计划名称（basic/pro/enterprise）
    private String plan;
    
    // 用户ID
    private String userId;
    
    // Stripe相关ID
    private String stripeCustomerId;
    private String stripeSubscriptionId;
    
    // 订阅状态
    private String status;
    
    // 计费周期
    private Instant periodStart;
    private Instant periodEnd;
    
    // 取消标志
    private Boolean cancelAtPeriodEnd;
    
    // 试用期
    private Instant trialStart;
    private Instant trialEnd;
    
    // 座位数（团队订阅）
    private Integer seats;
    
    // Stripe价格ID
    private String stripePriceId;
    
    // 创建和更新时间
    private Instant createdAt;
    private Instant updatedAt;
    
    public Subscription() {
        this.id = IdUtils.generateId();
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
        this.cancelAtPeriodEnd = false;
        this.seats = 1;
    }
}