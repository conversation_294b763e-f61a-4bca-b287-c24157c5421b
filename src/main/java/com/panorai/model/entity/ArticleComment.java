package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@TableName("article_comments")
@NoArgsConstructor
public class ArticleComment {
    @TableId
    private Long id;
    
    private String commentId;
    
    private String content;
    
    private String authorId;
    
    private String articleId;
    
    private String parentId;
    
    private String rootCommentId;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private LocalDateTime deletedAt;
} 