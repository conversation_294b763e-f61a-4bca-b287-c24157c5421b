package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.panorai.utils.IdUtils;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("users")
public class User {
    @TableId
    private String id;
    private String name;
    private String email;
    private Boolean emailVerified;
    private String image;
    private Instant createdAt;
    private Instant updatedAt;

    // 默认无参构造函数，MyBatis Plus需要
    public User() {
        this.id = IdUtils.generateShortId();
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
        this.emailVerified = false;
    }

    public User(String email) {
        this();  // 调用无参构造函数
        this.email = email;
        this.emailVerified = true;
    }
} 