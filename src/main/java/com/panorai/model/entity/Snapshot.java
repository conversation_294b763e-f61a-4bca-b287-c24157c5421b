package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;

/**
 * 数据快照实体类
 * 用于存储数据更改前和删除前的快照信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("snapshots")
@Slf4j
public class Snapshot {
    
    /**
     * 快照记录主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 原表名称
     */
    private String tableName;
    
    /**
     * 快照数据，JSON格式存储原始记录
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object snapshotData;
    
    /**
     * 操作类型：UPDATE-更新前快照，DELETE-删除前快照
     */
    private String operationType;
    
    /**
     * 执行操作的用户ID，系统操作时可为空
     */
    private String userId;
    
    /**
     * 操作来源IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理信息
     */
    private String userAgent;
    
    /**
     * 快照创建时间
     */
    private Instant createdAt;
} 