package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("user_profiles")
public class UserProfile {
    @TableId
    private String id;
    private String userId;
    private String description;
    private String location;
    private String website;
    private String photoUrl = "profile/default-avatar.png";
    private String headerUrl = "profile/default-header.png";
    private String username;
    private String gender = "preferNotToSay";
    private String pronoun = "They";
    private Boolean isPremium = false;
    private Instant createdAt = Instant.now();
    private Instant updatedAt = Instant.now();
    private String visibility = "public";
    private Boolean activeUser = false;

    public void updateActive(Boolean activeUser) {
        this.activeUser = activeUser;
    }

    public static UserProfile defaultProfile(String userId) {
        UserProfile profile = new UserProfile();
        profile.setId(userId);
        profile.setUserId(userId);
        profile.setUsername(randomUsername(userId));
        return profile;
    }

    public static String randomUsername(String userId) {
        String userIdPrefix = userId.length() >= 3 ? userId.substring(0, 5) : userId;
        return "Qöo_" + userIdPrefix;
    }
}
