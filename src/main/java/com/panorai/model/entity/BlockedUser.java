package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("blocked_users")
public class BlockedUser {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String userId;
    private String blockedUserId;
    private Instant createdAt;
    private Instant updatedAt;
}