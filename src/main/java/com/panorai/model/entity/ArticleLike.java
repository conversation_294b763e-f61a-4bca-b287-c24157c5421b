package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@TableName("article_likes")
@NoArgsConstructor
public class ArticleLike {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String articleId;
    
    private String userId;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime deletedAt;
} 