package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("voice_view_history")
public class VoiceViewHistory {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String voiceId;

    private Instant createdAt;

    public VoiceViewHistory(String userId, String voiceId) {
        this.userId = userId;
        this.voiceId = voiceId;
        this.createdAt = Instant.now();
    }
}