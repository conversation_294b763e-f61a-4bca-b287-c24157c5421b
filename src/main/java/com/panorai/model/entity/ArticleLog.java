package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

@Data
@TableName("article_logs")
@NoArgsConstructor
public class ArticleLog {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String userId;
    
    private String articleId;
    
    private String ipAddress;
    
    private OffsetDateTime createdAt;
} 