package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("accounts")
public class Account {
    @TableId
    private String id;
    private String accountId;
    private String providerId;
    private String userId;
    private String accessToken;
    private String refreshToken;
    private String idToken;
    private Instant accessTokenExpiresAt;
    private Instant refreshTokenExpiresAt;
    private String scope;
    private String password;
    private Instant createdAt;
    private Instant updatedAt;
} 