package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.panorai.utils.IdUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Optional;

@Data
@TableName("tweets")
@NoArgsConstructor
public class Voice {
    @TableId
    private String id;

    private String text;

    private Instant createdAt = Instant.now();

    private String authorId;

    private String photoUrl;

    private Boolean isRetweet = false;

    private String retweetOfId;

    private Boolean isReply = false;

    private String repliedToId;

    private Boolean isPinned = false;

    private Boolean isHidden = false;

    private String warningText;

    private String visibility = "public";

    private String type = "voice";

    private String relatedId;

    private String mainVoiceId;
    private String mainReplyId;
    
    // 积分系统相关字段
    private Boolean isFeatured = false;

    public Voice reply(String text, String authorId, String photoUrl) {
        boolean isReply = this.isReply;
        String mainVoiceId = this.mainVoiceId;
        String mainReplyId = Optional.ofNullable(this.mainReplyId).orElse(this.id);
        if (!isReply) {
            mainVoiceId = this.id;
            mainReplyId = null;
        }
        Voice reply = new Voice();
        reply.setId(IdUtils.generateId());
        reply.setText(text);
        reply.setAuthorId(authorId);
        reply.setPhotoUrl(photoUrl);
        reply.setIsReply(true);
        reply.setRepliedToId(this.id);
        reply.setMainVoiceId(mainVoiceId);
        reply.setMainReplyId(mainReplyId);
        reply.setRelatedId(this.id);
        return reply;
    }

    public Voice(Voice voice) {
        this.id = voice.getId();
        this.text = voice.getText();
        this.createdAt = voice.getCreatedAt();
        this.authorId = voice.getAuthorId();
        this.photoUrl = voice.getPhotoUrl();
        this.isRetweet = voice.getIsRetweet();
        this.retweetOfId = voice.getRetweetOfId();
        this.isReply = voice.getIsReply();
        this.repliedToId = voice.getRepliedToId();
        this.isPinned = voice.getIsPinned();
        this.isHidden = voice.getIsHidden();
        this.warningText = voice.getWarningText();
        this.visibility = voice.getVisibility();
        this.type = voice.getType();
        this.relatedId = voice.getRelatedId();
        this.mainVoiceId = voice.getMainVoiceId();
        this.mainReplyId = voice.getMainReplyId();
    }
}
