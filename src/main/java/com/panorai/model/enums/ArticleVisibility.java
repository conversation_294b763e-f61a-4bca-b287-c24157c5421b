package com.panorai.model.enums;

import lombok.Getter;

/**
 * 文章可见性枚举
 */
@Getter
public enum ArticleVisibility {
    PUBLIC("public", "公开"),
    ONLY_LOGGED_IN("onlyLoggedIn", "仅登录用户可见"),
    PRIVATE("private", "私有"),
    DRAFT("draft", "草稿");
    
    private final String code;
    private final String description;
    
    ArticleVisibility(String code, String description) {
        this.code = code;
        this.description = description;
    }
} 