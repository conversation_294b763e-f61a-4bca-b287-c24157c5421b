package com.panorai.model.enums;

import lombok.Getter;

/**
 * 快照操作类型枚举
 */
@Getter
public enum OperationType {
    
    /**
     * 更新操作 - 存储更新前的数据快照
     */
    UPDATE("UPDATE", "更新操作"),
    
    /**
     * 删除操作 - 存储删除前的数据快照
     */
    DELETE("DELETE", "删除操作");
    
    private final String code;
    private final String description;
    
    OperationType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据代码获取操作类型
     * @param code 操作代码
     * @return 操作类型枚举
     */
    public static OperationType fromCode(String code) {
        for (OperationType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的操作类型: " + code);
    }
} 