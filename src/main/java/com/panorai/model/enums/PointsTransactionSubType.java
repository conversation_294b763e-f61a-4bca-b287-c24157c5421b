package com.panorai.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 积分交易子类型枚举
 */
@Getter
@RequiredArgsConstructor
public enum PointsTransactionSubType {
    // 获得积分的子类型
    CONTENT_INTERACTION("content_interaction", "内容互动获得积分"),
    FEATURED_BONUS("featured_bonus", "精选奖励积分"),
    MANUAL_BONUS("manual_bonus", "管理员手动奖励"),
    
    // 调整类型
    DAILY_SETTLEMENT("daily_settlement", "每日结算"),
    ADMIN_ADJUSTMENT("admin_adjustment", "管理员调整"),
    
    // 提现类型
    WITHDRAWAL_REQUEST("withdrawal_request", "提现申请"),
    WITHDRAWAL_REFUND("withdrawal_refund", "提现退款");

    private final String code;
    private final String description;

    public static PointsTransactionSubType fromCode(String code) {
        for (PointsTransactionSubType subType : values()) {
            if (subType.code.equals(code)) {
                return subType;
            }
        }
        throw new IllegalArgumentException("未知的积分交易子类型: " + code);
    }
} 