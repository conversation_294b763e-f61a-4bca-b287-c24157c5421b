package com.panorai.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 积分配置键枚举
 */
@Getter
@RequiredArgsConstructor
public enum PointsConfigKeys {
    // 基础积分配置
    POINTS_PER_LIKE("points_per_like", "每个点赞获得的积分", 3),
    POINTS_PER_COMMENT("points_per_comment", "每个评论获得的积分", 10),
    POINTS_PER_VIEW("points_per_view", "每个浏览获得的积分", 1),
    POINTS_PER_SHARE("points_per_share", "每个分享获得的积分", 15),
    
    // 全局系数
    GLOBAL_POINTS_MULTIPLIER("global_points_multiplier", "全局积分系数（百分比）", 150),
    
    // 精选奖励
    FEATURED_BONUS("featured_bonus", "精选文章奖励积分", 2000),
    
    // 内容类型系数（百分比）
    CONTENT_TYPE_ORIGINAL("content_type_original", "原创内容积分系数（百分比）", 100),
    CONTENT_TYPE_DISTRIBUTION("content_type_distribution", "分发内容积分系数（百分比）", 60),
    CONTENT_TYPE_TRANSLATION("content_type_translation", "翻译内容积分系数（百分比）", 30);

    private final String key;
    private final String description;
    private final int defaultValue;
} 