package com.panorai.model.enums;

/**
 * OTP验证码类型枚举
 */
public enum OTPType {
    SIGN_IN("登录", "Sign In"),
    EMAIL_VERIFICATION("邮箱验证", "Email Verification"),
    FORGET_PASSWORD("重置密码", "Reset Password");

    private final String chineseName;
    private final String englishName;

    OTPType(String chineseName, String englishName) {
        this.chineseName = chineseName;
        this.englishName = englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }
} 