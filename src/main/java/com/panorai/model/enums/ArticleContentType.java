package com.panorai.model.enums;

import lombok.Getter;

/**
 * 文章内容类型枚举
 */
@Getter
public enum ArticleContentType {
    ORIGINAL("original", "原创"),
    DISTRIBUTION("distribution", "分发"),
    TRANSLATION("translation", "翻译");
    
    private final String code;
    private final String description;
    
    ArticleContentType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据code获取枚举
     */
    public static ArticleContentType fromCode(String code) {
        for (ArticleContentType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return ORIGINAL; // 默认返回原创
    }
} 