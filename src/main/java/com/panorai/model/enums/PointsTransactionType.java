package com.panorai.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 积分交易类型枚举
 */
@Getter
@RequiredArgsConstructor
public enum PointsTransactionType {
    EARN("earn", "获得积分"),
    WITHDRAW("withdraw", "提现"),
    ADJUST("adjust", "调整"),
    BONUS("bonus", "奖励");

    private final String code;
    private final String description;

    public static PointsTransactionType fromCode(String code) {
        for (PointsTransactionType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的积分交易类型: " + code);
    }
} 