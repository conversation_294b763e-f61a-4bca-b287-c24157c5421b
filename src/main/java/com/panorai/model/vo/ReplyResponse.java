package com.panorai.model.vo;

import com.panorai.model.entity.Voice;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode(callSuper = true)
public class ReplyResponse extends Voice {
    private final boolean ownVoice;
    private final String notificationUser;

    public ReplyResponse(Voice voice, boolean ownVoice, String notificationUser) {
        super(voice);
        this.ownVoice = ownVoice;
        this.notificationUser = notificationUser;
    }
}
