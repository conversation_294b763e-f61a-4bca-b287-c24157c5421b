package com.panorai.model.dto;

import lombok.Data;

import java.time.Instant;

/**
 * Voice浏览统计信息DTO
 */
@Data
public class VoiceViewStatistics {
    /**
     * Voice ID
     */
    private String voiceId;
    
    /**
     * 最后浏览时间
     */
    private Instant lastViewedTime;
    
    /**
     * 浏览次数
     */
    private Long viewCount;
    
    public VoiceViewStatistics() {}
    
    public VoiceViewStatistics(String voiceId, Instant lastViewedTime, Long viewCount) {
        this.voiceId = voiceId;
        this.lastViewedTime = lastViewedTime;
        this.viewCount = viewCount;
    }
}
