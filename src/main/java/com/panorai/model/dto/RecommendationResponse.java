package com.panorai.model.dto;

import com.panorai.model.entity.Voice;
import lombok.Data;
import java.util.List;

@Data
public class RecommendationResponse {
    private List<VoiceWithScore> voices;
    private Integer page;
    private Integer size;
    private Long total;
    private Boolean hasMore;

    @Data
    public static class VoiceWithScore {
        private Voice voice;
        private Double score;
        private String reason; // "recent", "popular", "following", "ai_recommended"
        private Double aiScore;
        private Double heatScore;
        private Double freshnessScore;
        private Double penaltyScore;
    }
}
