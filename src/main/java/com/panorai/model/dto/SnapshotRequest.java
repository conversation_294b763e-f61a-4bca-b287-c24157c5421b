package com.panorai.model.dto;

import com.panorai.model.enums.OperationType;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 快照请求 DTO
 * 用于接收创建快照的请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SnapshotRequest {
    
    /**
     * 原表名称 (必填)
     */
    @NotBlank(message = "表名不能为空")
    private String tableName;
    
    /**
     * 快照数据，JSON格式的原始记录 (必填)
     */
    @NotNull(message = "快照数据不能为空")
    private Object snapshotData;
    
    /**
     * 操作类型 (必填)
     */
    @NotNull(message = "操作类型不能为空")
    private OperationType operationType;
    
    /**
     * 执行操作的用户ID (可选)
     */
    private String userId;
    
    /**
     * 操作来源IP地址 (可选)
     */
    private String ipAddress;
    
    /**
     * 用户代理信息 (可选)
     */
    private String userAgent;
} 