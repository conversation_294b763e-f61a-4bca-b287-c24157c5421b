package com.panorai.model.dto;

import com.panorai.model.entity.Voice;
import lombok.Data;
import java.util.List;

@Data
public class VoiceCommentTreeDTO {
    
    @Data
    public static class VoiceCommentNode {
        private Voice comment;
        private List<VoiceReplyItem> replies;
        private int displayReplyCount;
        private int totalReplyCount;
        private boolean hasMoreReplies;
    }
    
    @Data
    public static class VoiceReplyItem {
        private Voice reply;
        private Voice repliedTo;
        private String repliedToAuthorName;
        private String repliedToAuthorId;
    }
    
    @Data
    public static class VoiceCommentTreeResponse {
        private Voice mainVoice;
        private List<VoiceCommentNode> comments;
        private int totalCommentCount;
        private boolean hasMoreComments;
    }
}