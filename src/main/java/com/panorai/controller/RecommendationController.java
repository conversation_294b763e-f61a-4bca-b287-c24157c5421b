package com.panorai.controller;

import com.panorai.model.dto.RecommendationRequest;
import com.panorai.model.entity.Voice;
import com.panorai.service.RecommendationService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/recommendations")
@RequiredArgsConstructor
public class RecommendationController {
    private final RecommendationService recommendationService;
    
    /**
     * 获取推荐推文列表
     */
    @PostMapping("/voices")
    public List<Voice> getRecommendations(@RequestBody RecommendationRequest request) {
        return recommendationService.getRecommendations(request.getUserId(), request);
    }
}
