package com.panorai.controller;

import com.panorai.model.dto.VoiceCommentTreeDTO.*;
import com.panorai.service.VoiceCommentTreeService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/voice/comments")
@RequiredArgsConstructor
public class VoiceCommentController {
    
    private final VoiceCommentTreeService voiceCommentTreeService;
    
    @GetMapping("/tree/{voiceId}")
    public ResponseEntity<VoiceCommentTreeResponse> getCommentTree(
            @PathVariable String voiceId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        VoiceCommentTreeResponse response = voiceCommentTreeService.buildCommentTree(voiceId, page, size);
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/replies/{mainReplyId}")
    public ResponseEntity<List<VoiceReplyItem>> loadMoreReplies(
            @PathVariable String mainReplyId,
            @RequestParam(defaultValue = "2") int offset) {
        
        List<VoiceReplyItem> replies = voiceCommentTreeService.loadMoreReplies(mainReplyId, offset);
        return ResponseEntity.ok(replies);
    }
}