package com.panorai.controller;

import com.panorai.model.entity.UserProfile;
import com.panorai.service.UserService;
import com.panorai.utils.RequestUtil;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserController {
    private final UserService userService;

    @PostMapping
    public UserProfile getUserProfile(String userId, HttpServletRequest request) {
        if (StringUtils.isBlank(userId)) {
            throw new IllegalArgumentException();
        }
        // 记录用户活动
        String ipAddress = RequestUtil.getClientIpAddress(request);
        userService.recordUserActivity(userId, ipAddress);
        
        return userService.getProfileAndCheckActive(userId);
    }
}
