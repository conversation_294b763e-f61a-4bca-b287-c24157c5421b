package com.panorai.controller;

import com.panorai.service.ReplyService;
import com.panorai.model.vo.ReplyRequest;
import com.panorai.model.vo.ReplyResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/reply")
@RequiredArgsConstructor
public class ReplyController {
    private final ReplyService replyService;

    @PostMapping
    public ReplyResponse reply(@RequestBody @Valid ReplyRequest request) {
        return replyService.reply(request);
    }
}
