package com.panorai.controller;

import com.panorai.service.VoiceStatService;
import com.panorai.model.vo.UpdateAiScoreRequest;
import com.panorai.model.vo.UpdateVoiceStatRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/voice/stat")
@RequiredArgsConstructor
public class VoiceStatController {
    private final VoiceStatService voiceStatService;

    @PostMapping
    public Long updateVoiceStat(@RequestBody @Valid UpdateVoiceStatRequest request) {
        return voiceStatService.updateVoiceStat(request);
    }

    @PostMapping("/ai-score")
    public void updateVoiceStat(@RequestBody @Valid UpdateAiScoreRequest request) {
        voiceStatService.updateAiScore(request);
    }
}
