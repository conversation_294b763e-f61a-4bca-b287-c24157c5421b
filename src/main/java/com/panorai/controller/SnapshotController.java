package com.panorai.controller;

import com.panorai.model.dto.SnapshotRequest;
import com.panorai.model.entity.Snapshot;
import com.panorai.service.SnapshotService;
import com.panorai.utils.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 快照控制器
 * 提供数据快照相关的 REST API 接口
 */
@RestController
@RequestMapping("/api/snapshots")
@RequiredArgsConstructor
@Slf4j
public class SnapshotController {

    private final SnapshotService snapshotService;

    /**
     * 创建数据快照
     *
     * @param request     快照请求
     * @param httpRequest HTTP请求对象
     * @return 创建的快照信息
     */
    @PostMapping
    public ResponseEntity<Void> createSnapshot(@Valid @RequestBody SnapshotRequest request,
                                               HttpServletRequest httpRequest) {
        request.setIpAddress(RequestUtil.getClientIpAddress(httpRequest));
        request.setUserAgent(RequestUtil.getUserAgent(httpRequest));

        snapshotService.createSnapshot(request);
        return ResponseEntity.ok(null);
    }

    /**
     * 根据表名和记录ID查询快照
     *
     * @param tableName 表名
     * @param recordId  记录ID
     * @return 快照列表
     */
    @GetMapping("/table/{tableName}/record/{recordId}")
    public ResponseEntity<List<Snapshot>> getSnapshotsByTableAndRecord(@PathVariable String tableName,
                                                                       @PathVariable String recordId) {

        List<Snapshot> snapshots = snapshotService.getSnapshotsByTableAndRecord(tableName, recordId);
        return ResponseEntity.ok(snapshots);

    }

    /**
     * 根据表名查询快照
     *
     * @param tableName 表名
     * @return 快照列表
     */
    @GetMapping("/table/{tableName}")
    public ResponseEntity<List<Snapshot>> getSnapshotsByTable(@PathVariable String tableName) {
        List<Snapshot> snapshots = snapshotService.getSnapshotsByTable(tableName);
        return ResponseEntity.ok(snapshots);
    }

    /**
     * 根据用户ID查询快照
     *
     * @param userId 用户ID
     * @return 快照列表
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<Snapshot>> getSnapshotsByUser(@PathVariable String userId) {
        List<Snapshot> snapshots = snapshotService.getSnapshotsByUser(userId);
        return ResponseEntity.ok(snapshots);
    }
} 