package com.panorai.controller;

import com.panorai.service.StripeSubscriptionService;

import java.util.Optional;
import com.stripe.exception.StripeException;
import com.stripe.model.checkout.Session;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/subscription")
@RequiredArgsConstructor
public class SubscriptionController {
    
    private final StripeSubscriptionService subscriptionService;
    
    @Data
    public static class CreateCheckoutRequest {
        private String userId;
        private String priceId;
    }
    
    @Data
    public static class UpdatePlanRequest {
        private String userId;
        private String newPriceId;
    }
    
    /**
     * 创建订阅Checkout会话
     */
    @PostMapping("/checkout")
    public ResponseEntity<?> createCheckoutSession(@RequestBody CreateCheckoutRequest request) {
        try {
            // 检查用户是否已有活跃订阅
            if (subscriptionService.hasValidSubscription(request.getUserId())) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "用户已有活跃的订阅"));
            }
            
            Session session = subscriptionService.createCheckoutSession(request.getUserId(), request.getPriceId());
            
            Map<String, String> response = new HashMap<>();
            response.put("checkoutUrl", session.getUrl());
            response.put("sessionId", session.getId());
            
            return ResponseEntity.ok(response);
        } catch (StripeException e) {
            log.error("创建Checkout会话失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "创建支付会话失败"));
        }
    }
    
    /**
     * 获取当前订阅信息
     */
    @GetMapping("/current/{userId}")
    public ResponseEntity<?> getCurrentSubscription(@PathVariable String userId) {
        Optional<com.panorai.model.entity.Subscription> subscription = 
            subscriptionService.getCurrentSubscription(userId);
        
        Map<String, Object> response = new HashMap<>();
        if (subscription.isPresent()) {
            com.panorai.model.entity.Subscription sub = subscription.get();
            response.put("hasSubscription", true);
            response.put("subscriptionId", sub.getStripeSubscriptionId());
            response.put("status", sub.getStatus());
            response.put("priceId", sub.getStripePriceId());
            response.put("plan", sub.getPlan());
            response.put("periodStart", sub.getPeriodStart());
            response.put("periodEnd", sub.getPeriodEnd());
            response.put("cancelAtPeriodEnd", sub.getCancelAtPeriodEnd());
            response.put("trialEnd", sub.getTrialEnd());
        } else {
            response.put("hasSubscription", false);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 取消订阅（周期结束时）
     */
    @PostMapping("/cancel")
    public ResponseEntity<?> cancelSubscription(@RequestBody Map<String, String> request) {
        try {
            String userId = request.get("userId");
            com.stripe.model.Subscription subscription = subscriptionService.cancelSubscription(userId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "订阅将在当前周期结束时取消");
            response.put("cancelAt", subscription.getCancelAt());
            
            return ResponseEntity.ok(response);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest()
                .body(Map.of("error", e.getMessage()));
        } catch (StripeException e) {
            log.error("取消订阅失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "取消订阅失败"));
        }
    }
    
    /**
     * 立即取消订阅
     */
    @PostMapping("/cancel-immediately")
    public ResponseEntity<?> cancelSubscriptionImmediately(@RequestBody Map<String, String> request) {
        try {
            String userId = request.get("userId");
            subscriptionService.cancelSubscriptionImmediately(userId);
            
            return ResponseEntity.ok(Map.of("message", "订阅已立即取消"));
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest()
                .body(Map.of("error", e.getMessage()));
        } catch (StripeException e) {
            log.error("立即取消订阅失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "取消订阅失败"));
        }
    }
    
    /**
     * 恢复订阅
     */
    @PostMapping("/resume")
    public ResponseEntity<?> resumeSubscription(@RequestBody Map<String, String> request) {
        try {
            String userId = request.get("userId");
            subscriptionService.resumeSubscription(userId);
            
            return ResponseEntity.ok(Map.of("message", "订阅已恢复"));
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest()
                .body(Map.of("error", e.getMessage()));
        } catch (StripeException e) {
            log.error("恢复订阅失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "恢复订阅失败"));
        }
    }
    
    /**
     * 更改订阅计划
     */
    @PostMapping("/update-plan")
    public ResponseEntity<?> updateSubscriptionPlan(@RequestBody UpdatePlanRequest request) {
        try {
            subscriptionService.updateSubscriptionPlan(request.getUserId(), request.getNewPriceId());
            
            return ResponseEntity.ok(Map.of("message", "订阅计划已更新"));
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest()
                .body(Map.of("error", e.getMessage()));
        } catch (StripeException e) {
            log.error("更新订阅计划失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "更新订阅计划失败"));
        }
    }
    
    /**
     * 创建Customer Portal会话
     */
    @PostMapping("/portal")
    public ResponseEntity<?> createPortalSession(@RequestBody Map<String, String> request) {
        try {
            String userId = request.get("userId");
            com.stripe.model.billingportal.Session session = 
                subscriptionService.createPortalSession(userId);
            
            Map<String, String> response = new HashMap<>();
            response.put("portalUrl", session.getUrl());
            
            return ResponseEntity.ok(response);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest()
                .body(Map.of("error", e.getMessage()));
        } catch (StripeException e) {
            log.error("创建Portal会话失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "创建管理门户失败"));
        }
    }
}