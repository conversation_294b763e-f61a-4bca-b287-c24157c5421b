package com.panorai.controller;

import com.panorai.config.StripeConfig;
import com.panorai.service.StripeSubscriptionService;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.model.Event;
import com.stripe.model.EventDataObjectDeserializer;
import com.stripe.model.Subscription;
import com.stripe.model.Customer;
import com.stripe.model.StripeObject;
import com.stripe.net.Webhook;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/stripe/webhook")
@RequiredArgsConstructor
public class StripeWebhookController {
    
    private final StripeConfig stripeConfig;
    private final StripeSubscriptionService subscriptionService;
    
    @PostMapping
    public ResponseEntity<String> handleWebhook(
            @RequestBody String payload,
            @RequestHeader("Stripe-Signature") String sigHeader) {
        
        Event event;
        
        try {
            // 验证webhook签名
            event = Webhook.constructEvent(
                payload, sigHeader, stripeConfig.getWebhookSecret()
            );
        } catch (SignatureVerificationException e) {
            log.error("Webhook签名验证失败", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid signature");
        } catch (Exception e) {
            log.error("Webhook处理异常", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid payload");
        }
        
        // 反序列化事件数据
        EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
        StripeObject stripeObject = null;
        
        if (dataObjectDeserializer.getObject().isPresent()) {
            stripeObject = dataObjectDeserializer.getObject().get();
        } else {
            log.error("事件数据反序列化失败");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Deserialization failed");
        }
        
        // 处理不同类型的事件
        switch (event.getType()) {
            case "customer.created":
                handleCustomerCreated((Customer) stripeObject);
                break;
                
            case "checkout.session.completed":
                handleCheckoutSessionCompleted((com.stripe.model.checkout.Session) stripeObject);
                break;
                
            case "customer.subscription.created":
            case "customer.subscription.updated":
                handleSubscriptionUpdate((Subscription) stripeObject);
                break;
                
            case "customer.subscription.deleted":
                handleSubscriptionDeleted((Subscription) stripeObject);
                break;
                
            case "customer.subscription.trial_will_end":
                handleTrialWillEnd((Subscription) stripeObject);
                break;
                
            case "invoice.payment_succeeded":
                log.info("发票支付成功: {}", event.getId());
                break;
                
            case "invoice.payment_failed":
                log.warn("发票支付失败: {}", event.getId());
                // 可以在这里发送邮件通知用户
                break;
                
            default:
                log.info("收到未处理的事件类型: {}", event.getType());
        }
        
        // 返回200表示成功接收
        return ResponseEntity.ok("Webhook received");
    }
    
    private void handleSubscriptionUpdate(Subscription subscription) {
        try {
            log.info("处理订阅更新事件: {}", subscription.getId());
            subscriptionService.handleSubscriptionUpdate(subscription);
        } catch (Exception e) {
            log.error("处理订阅更新失败", e);
            // 抛出异常让Stripe重试
            throw new RuntimeException("Failed to handle subscription update", e);
        }
    }
    
    private void handleSubscriptionDeleted(Subscription subscription) {
        try {
            log.info("处理订阅删除事件: {}", subscription.getId());
            subscriptionService.handleSubscriptionDeleted(subscription);
        } catch (Exception e) {
            log.error("处理订阅删除失败", e);
            throw new RuntimeException("Failed to handle subscription deletion", e);
        }
    }
    
    private void handleCustomerCreated(Customer customer) {
        try {
            log.info("处理客户创建事件: {}", customer.getId());
            subscriptionService.handleCustomerCreated(customer);
        } catch (Exception e) {
            log.error("处理客户创建失败", e);
            throw new RuntimeException("Failed to handle customer creation", e);
        }
    }
    
    private void handleCheckoutSessionCompleted(com.stripe.model.checkout.Session session) {
        try {
            log.info("处理Checkout会话完成事件: {}", session.getId());
            subscriptionService.handleCheckoutSessionCompleted(session);
        } catch (Exception e) {
            log.error("处理Checkout会话完成失败", e);
            throw new RuntimeException("Failed to handle checkout session", e);
        }
    }
    
    private void handleTrialWillEnd(Subscription subscription) {
        log.info("试用期即将结束: {}", subscription.getId());
        // 可以在这里发送提醒邮件
    }
}