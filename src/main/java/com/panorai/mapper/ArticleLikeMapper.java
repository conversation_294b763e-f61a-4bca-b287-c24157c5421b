package com.panorai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panorai.model.entity.ArticleLike;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.Instant;

@Mapper
public interface ArticleLikeMapper extends BaseMapper<ArticleLike> {
    
    /**
     * 统计文章点赞数量
     */
    @Select("SELECT COUNT(*) FROM article_likes WHERE article_id = #{articleId} AND deleted_at IS NULL")
    int countByArticleId(String articleId);
    
    /**
     * 统计指定时间后的文章点赞数量
     */
    @Select("SELECT COUNT(*) FROM article_likes WHERE article_id = #{articleId} AND created_at > #{afterTime} AND deleted_at IS NULL")
    int countByArticleIdAfterTime(String articleId, Instant afterTime);
    
    /**
     * 统计指定时间区间内的文章点赞数量
     */
    @Select("SELECT COUNT(*) FROM article_likes WHERE article_id = #{articleId} AND created_at > #{startTime} AND created_at <= #{endTime} AND deleted_at IS NULL")
    int countByArticleIdBetweenTime(String articleId, Instant startTime, Instant endTime);
} 