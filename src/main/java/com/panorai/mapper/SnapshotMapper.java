package com.panorai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panorai.model.entity.Snapshot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 快照数据访问层接口
 */
@Mapper
public interface SnapshotMapper extends BaseMapper<Snapshot> {
    
    /**
     * 根据表名和记录ID查询快照
     * @param tableName 表名
     * @param recordId 记录ID
     * @return 快照列表
     */
    @Select("SELECT * FROM snapshots WHERE table_name = #{tableName} AND snapshot_data->>'id' = #{recordId} ORDER BY created_at DESC")
    List<Snapshot> findByTableNameAndRecordId(@Param("tableName") String tableName, @Param("recordId") String recordId);
    
    /**
     * 根据表名查询快照
     * @param tableName 表名
     * @return 快照列表
     */
    @Select("SELECT * FROM snapshots WHERE table_name = #{tableName} ORDER BY created_at DESC")
    List<Snapshot> findByTableName(@Param("tableName") String tableName);
    
    /**
     * 根据用户ID查询快照
     * @param userId 用户ID
     * @return 快照列表
     */
    @Select("SELECT * FROM snapshots WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<Snapshot> findByUserId(@Param("userId") String userId);
} 