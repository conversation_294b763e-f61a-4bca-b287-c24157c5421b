package com.panorai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panorai.model.entity.Article;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ArticleMapper extends BaseMapper<Article> {
    
    /**
     * 查找活跃的文章ID列表（供积分计算使用）
     */
    @Select("SELECT article_id FROM articles WHERE deleted_at IS NULL AND visibility = 'public' AND is_hidden = false ORDER BY created_at DESC")
    List<String> findActiveContentIds();
} 