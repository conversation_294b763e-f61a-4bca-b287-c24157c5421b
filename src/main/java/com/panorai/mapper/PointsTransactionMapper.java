package com.panorai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panorai.model.entity.PointsTransaction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface PointsTransactionMapper extends BaseMapper<PointsTransaction> {
    
    /**
     * 根据用户ID查找交易记录（限制数量）
     */
    @Select("SELECT * FROM points_transactions WHERE user_id = #{userId} ORDER BY created_at DESC LIMIT #{limit}")
    List<PointsTransaction> findByUserIdWithLimit(@Param("userId") String userId, @Param("limit") int limit);
    
    /**
     * 根据用户ID和交易类型查找记录（限制数量）
     */
    @Select("SELECT * FROM points_transactions WHERE user_id = #{userId} AND type = #{type} ORDER BY created_at DESC LIMIT #{limit}")
    List<PointsTransaction> findByUserIdAndTypeWithLimit(@Param("userId") String userId, @Param("type") String type, @Param("limit") int limit);
    
    /**
     * 根据时间范围查找交易记录
     */
    @Select("SELECT * FROM points_transactions WHERE created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<PointsTransaction> findByCreatedAtBetween(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);
    
    /**
     * 统计用户指定类型的积分总量
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM points_transactions WHERE user_id = #{userId} AND type = #{type}")
    long sumAmountByUserIdAndType(@Param("userId") String userId, @Param("type") String type);
    
    /**
     * 根据内容ID查找相关交易
     */
    @Select("SELECT * FROM points_transactions WHERE content_id = #{contentId} ORDER BY created_at DESC")
    List<PointsTransaction> findByContentId(@Param("contentId") String contentId);
    
    /**
     * 查找内容的最后一次互动积分交易记录
     */
    @Select("SELECT * FROM points_transactions WHERE content_id = #{contentId} AND sub_type = 'content_interaction' ORDER BY created_at DESC LIMIT 1")
    List<PointsTransaction> findLastContentInteractionTransaction(@Param("contentId") String contentId);
    
    /**
     * 根据内容ID和交易子类型查找记录
     */
    @Select("SELECT * FROM points_transactions WHERE content_id = #{contentId} AND sub_type = #{subType} ORDER BY created_at DESC")
    List<PointsTransaction> findByContentIdAndSubType(@Param("contentId") String contentId, @Param("subType") String subType);
    
    /**
     * 计算指定内容的总积分
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM points_transactions WHERE content_id = #{contentId}")
    long sumPointsByContentId(@Param("contentId") String contentId);
} 