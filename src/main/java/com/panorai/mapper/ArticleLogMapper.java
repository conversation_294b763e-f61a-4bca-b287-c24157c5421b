package com.panorai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panorai.model.entity.ArticleLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface ArticleLogMapper extends BaseMapper<ArticleLog> {
    
    /**
     * 统计文章浏览次数
     */
    @Select("SELECT COUNT(*) FROM article_logs WHERE article_id = #{articleId}")
    int countByArticleId(String articleId);
} 