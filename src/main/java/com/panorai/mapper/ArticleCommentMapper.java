package com.panorai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panorai.model.entity.ArticleComment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface ArticleCommentMapper extends BaseMapper<ArticleComment> {
    
    /**
     * 统计文章评论数量
     */
    @Select("SELECT COUNT(*) FROM article_comments WHERE article_id = #{articleId} AND deleted_at IS NULL")
    int countByArticleId(String articleId);
} 