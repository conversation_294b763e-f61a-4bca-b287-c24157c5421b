package com.panorai.service;

import com.panorai.model.dto.VoiceCommentTreeDTO.*;
import com.panorai.model.entity.Voice;
import com.panorai.repository.VoiceRepository;
import com.panorai.model.dto.CommentReplyCountDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class VoiceCommentTreeService {
    
    private final VoiceRepository voiceRepository;
    private final UserService userService;
    
    private static final int DEFAULT_COMMENT_LIMIT = 10;
    private static final int DEFAULT_REPLY_PREVIEW_COUNT = 2;
    
    public VoiceCommentTreeResponse buildCommentTree(String mainVoiceId, int page, int size) {
        // 1. 获取主帖
        Voice mainVoice = voiceRepository.getById(mainVoiceId);
        if (mainVoice == null || mainVoice.getIsReply()) {
            throw new IllegalArgumentException("Invalid main voice id");
        }
        
        // 2. 获取一级评论
        int offset = page * size;
        List<Voice> firstLevelComments = voiceRepository.getFirstLevelComments(mainVoiceId, size, offset);
        
        // 3. 获取总评论数
        long totalCommentCount = voiceRepository.countFirstLevelComments(mainVoiceId);
        
        // 4. 构建评论节点
        List<VoiceCommentNode> commentNodes = buildCommentNodes(firstLevelComments);
        
        // 5. 构建响应
        VoiceCommentTreeResponse response = new VoiceCommentTreeResponse();
        response.setMainVoice(mainVoice);
        response.setComments(commentNodes);
        response.setTotalCommentCount((int) totalCommentCount);
        response.setHasMoreComments(offset + size < totalCommentCount);
        
        return response;
    }
    
    private List<VoiceCommentNode> buildCommentNodes(List<Voice> firstLevelComments) {
        if (CollectionUtils.isEmpty(firstLevelComments)) {
            return Collections.emptyList();
        }
        
        // 批量获取回复数
        List<String> commentIds = firstLevelComments.stream()
                .map(Voice::getId)
                .collect(Collectors.toList());
        
        List<CommentReplyCountDTO> replyCounts = voiceRepository.batchCountReplies(commentIds);
        Map<String, Long> replyCountMap = replyCounts.stream()
                .collect(Collectors.toMap(
                    CommentReplyCountDTO::getMainReplyId,
                    CommentReplyCountDTO::getReplyCount
                ));
        
        return firstLevelComments.stream()
                .map(comment -> buildCommentNode(comment, replyCountMap))
                .collect(Collectors.toList());
    }
    
    private VoiceCommentNode buildCommentNode(Voice comment, Map<String, Long> replyCountMap) {
        VoiceCommentNode node = new VoiceCommentNode();
        node.setComment(comment);
        
        // 获取该评论的回复
        List<Voice> allReplies = voiceRepository.getRepliesByMainReplyId(comment.getId());
        long totalReplyCount = replyCountMap.getOrDefault(comment.getId(), 0L);
        
        // 只展示前N条回复
        List<Voice> displayReplies = allReplies.stream()
                .limit(DEFAULT_REPLY_PREVIEW_COUNT)
                .collect(Collectors.toList());
        
        // 构建回复项（包含回复链信息）
        List<VoiceReplyItem> replyItems = buildReplyItems(displayReplies, allReplies);
        
        node.setReplies(replyItems);
        node.setDisplayReplyCount(replyItems.size());
        node.setTotalReplyCount((int) totalReplyCount);
        node.setHasMoreReplies(totalReplyCount > DEFAULT_REPLY_PREVIEW_COUNT);
        
        return node;
    }
    
    private List<VoiceReplyItem> buildReplyItems(List<Voice> displayReplies, List<Voice> allReplies) {
        if (CollectionUtils.isEmpty(displayReplies)) {
            return Collections.emptyList();
        }
        
        // 创建ID到Voice的映射，用于快速查找
        Map<String, Voice> voiceMap = allReplies.stream()
                .collect(Collectors.toMap(Voice::getId, v -> v));
        
        // 获取所有需要的repliedToId
        Set<String> repliedToIds = displayReplies.stream()
                .map(Voice::getRepliedToId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        // 补充获取不在allReplies中的Voice（可能回复的是一级评论）
        Set<String> missingIds = repliedToIds.stream()
                .filter(id -> !voiceMap.containsKey(id))
                .collect(Collectors.toSet());
        
        if (!missingIds.isEmpty()) {
            List<Voice> additionalVoices = voiceRepository.getVoicesByIds(new ArrayList<>(missingIds));
            additionalVoices.forEach(v -> voiceMap.put(v.getId(), v));
        }
        
        return displayReplies.stream()
                .map(reply -> {
                    VoiceReplyItem item = new VoiceReplyItem();
                    item.setReply(reply);
                    
                    // 设置被回复的对象
                    Voice repliedTo = voiceMap.get(reply.getRepliedToId());
                    if (repliedTo != null) {
                        item.setRepliedTo(repliedTo);
                        item.setRepliedToAuthorId(repliedTo.getAuthorId());
                        // 这里可以通过UserService获取用户名
                        // item.setRepliedToAuthorName(userService.getUserName(repliedTo.getAuthorId()));
                    }
                    
                    return item;
                })
                .collect(Collectors.toList());
    }
    
    // 加载更多回复
    public List<VoiceReplyItem> loadMoreReplies(String mainReplyId, int offset) {
        List<Voice> allReplies = voiceRepository.getRepliesByMainReplyId(mainReplyId);
        
        // 跳过已展示的回复
        List<Voice> moreReplies = allReplies.stream()
                .skip(offset)
                .collect(Collectors.toList());
        
        return buildReplyItems(moreReplies, allReplies);
    }
}