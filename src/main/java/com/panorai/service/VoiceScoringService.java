package com.panorai.service;

import com.panorai.model.dto.LastViewedTimeDTO;
import com.panorai.model.entity.Voice;
import com.panorai.model.entity.VoiceStat;
import com.panorai.repository.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class VoiceScoringService {
    private final VoiceViewHistoryRepository voiceViewHistoryRepository;
    private final VoiceStatRepo voiceStatRepo;

    public double calculatePenaltyScore(LastViewedTimeDTO dto, Instant createdAt, Instant now) {
        if (Objects.isNull(dto)) {
            return 1.0;
        }

        long seenAgoMinutes = Duration.between(dto.lastViewedTime(), now).toMinutes();

        // 惩罚衰减周期设为7天 = 10080分钟，使用指数衰减
        double lambda = 0.0003; // 衰减速度，数值可调
        double penalty = Math.exp(-lambda * seenAgoMinutes);

        double timePenalty =  Math.max(penalty, 0.95);
        // 新增：观看次数惩罚
        double countPenalty;
        Integer viewCount = dto.viewCount();
        double countLambda = createdAt.isAfter(createdAt.minusSeconds(60 * 60 * 12)) ? 0.15 : 0.3;
        if (viewCount <= 1) {
            countPenalty = 1.0;
        } else {
            countPenalty = Math.exp(-countLambda * (viewCount - 1));
        }

        return timePenalty * countPenalty;
    }

    /**
     * 公式：(ai * 0.6 + heat * 0.4) * freshness * penalty
     *
     * @param voice
     * @param stat
     * @param lastViewedTimeDTO
     * @param now
     * @return
     */
    public double calculateFinalScore(Voice voice, VoiceStat stat, LastViewedTimeDTO lastViewedTimeDTO, Instant now) {
        double fixedScore = calculateFixedScore(stat);
        double freshnessScore = calculateFreshnessScore(voice.getCreatedAt(), now);
        double penaltyScore = calculatePenaltyScore(lastViewedTimeDTO, voice.getCreatedAt(), now);

        return fixedScore * freshnessScore * penaltyScore;
    }

    public Map<String, Double> calculateScore(Set<Voice> candidateVoices, String userId) {
        Set<String> voiceIds = candidateVoices.stream().map(Voice::getId).collect(Collectors.toSet());
        List<VoiceStat> voiceStats = voiceStatRepo.getVoiceStats(voiceIds);
        Map<String, VoiceStat> statMap = voiceStats.stream().collect(Collectors.toMap(VoiceStat::getVoiceId, (i) -> i));
        Map<String, Voice> voiceMap = candidateVoices.stream().collect(Collectors.toMap(Voice::getId, (i) -> i));
        Map<String, LastViewedTimeDTO> lastViewedTime = voiceViewHistoryRepository.getViewStatistics(voiceIds, userId);
        Instant now = Instant.now();
        return voiceMap.values()
                .parallelStream()
                .map(Voice::getId)
                .collect(
                        Collectors.toMap(
                                i -> i,
                                (i) -> calculateFinalScore(
                                        voiceMap.get(i),
                                        statMap.get(i),
                                        lastViewedTime.get(i),
                                        now
                                )
                        )
                );
    }

    private double calculateFixedScore(VoiceStat stat) {
        double aiScore = getAiScore(stat);
        double heatScore = calculateHeatScore(stat);
        return aiScore * 0.6 + heatScore * 0.4;
    }


    private double getAiScore(VoiceStat voiceStat) {
        // if aiScore is null, return 0.5
        return Optional.ofNullable(voiceStat).map(VoiceStat::getAiScore).orElse(0.5);
    }

    private double calculateHeatScore(VoiceStat stat) {
        int likedCount = Optional.ofNullable(stat).map(VoiceStat::getLikedCount).orElse(0);
        int forwardedCount = Optional.ofNullable(stat).map(VoiceStat::getForwardedCount).orElse(0);
        int repliedCount = Optional.ofNullable(stat).map(VoiceStat::getRepliedCount).orElse(0);

        double totalScore = likedCount * 1.0 + forwardedCount * 2.0 + repliedCount * 3.0;

        // 100为满分基准, 归一化到0-1范围，使用对数函数避免极值
        return Math.min(1.0, Math.log(totalScore + 1) / Math.log(100));
    }

    public double calculateFreshnessScore(Instant createdAt, Instant now) {
//        long hours = Duration.between(createdAt, now).toHours();
//        // λ = 0.05 可调，指数衰减
//        return Math.exp(-0.05 * hours);
        long hours = Duration.between(createdAt, now).toHours();

        // raise voice scoring weight for recent voices within 2 & 12 hours
        if (hours < 1) {
            return 1.1;
        }
        if (hours <= 6) {
            return 1.05;
        }
        if (hours < 12) {
            return 1.0;
        }

        double raw = Math.exp(-0.02 * hours);
        return Math.max(raw, 0.2); // 保底 0.2 分，旧帖也能参与竞争
    }
}
