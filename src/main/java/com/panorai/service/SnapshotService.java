package com.panorai.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.panorai.model.dto.SnapshotRequest;
import com.panorai.model.entity.Snapshot;
import com.panorai.model.enums.OperationType;
import com.panorai.repository.SnapshotRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

/**
 * 快照服务类
 * 提供数据快照的创建、查询等功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SnapshotService {
    
    private final SnapshotRepository snapshotRepository;
    private final ObjectMapper objectMapper;
    
    /**
     * 创建数据快照
     * @param request 快照请求
     * @return 创建的快照实体
     */
    @Transactional
    public Snapshot createSnapshot(SnapshotRequest request) {
        try {
            // 将快照数据转换为 JSON 字符串
            String snapshotDataJson = objectMapper.writeValueAsString(request.getSnapshotData());
            
            // 构建快照实体
            Snapshot snapshot = Snapshot.builder()
                    .tableName(request.getTableName())
                    .snapshotData(snapshotDataJson)
                    .operationType(request.getOperationType().getCode())
                    .userId(request.getUserId())
                    .ipAddress(request.getIpAddress())
                    .userAgent(request.getUserAgent())
                    .createdAt(Instant.now())
                    .build();
            
            // 保存快照
            snapshotRepository.save(snapshot);
            
            log.info("成功创建快照 - 表名: {}, 操作类型: {}, 用户ID: {}", 
                    request.getTableName(), request.getOperationType().getCode(), request.getUserId());
            
            return snapshot;
        } catch (Exception e) {
            log.error("创建快照失败 - 表名: {}, 操作类型: {}, 错误: {}", 
                    request.getTableName(), request.getOperationType().getCode(), e.getMessage(), e);
            throw new RuntimeException("创建快照失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 便捷方法：创建更新前快照
     * @param tableName 表名
     * @param originalData 原始数据
     * @param userId 用户ID
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 创建的快照实体
     */
    public Snapshot createUpdateSnapshot(String tableName, Object originalData, String userId, String ipAddress, String userAgent) {
        SnapshotRequest request = SnapshotRequest.builder()
                .tableName(tableName)
                .snapshotData(originalData)
                .operationType(OperationType.UPDATE)
                .userId(userId)
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .build();
        return createSnapshot(request);
    }
    
    /**
     * 便捷方法：创建删除前快照
     * @param tableName 表名
     * @param originalData 原始数据
     * @param userId 用户ID
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 创建的快照实体
     */
    public Snapshot createDeleteSnapshot(String tableName, Object originalData, String userId, String ipAddress, String userAgent) {
        SnapshotRequest request = SnapshotRequest.builder()
                .tableName(tableName)
                .snapshotData(originalData)
                .operationType(OperationType.DELETE)
                .userId(userId)
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .build();
        return createSnapshot(request);
    }
    
    /**
     * 根据表名和记录ID查询快照
     * @param tableName 表名
     * @param recordId 记录ID
     * @return 快照列表
     */
    public List<Snapshot> getSnapshotsByTableAndRecord(String tableName, String recordId) {
        return snapshotRepository.findByTableNameAndRecordId(tableName, recordId);
    }
    
    /**
     * 根据表名查询快照
     * @param tableName 表名
     * @return 快照列表
     */
    public List<Snapshot> getSnapshotsByTable(String tableName) {
        return snapshotRepository.findByTableName(tableName);
    }
    
    /**
     * 根据用户ID查询快照
     * @param userId 用户ID
     * @return 快照列表
     */
    public List<Snapshot> getSnapshotsByUser(String userId) {
        return snapshotRepository.findByUserId(userId);
    }
} 