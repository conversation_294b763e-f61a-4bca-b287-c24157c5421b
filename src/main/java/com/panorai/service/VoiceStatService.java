package com.panorai.service;

import com.panorai.model.dto.AiScoreEnum;
import com.panorai.model.entity.VoiceStat;
import com.panorai.repository.UserLikeRepository;
import com.panorai.repository.UserRetweetRepository;
import com.panorai.repository.VoiceRepository;
import com.panorai.repository.VoiceStatRepo;
import com.panorai.model.vo.UpdateAiScoreRequest;
import com.panorai.model.vo.UpdateVoiceStatRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class VoiceStatService {
    private final VoiceStatRepo voiceStatRepo;
    private final UserLikeRepository userLikeRepository;
    private final UserRetweetRepository userRetweetRepository;
    private final VoiceRepository voiceRepository;

    public void updateAiScore(@Valid UpdateAiScoreRequest request) {
        double aiScore = checkAiScore(request.aiScore());
        String voiceId = request.voiceId();
        Optional<VoiceStat> voiceStat = voiceStatRepo.getVoiceStat(voiceId);
        if (voiceStat.isEmpty()) {
            createVoiceStat(voiceId, aiScore);
            return;
        }
        voiceStatRepo.updateAiScore(voiceId, aiScore);
    }

    public Long updateVoiceStat(@Valid UpdateVoiceStatRequest request) {
        String type = request.type();
        String voiceId = request.voiceId();

        if (voiceStatRepo.getVoiceStat(voiceId).isEmpty()) {
            createVoiceStat(request.voiceId(), AiScoreEnum.NORMAL.getScore());
        }
        if (Objects.equals(type, "like")) {
            Long voiceLikeCount = userLikeRepository.getVoiceLikeCount(voiceId);
            voiceStatRepo.updateLikedCount(voiceId, voiceLikeCount.intValue());
            return voiceLikeCount;
        }
        if (Objects.equals(type, "forward")) {
            Long forwardCount = userRetweetRepository.getForwardCount(voiceId);
            voiceStatRepo.updateForwardedCount(voiceId, forwardCount.intValue());
            return forwardCount;
        }
        Long voiceReplyCount = voiceRepository.getVoiceReplyCount(voiceId);
        voiceStatRepo.updateRepliedCount(voiceId, voiceReplyCount.intValue());
        return voiceReplyCount;
    }

    private double checkAiScore(AiScoreEnum aiScore) {
        return Arrays.stream(AiScoreEnum.values())
                .filter((i) -> i == aiScore)
                .findFirst()
                .map(AiScoreEnum::getScore)
                .orElse(0.5);
    }

    private void createVoiceStat(String voiceId, double aiScore) {
        VoiceStat voiceStat = new VoiceStat();
        voiceStat.setVoiceId(voiceId);
        voiceStat.setAiScore(aiScore);
        voiceStat.setRepliedCount(0);
        voiceStat.setLikedCount(0);
        voiceStat.setForwardedCount(0);
        voiceStatRepo.save(voiceStat);
    }
}
