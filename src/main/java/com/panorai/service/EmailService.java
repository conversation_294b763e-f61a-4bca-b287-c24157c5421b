package com.panorai.service;

import com.panorai.model.enums.OTPType;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

/**
 * 邮件服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {
    private final TemplateEngine templateEngine;
    private final JavaMailSender mailSender;

    @Value("${spring.mail.from}")
    private String fromEmail;

    @Value("${app.otp.expiration-time:5}")
    private int expirationTime;

    /**
     * 发送OTP验证码邮件
     *
     * @param email 接收邮箱
     * @param otp   验证码
     * @param type  验证码类型
     */
    public void sendOTPEmail(String email, String otp, OTPType type) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            // 设置邮件信息
            helper.setFrom(fromEmail);
            helper.setTo(email);
            helper.setSubject(getSubject(type, otp));

            // 准备模板变量
            Context context = new Context();
            context.setVariable("otp", otp);
            context.setVariable("type", type.name());
            context.setVariable("expirationTime", expirationTime);

            // 生成HTML内容
            String htmlContent = templateEngine.process("OptEmail", context);
            helper.setText(getPlainTextContent(otp, type), htmlContent);

            // 发送邮件
            mailSender.send(message);
            log.info("发送验证码邮件成功: {}, 类型: {}", email, type);

        } catch (MessagingException e) {
            log.error("发送验证码邮件失败:", e);
            throw new RuntimeException("邮件发送失败", e);
        }
    }

    /**
     * 获取邮件主题
     */
    private String getSubject(OTPType type, String otp) {
        return String.format("Your Verification Code is [%s], Valid for %d Minutes", otp, expirationTime);
    }

    /**
     * 获取纯文本内容（备选方案）
     */
    private String getPlainTextContent(String otp, OTPType type) {
        String typeText = switch (type) {
            case SIGN_IN -> "登录 / Sign In";
            case EMAIL_VERIFICATION -> "邮箱验证 / Email Verification";
            case FORGET_PASSWORD -> "重置密码 / Reset Password";
        };

        return String.format("""
                您的验证码是: %s，有效期%d分钟。
                Your verification code is: %s, valid for %d minutes.
                
                用途: %s
                Purpose: %s
                
                如果您没有请求此验证码，请忽略此邮件。
                If you didn't request this code, please ignore this email.
                """, otp, expirationTime, otp, expirationTime, typeText, typeText);
    }

    public void sendMail(String to, String subject, String content) {
        SimpleMailMessage simpleMailMessage = new SimpleMailMessage();
        simpleMailMessage.setFrom(fromEmail);
        simpleMailMessage.setTo(to);
        simpleMailMessage.setSubject(subject);
        simpleMailMessage.setText(content);
        mailSender.send(simpleMailMessage);
    }

    @SneakyThrows
    public void sendHtmlMail(String to, String subject, String htmlContent) {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

        helper.setFrom(fromEmail);
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setText(htmlContent, true);
        mailSender.send(message);
    }
}
