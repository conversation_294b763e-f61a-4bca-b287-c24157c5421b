package com.panorai.service;

import com.panorai.model.dto.RecommendationRequest;
import com.panorai.model.entity.*;
import com.panorai.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class RecommendationService {
    private final VoiceRepository voiceRepository;
    private final VoiceScoringService voiceScoringService;
    private final VoiceViewHistoryRepository voiceViewHistoryRepository;
    private final UserLikeRepository userLikeRepository;
    private final VoiceStatRepo voiceStatRepo;
    private final VoiceService voiceService;
    private final BlockedUserService blockedUserService;
    private final RecommendationCacheRepository recommendationCacheRepository;
    private final UserProfileRepository userProfileRepository;
    private final UserFollowRepository userFollowRepository;

    public List<Voice> getRecommendations(String userId, RecommendationRequest request) {
        // 如果不是刷新请求，先尝试从Redis缓存获取
        if (Boolean.FALSE.equals(request.getRefresh())) {
            Optional<List<Voice>> cachedVoices = recommendationCacheRepository.getCachedUserRecommendations(userId);
            if (cachedVoices.isPresent() && !cachedVoices.get().isEmpty()) {
                return returnTopVoice(userId, request);
            }
        }

        Set<String> excludeIds = getExcludeVoiceIds(userId);

        Set<Voice> candidateVoices = recallVoices(userId, excludeIds);

        Map<String, Double> scoreMap = voiceScoringService.calculateScore(candidateVoices, userId);

        Map<String, Voice> voiceMap = candidateVoices.stream().collect(Collectors.toMap(Voice::getId, (i) -> i));
        List<Voice> voices = scoreMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .map((map) -> voiceMap.get(map.getKey()))
                .limit(100)
                .toList();

        // 存储到Redis缓存，有效期半小时，防止缓存雪崩加随机值
        recommendationCacheRepository.cacheUserRecommendations(userId, voices);

        return returnTopVoice(userId, request);
    }

    private List<Voice> returnTopVoice(String userId, RecommendationRequest request) {
        Optional<List<Voice>> cachedVoicesOpt = recommendationCacheRepository.getCachedUserRecommendations(userId);
        
        if (cachedVoicesOpt.isEmpty()) {
            log.warn("No cached recommendations found for user: {}", userId);
            return List.of();
        }
        
        List<Voice> voices = cachedVoicesOpt.get();
        log.info("returnTopVoice, userId: {}, voices size: {}", userId, voices.size());
        
        if (CollectionUtils.isEmpty(voices)) {
            return List.of();
        }

        int returnSize = Math.min(voices.size(), request.getSize());
        List<Voice> subList = new ArrayList<>(voices.subList(0, returnSize));

        List<Voice> remainingVoices = new ArrayList<>(voices);
        remainingVoices.removeAll(subList);

        // 更新Redis缓存，如果没有剩余则删除缓存
        if (remainingVoices.isEmpty()) {
            recommendationCacheRepository.clearUserRecommendations(userId);
        } else {
            recommendationCacheRepository.updateUserRecommendations(userId, remainingVoices);
        }

        log.info("returnTopVoice, userId: {}, sub voices size: {}", userId, subList.size());

        saveHistory(subList, userId);
        return new ArrayList<>(subList);
    }

    private void saveHistory(List<Voice> subList, String userId) {
        List<VoiceViewHistory> list = subList.stream().map((voice -> new VoiceViewHistory(userId, voice.getId()))).toList();
        voiceViewHistoryRepository.saveBatch(list);
    }

    /**
     * 排除互动过内容，排除3天内看过的内容
     */
    private Set<String> getExcludeVoiceIds(String userId) {
        List<VoiceViewHistory> recent3DayViewed = voiceViewHistoryRepository.getRecent3DayViewed(userId);
        Set<String> userInteractedVoiceIds = getUserInteractedVoiceIds(userId);

        Set<String> viewedVoiceIds = recent3DayViewed.stream()
                .map(VoiceViewHistory::getVoiceId)
                .collect(Collectors.toSet());
        viewedVoiceIds.addAll(userInteractedVoiceIds);

        return viewedVoiceIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());
    }

    private Set<String> getUserInteractedVoiceIds(String userId) {
        List<UserLike> userLikes = userLikeRepository.getUserLikes(userId);
        List<Voice> repliedVoices = voiceRepository.getRepliedVoiceOrForwardedByUserId(userId);

        Set<String> likeIds = userLikes.stream()
                .map(UserLike::getVoiceId)
                .collect(Collectors.toSet());
        Set<String> retweetIds = repliedVoices.stream()
                .map(Voice::getRetweetOfId)
                .collect(Collectors.toSet());
        Set<String> forwardedIds = repliedVoices.stream()
                .map(Voice::getRepliedToId)
                .collect(Collectors.toSet());

        likeIds.addAll(retweetIds);
        likeIds.addAll(forwardedIds);

        return likeIds;
    }

    /**
     * 召回算法 - 获取候选推文
     */
    private Set<Voice> recallVoices(String userId, Set<String> excludeIds) {

        Set<String> excludeUserIds = getExcludeUserIds(userId);

        List<Voice> recentVoices = voiceRepository.getRecentVoices(300, excludeIds, excludeUserIds);
        Set<Voice> candidateVoices = new HashSet<>(recentVoices);

        List<String> popularVoices = voiceStatRepo.getPopularVoices(200, excludeIds);
        if (!popularVoices.isEmpty()) {
            candidateVoices.addAll(voiceRepository.listPublicByIds(popularVoices, excludeUserIds));
        }

        List<Voice> followingVoices = voiceService.getFollowingVoices(userId, 100, excludeIds, excludeUserIds);
        if (!followingVoices.isEmpty()) {
            candidateVoices.addAll(followingVoices);
        }

        return candidateVoices;
    }

    private Set<String> getExcludeUserIds(String userId) {
        Set<String> invisibleUsers = new HashSet<>(blockedUserService.getInvisibleUsers(userId));

        // 获取当前用户关注的用户ID集合
        Set<String> followingIds = userFollowRepository.getFollowings(userId)
                .stream()
                .map(UserFollow::getFollowedId)
                .collect(Collectors.toSet());

        // 获取所有私有账号，但排除已关注的
        List<UserProfile> privateUsers = userProfileRepository.getPrivateUsers();
        Set<String> privateUserIds = privateUsers.stream()
                .map(UserProfile::getUserId)
                .filter(id -> !followingIds.contains(id)) // 直接过滤，避免先添加再删除
                .collect(Collectors.toSet());

        invisibleUsers.addAll(privateUserIds);
        invisibleUsers.add(userId); // 排除自己
        return invisibleUsers;
    }
}
