package com.panorai.service;

import com.panorai.repository.BlockedUserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class BlockedUserService {
    private final BlockedUserRepository blockedUserRepository;

    public Set<String> getInvisibleUsers(String userId) {
        return blockedUserRepository.getInviableUserIds(userId).stream()
                .flatMap(bu -> Stream.of(bu.getUserId(), bu.getBlockedUserId()))
                .filter(id -> !id.equals(userId))
                .collect(Collectors.toSet());
    }
}
