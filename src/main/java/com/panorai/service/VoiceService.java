package com.panorai.service;

import com.panorai.model.entity.UserFollow;
import com.panorai.model.entity.Voice;
import com.panorai.repository.UserFollowRepository;
import com.panorai.repository.VoiceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class VoiceService {
    private final VoiceRepository voiceRepository;
    private final UserFollowRepository userFollowRepository;

    public List<Voice> getFollowingVoices(String userId, Integer limit, Set<String> excludeIds, Collection<String> excludeUserIds) {
        // 获取用户关注的人
        List<UserFollow> followings = userFollowRepository.getFollowings(userId);
        if (followings.isEmpty()) {
            return List.of();
        }

        List<String> followingIds = followings.stream()
                .map(UserFollow::getFollowedId)
                .collect(Collectors.toList());

        return voiceRepository.listPublicByUserIds(followingIds, limit, excludeIds, excludeUserIds);
    }
}
