package com.panorai.service;

import com.panorai.model.entity.Voice;
import com.panorai.repository.VoiceRepository;
import com.panorai.model.vo.ReplyRequest;
import com.panorai.model.vo.ReplyResponse;
import com.panorai.model.vo.UpdateVoiceStatRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReplyService {
    private final VoiceRepository voiceRepository;
    private final VoiceStatService voiceStatService;

    @Transactional(rollbackFor = Exception.class)
    public ReplyResponse reply(ReplyRequest request) {
        String replyId = request.voiceId();
        Optional<Voice> voiceOpt = voiceRepository.getOptById(replyId);
        if (voiceOpt.isEmpty()) {
            log.error("reply voice not found, voiceId: {}", replyId);
            return null;
        }
        Voice voice = voiceOpt.get();
        Voice reply = voice.reply(request.text(), request.userId(), request.photoUrl());
        voiceRepository.createVoice(reply);

        if (!voice.getIsReply()) {
            voiceStatService.updateVoiceStat(new UpdateVoiceStatRequest(voice.getId(), "reply"));
        }
        return new ReplyResponse(reply, voice.getAuthorId().equals(request.userId()), voice.getAuthorId());
    }
}
