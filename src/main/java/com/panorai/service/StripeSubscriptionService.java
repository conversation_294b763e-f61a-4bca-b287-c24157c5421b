package com.panorai.service;

import com.panorai.config.StripeConfig;
import com.panorai.config.StripePriceConfig;
import com.panorai.model.entity.User;
import com.panorai.model.entity.Subscription;
import com.panorai.repository.UserRepository;
import com.panorai.repository.SubscriptionRepository;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.stripe.model.checkout.Session;
import com.stripe.param.CustomerCreateParams;
import com.stripe.param.SubscriptionCancelParams;
import com.stripe.param.SubscriptionUpdateParams;
import com.stripe.param.checkout.SessionCreateParams;
import com.stripe.param.checkout.SessionCreateParams.Mode;
import com.stripe.param.checkout.SessionCreateParams.LineItem;
import com.stripe.param.checkout.SessionCreateParams.SubscriptionData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class StripeSubscriptionService {
    
    private final UserRepository userRepository;
    private final SubscriptionRepository subscriptionRepository;
    private final StripeConfig stripeConfig;
    private final StripePriceConfig stripePriceConfig;
    
    /**
     * 创建Stripe客户
     */
    @Transactional
    public Customer createCustomer(User user) throws StripeException {
        CustomerCreateParams params = CustomerCreateParams.builder()
                .setEmail(user.getEmail())
                .setName(user.getName())
                .putMetadata("userId", user.getId())
                .build();
                
        return Customer.create(params);
    }
    
    /**
     * 创建订阅Checkout Session
     */
    public Session createCheckoutSession(String userId, String priceId) throws StripeException {
        User user = userRepository.getById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        // 检查用户是否已有活跃订阅
        Optional<Subscription> existingSubscription = subscriptionRepository.getActiveSubscriptionByUserId(user.getId());
        if (existingSubscription.isPresent()) {
            throw new IllegalStateException("用户已有活跃的订阅");
        }
        
        // 创建元数据
        Map<String, String> metadata = new HashMap<>();
        metadata.put("userId", user.getId());
        metadata.put("priceId", priceId);
        
        SessionCreateParams.Builder paramsBuilder = SessionCreateParams.builder()
                .setMode(Mode.SUBSCRIPTION)
                .setSuccessUrl(stripeConfig.getSuccessUrl() + "?session_id={CHECKOUT_SESSION_ID}")
                .setCancelUrl(stripeConfig.getCancelUrl())
                .addLineItem(
                    LineItem.builder()
                        .setPrice(priceId)
                        .setQuantity(1L)
                        .build()
                )
                .setSubscriptionData(
                    SubscriptionData.builder()
                        .putAllMetadata(metadata)
                        .build()
                )
                .putAllMetadata(metadata);
        
        // 如果用户已有Stripe客户ID，使用它
        Optional<Subscription> userSubscription = subscriptionRepository.getByUserId(user.getId());
        if (userSubscription.isPresent() && userSubscription.get().getStripeCustomerId() != null) {
            paramsBuilder.setCustomer(userSubscription.get().getStripeCustomerId());
        } else {
            // 否则让Stripe创建新客户
            paramsBuilder.setCustomerEmail(user.getEmail());
        }
                
        return Session.create(paramsBuilder.build());
    }
    
    /**
     * 取消订阅
     */
    @Transactional
    public com.stripe.model.Subscription cancelSubscription(String userId) throws StripeException {
        User user = userRepository.getById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        Optional<Subscription> subscriptionOpt = subscriptionRepository.getActiveSubscriptionByUserId(user.getId());
        if (subscriptionOpt.isEmpty()) {
            throw new IllegalStateException("用户没有活跃的订阅");
        }
        
        Subscription localSubscription = subscriptionOpt.get();
        com.stripe.model.Subscription stripeSubscription = 
            com.stripe.model.Subscription.retrieve(localSubscription.getStripeSubscriptionId());
        
        // 使用 SubscriptionUpdateParams 来设置在周期结束时取消
        SubscriptionUpdateParams params = SubscriptionUpdateParams.builder()
                .setCancelAtPeriodEnd(true)
                .build();
                
        com.stripe.model.Subscription canceledSubscription = stripeSubscription.update(params);
        
        // 更新本地状态
        localSubscription.setCancelAtPeriodEnd(true);
        localSubscription.setUpdatedAt(Instant.now());
        subscriptionRepository.updateById(localSubscription);
        
        return canceledSubscription;
    }
    
    /**
     * 立即取消订阅
     */
    @Transactional
    public com.stripe.model.Subscription cancelSubscriptionImmediately(String userId) throws StripeException {
        User user = userRepository.getById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        Optional<Subscription> subscriptionOpt = subscriptionRepository.getActiveSubscriptionByUserId(user.getId());
        if (subscriptionOpt.isEmpty()) {
            throw new IllegalStateException("用户没有活跃的订阅");
        }
        
        Subscription localSubscription = subscriptionOpt.get();
        com.stripe.model.Subscription stripeSubscription = 
            com.stripe.model.Subscription.retrieve(localSubscription.getStripeSubscriptionId());
        com.stripe.model.Subscription canceledSubscription = stripeSubscription.cancel();
        
        // 更新本地状态
        localSubscription.setStatus("canceled");
        localSubscription.setUpdatedAt(Instant.now());
        subscriptionRepository.updateById(localSubscription);
        
        return canceledSubscription;
    }
    
    /**
     * 恢复订阅（取消"周期结束时取消"）
     */
    @Transactional
    public com.stripe.model.Subscription resumeSubscription(String userId) throws StripeException {
        User user = userRepository.getById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        Optional<Subscription> subscriptionOpt = subscriptionRepository.getActiveSubscriptionByUserId(user.getId());
        if (subscriptionOpt.isEmpty()) {
            throw new IllegalStateException("用户没有订阅可以恢复");
        }
        
        Subscription localSubscription = subscriptionOpt.get();
        com.stripe.model.Subscription stripeSubscription = 
            com.stripe.model.Subscription.retrieve(localSubscription.getStripeSubscriptionId());
        
        // 只有当订阅设置为周期结束时取消才能恢复
        if (!stripeSubscription.getCancelAtPeriodEnd()) {
            throw new IllegalStateException("订阅不在取消状态");
        }
        
        SubscriptionUpdateParams params = SubscriptionUpdateParams.builder()
                .setCancelAtPeriodEnd(false)
                .build();
                
        com.stripe.model.Subscription resumedSubscription = stripeSubscription.update(params);
        
        // 更新本地状态
        localSubscription.setStatus("active");
        localSubscription.setCancelAtPeriodEnd(false);
        localSubscription.setUpdatedAt(Instant.now());
        subscriptionRepository.updateById(localSubscription);
        
        return resumedSubscription;
    }
    
    /**
     * 更改订阅计划
     */
    @Transactional
    public com.stripe.model.Subscription updateSubscriptionPlan(String userId, String newPriceId) throws StripeException {
        User user = userRepository.getById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        Optional<Subscription> subscriptionOpt = subscriptionRepository.getActiveSubscriptionByUserId(user.getId());
        if (subscriptionOpt.isEmpty()) {
            throw new IllegalStateException("用户没有活跃的订阅");
        }
        
        Subscription localSubscription = subscriptionOpt.get();
        com.stripe.model.Subscription stripeSubscription = 
            com.stripe.model.Subscription.retrieve(localSubscription.getStripeSubscriptionId());
        
        // 获取当前订阅项
        SubscriptionItem item = stripeSubscription.getItems().getData().get(0);
        
        // 更新订阅项的价格
        SubscriptionUpdateParams params = SubscriptionUpdateParams.builder()
                .addItem(
                    SubscriptionUpdateParams.Item.builder()
                        .setId(item.getId())
                        .setPrice(newPriceId)
                        .build()
                )
                .setProrationBehavior(SubscriptionUpdateParams.ProrationBehavior.CREATE_PRORATIONS)
                .build();
                
        com.stripe.model.Subscription updatedSubscription = stripeSubscription.update(params);
        
        // 更新本地价格ID
        localSubscription.setStripePriceId(newPriceId);
        localSubscription.setUpdatedAt(Instant.now());
        subscriptionRepository.updateById(localSubscription);
        
        return updatedSubscription;
    }
    
    /**
     * 创建Customer Portal会话
     */
    public com.stripe.model.billingportal.Session createPortalSession(String userId) throws StripeException {
        Optional<Subscription> subscriptionOpt = subscriptionRepository.getByUserId(userId);
        if (subscriptionOpt.isEmpty() || subscriptionOpt.get().getStripeCustomerId() == null) {
            throw new IllegalStateException("用户还没有Stripe客户ID");
        }
        
        com.stripe.param.billingportal.SessionCreateParams params = 
            com.stripe.param.billingportal.SessionCreateParams.builder()
                .setCustomer(subscriptionOpt.get().getStripeCustomerId())
                .setReturnUrl(stripeConfig.getSuccessUrl())
                .build();
                
        return com.stripe.model.billingportal.Session.create(params);
    }
    
    /**
     * 处理Customer创建事件
     */
    @Transactional
    public void handleCustomerCreated(Customer customer) {
        String userId = customer.getMetadata().get("userId");
        if (userId == null) {
            log.error("客户缺少userId元数据: {}", customer.getId());
            return;
        }
        
        User user = userRepository.getById(userId);
        if (user == null) {
            log.error("找不到用户: {}", userId);
            return;
        }
        
        // 创建或更新订阅记录，绑定客户ID
        Optional<Subscription> existingSubscription = subscriptionRepository.getByUserId(userId);
        Subscription subscription;
        
        if (existingSubscription.isPresent()) {
            subscription = existingSubscription.get();
            subscription.setStripeCustomerId(customer.getId());
            subscription.setUpdatedAt(Instant.now());
        } else {
            subscription = new Subscription();
            subscription.setUserId(userId);
            subscription.setStripeCustomerId(customer.getId());
            subscription.setStatus("incomplete");
        }
        
        subscriptionRepository.saveOrUpdate(subscription);
        log.info("绑定用户 {} 的Stripe客户ID: {}", userId, customer.getId());
    }
    
    /**
     * 处理订阅创建/更新事件
     */
    @Transactional
    public void handleSubscriptionUpdate(com.stripe.model.Subscription stripeSubscription) {
        String userId = stripeSubscription.getMetadata().get("userId");
        if (userId == null) {
            log.error("订阅缺少userId元数据: {}", stripeSubscription.getId());
            return;
        }
        
        User user = userRepository.getById(userId);
        if (user == null) {
            log.error("找不到用户: {}", userId);
            return;
        }
        
        // 获取或创建订阅记录
        Optional<Subscription> existingSubscription = subscriptionRepository.getByUserId(userId);
        Subscription subscription;
        
        if (existingSubscription.isPresent()) {
            subscription = existingSubscription.get();
        } else {
            subscription = new Subscription();
            subscription.setUserId(userId);
        }
        
        // 更新订阅信息
        subscription.setStripeCustomerId(stripeSubscription.getCustomer());
        subscription.setStripeSubscriptionId(stripeSubscription.getId());
        subscription.setStatus(stripeSubscription.getStatus());
        subscription.setCancelAtPeriodEnd(stripeSubscription.getCancelAtPeriodEnd());
        
        // 设置计费周期
        if (stripeSubscription.getCurrentPeriodStart() != null) {
            subscription.setPeriodStart(Instant.ofEpochSecond(stripeSubscription.getCurrentPeriodStart()));
        }
        if (stripeSubscription.getCurrentPeriodEnd() != null) {
            subscription.setPeriodEnd(Instant.ofEpochSecond(stripeSubscription.getCurrentPeriodEnd()));
        }
        
        // 设置试用期
        if (stripeSubscription.getTrialStart() != null) {
            subscription.setTrialStart(Instant.ofEpochSecond(stripeSubscription.getTrialStart()));
        }
        if (stripeSubscription.getTrialEnd() != null) {
            subscription.setTrialEnd(Instant.ofEpochSecond(stripeSubscription.getTrialEnd()));
        }
        
        // 获取价格ID和计划名称
        if (!stripeSubscription.getItems().getData().isEmpty()) {
            SubscriptionItem item = stripeSubscription.getItems().getData().get(0);
            subscription.setStripePriceId(item.getPrice().getId());
            // 这里可以根据priceId映射到具体的计划名称
            subscription.setPlan(mapPriceIdToPlan(item.getPrice().getId()));
        }
        
        subscription.setUpdatedAt(Instant.now());
        subscriptionRepository.saveOrUpdate(subscription);
        
        log.info("更新用户 {} 的订阅信息: {}", userId, stripeSubscription.getId());
    }
    
    /**
     * 处理订阅删除事件
     */
    @Transactional
    public void handleSubscriptionDeleted(com.stripe.model.Subscription stripeSubscription) {
        Optional<Subscription> subscriptionOpt = subscriptionRepository.getByStripeSubscriptionId(stripeSubscription.getId());
        if (subscriptionOpt.isEmpty()) {
            log.error("找不到本地订阅记录: {}", stripeSubscription.getId());
            return;
        }
        
        Subscription subscription = subscriptionOpt.get();
        subscription.setStatus("canceled");
        subscription.setUpdatedAt(Instant.now());
        subscriptionRepository.updateById(subscription);
        
        log.info("删除用户 {} 的订阅信息", subscription.getUserId());
    }
    
    /**
     * 根据价格ID映射到计划名称
     */
    private String mapPriceIdToPlan(String priceId) {
        String planName = stripePriceConfig.getPlanNameByPriceId(priceId);
        log.info("映射价格ID {} 到计划名称: {}", priceId, planName);
        return planName;
    }
    
    /**
     * 获取用户当前订阅
     */
    public Optional<Subscription> getCurrentSubscription(String userId) {
        return subscriptionRepository.getActiveSubscriptionByUserId(userId);
    }
    
    /**
     * 检查用户是否有有效订阅
     */
    public boolean hasValidSubscription(String userId) {
        return subscriptionRepository.hasValidSubscription(userId);
    }
    
    /**
     * 获取用户的所有订阅（包括已取消的）
     */
    public List<Subscription> getUserSubscriptions(String userId) {
        return subscriptionRepository.lambdaQuery()
                .eq(Subscription::getUserId, userId)
                .orderByDesc(Subscription::getCreatedAt)
                .list();
    }
    
    /**
     * 创建一次性支付Checkout Session
     */
    public Session createOneTimePaymentSession(String userId, String priceId) throws StripeException {
        User user = userRepository.getById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        Map<String, String> metadata = new HashMap<>();
        metadata.put("userId", user.getId());
        metadata.put("priceId", priceId);
        
        SessionCreateParams.Builder paramsBuilder = SessionCreateParams.builder()
                .setMode(Mode.PAYMENT)
                .setSuccessUrl(stripeConfig.getSuccessUrl() + "?session_id={CHECKOUT_SESSION_ID}")
                .setCancelUrl(stripeConfig.getCancelUrl())
                .addLineItem(
                    LineItem.builder()
                        .setPrice(priceId)
                        .setQuantity(1L)
                        .build()
                )
                .putAllMetadata(metadata);
        
        // 如果用户已有Stripe客户ID，使用它
        Optional<Subscription> userSubscription = subscriptionRepository.getByUserId(user.getId());
        if (userSubscription.isPresent() && userSubscription.get().getStripeCustomerId() != null) {
            paramsBuilder.setCustomer(userSubscription.get().getStripeCustomerId());
        } else {
            paramsBuilder.setCustomerEmail(user.getEmail());
        }
        
        return Session.create(paramsBuilder.build());
    }
    
    /**
     * 处理Checkout Session完成事件
     */
    @Transactional
    public void handleCheckoutSessionCompleted(Session session) {
        String userId = session.getMetadata().get("userId");
        if (userId == null) {
            log.error("Checkout session缺少userId元数据: {}", session.getId());
            return;
        }
        
        // 如果是订阅模式，Stripe会自动触发subscription.created事件
        // 如果是一次性支付，这里可以处理相关逻辑
        if (Mode.PAYMENT.getValue().equals(session.getMode())) {
            log.info("用户 {} 完成一次性支付: {}", userId, session.getAmountTotal());
            // 这里可以添加一次性支付的业务逻辑
        }
    }
}