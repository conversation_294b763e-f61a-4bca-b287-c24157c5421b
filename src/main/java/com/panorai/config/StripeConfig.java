package com.panorai.config;

import com.stripe.Stripe;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
public class StripeConfig {
    
    @Value("${stripe.api.key}")
    private String apiKey;
    
    @Value("${stripe.webhook.secret}")
    private String webhookSecret;
    
    @Value("${stripe.success.url:http://localhost:3000/subscription/success}")
    private String successUrl;
    
    @Value("${stripe.cancel.url:http://localhost:3000/subscription/cancel}")
    private String cancelUrl;
    
    @PostConstruct
    public void init() {
        Stripe.apiKey = apiKey;
    }
}