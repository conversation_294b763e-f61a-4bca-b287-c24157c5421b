package com.panorai.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "stripe.price")
@Data
public class StripePriceConfig {
    
    // 价格ID到计划名称的映射
    private Map<String, String> planMapping = new HashMap<>();
    
    // 计划详情
    private Map<String, PlanDetail> plans = new HashMap<>();
    
    @Data
    public static class PlanDetail {
        private String name;
        private String description;
        private Integer seats;
        private Boolean hasTrialPeriod;
        private Integer trialDays;
    }
    
    /**
     * 根据价格ID获取计划名称
     */
    public String getPlanNameByPriceId(String priceId) {
        return planMapping.getOrDefault(priceId, "basic");
    }
    
    /**
     * 获取计划详情
     */
    public PlanDetail getPlanDetail(String planName) {
        return plans.get(planName);
    }
}