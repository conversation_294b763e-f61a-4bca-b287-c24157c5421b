{"mappings": {"dynamic_date_formats": ["strict_date_optional_time", "yyyy/MM/dd HH:mm:ss Z||yyyy/MM/dd Z"], "dynamic_templates": [], "date_detection": true, "numeric_detection": false, "properties": {"abnormalBehaviorDetectStatus": {"type": "keyword"}, "abnormalBehaviorRuleTriggerId": {"type": "keyword"}, "apiId": {"type": "keyword"}, "appId": {"type": "keyword"}, "clientIp": {"type": "keyword"}, "clientIpInfo": {"properties": {"city": {"type": "keyword"}, "country": {"type": "keyword"}, "email": {"type": "keyword"}, "isp": {"type": "keyword"}, "location": {"type": "geo_point"}, "person": {"type": "keyword"}, "phone": {"type": "keyword"}, "province": {"type": "keyword"}}}, "clientPort": {"type": "integer"}, "crsDetectResult": {"type": "keyword"}, "crsDetectStatus": {"type": "keyword"}, "decryptedRequestBody": {"type": "text"}, "device": {"type": "keyword"}, "domain": {"type": "keyword"}, "httpHost": {"type": "keyword"}, "httpMethod": {"type": "keyword"}, "logTime": {"type": "date"}, "protocol": {"type": "keyword"}, "referer": {"type": "keyword"}, "rejectRiskRules": {"properties": {"content": {"type": "text"}, "crsRuleId": {"type": "keyword"}, "crsSeverity": {"type": "keyword"}, "crsShortRuleId": {"type": "keyword"}, "isDealt": {"type": "boolean"}, "score": {"type": "short", "ignore_malformed": false, "coerce": true}, "type": {"type": "keyword"}}}, "remark": {"type": "keyword"}, "requestBody": {"type": "text"}, "requestHeader": {"type": "text"}, "requestParam": {"type": "text"}, "requestResourceType": {"type": "keyword"}, "requestSize": {"type": "integer"}, "responseData": {"type": "text"}, "responseHeader": {"type": "text"}, "responseSize": {"type": "integer"}, "responseTime": {"type": "float"}, "riskRules": {"properties": {"content": {"type": "text"}, "crsRuleId": {"type": "keyword"}, "crsSeverity": {"type": "keyword"}, "crsShortRuleId": {"type": "keyword"}, "isDealt": {"type": "boolean"}, "score": {"type": "short", "ignore_malformed": false, "coerce": true}, "type": {"type": "keyword"}}}, "scheme": {"type": "keyword"}, "sensitiveRules": {"properties": {"content": {"type": "keyword"}, "httpField": {"type": "keyword"}, "ruleId": {"type": "keyword"}}}, "serverIp": {"type": "keyword"}, "serverPort": {"type": "integer"}, "statusCode": {"type": "keyword"}, "upstreamAddr": {"type": "keyword"}, "uri": {"type": "keyword"}, "url": {"type": "keyword"}, "userAgent": {"type": "text"}, "uuid": {"type": "keyword"}, "wafDetectId": {"type": "keyword"}, "wafDetectStatus": {"type": "keyword"}}}, "aliases": {}}