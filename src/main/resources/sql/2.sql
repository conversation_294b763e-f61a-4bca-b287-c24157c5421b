ALTER TABLE tweets
ADD COLUMN main_voice_id TEXT,
ADD COLUMN main_reply_id TEXT;


WITH RECURSIVE roots AS (
  SELECT id, id AS root_id, replied_to_id, is_reply
  FROM tweets
  WHERE is_reply = false
  UNION ALL
  SELECT t.id, r.root_id, t.replied_to_id, t.is_reply
  FROM tweets t
  JOIN roots r ON t.replied_to_id = r.id
  WHERE t.is_reply = true
)
UPDATE tweets AS t
SET main_voice_id = r.root_id
FROM roots r
WHERE t.id = r.id AND t.is_reply = true;

WITH first_replies AS (
  SELECT main_voice_id, MIN(created_at) AS min_created_at
  FROM tweets
  WHERE is_reply = true
  GROUP BY main_voice_id
),
main_reply_ids AS (
  SELECT t.main_voice_id, t.id AS main_reply_id
  FROM tweets t
  JOIN first_replies f
    ON t.main_voice_id = f.main_voice_id AND t.created_at = f.min_created_at
  WHERE t.is_reply = true
)
UPDATE tweets t
SET main_reply_id = m.main_reply_id
FROM main_reply_ids m
WHERE t.main_voice_id = m.main_voice_id
  AND t.id <> m.main_reply_id;