CREATE TABLE voice_stats (
    id bigserial PRIMARY KEY,
    voice_id TEXT NOT NULL,
    ai_score DOUBLE PRECISION,
    liked_count INT,
    forwarded_count INT,
    replied_count INT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE voice_stats IS 'Voice AI评分表，存储每条Voice的AI基础分';
COMMENT ON COLUMN voice_stats.id IS '主键ID';
COMMENT ON COLUMN voice_stats.voice_id IS 'Voice ID，关联tweets表';
COMMENT ON COLUMN voice_stats.ai_score IS 'AI基础分，范围0.0-1.0';
COMMENT ON COLUMN voice_stats.created_at IS '创建时间';
COMMENT ON COLUMN voice_stats.updated_at IS '更新时间';

CREATE INDEX idx_voice_stats_voice_id ON voice_stats(voice_id);
CREATE INDEX idx_voice_stats_ai_score ON voice_stats(ai_score);
CREATE INDEX idx_voice_stats_created_at ON voice_stats(created_at);


CREATE TABLE voice_view_history (
    id bigserial PRIMARY KEY,
    user_id TEXT NOT NULL,
    voice_id TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE voice_view_history IS '用户浏览历史表，记录用户与Voice的交互行为';
COMMENT ON COLUMN voice_view_history.id IS '主键ID';
COMMENT ON COLUMN voice_view_history.user_id IS '用户ID';
COMMENT ON COLUMN voice_view_history.voice_id IS 'Voice ID，关联tweets表';
COMMENT ON COLUMN voice_view_history.created_at IS '浏览时间';

CREATE INDEX idx_voice_view_history_user_id ON voice_view_history(user_id);
CREATE INDEX idx_voice_view_history_voice_id ON voice_view_history(voice_id);
CREATE INDEX idx_voice_view_history_created_at ON voice_view_history(created_at);
CREATE INDEX idx_voice_view_history_user_voice ON voice_view_history(user_id, voice_id);
CREATE INDEX idx_voice_view_history_user_created_at ON voice_view_history(user_id, created_at);



INSERT INTO voice_stats (voice_id, liked_count, forwarded_count, replied_count, ai_score)
SELECT
  v.id AS voice_id,
  COUNT(DISTINCT ul."B") AS liked_count,
  COUNT(DISTINCT ur."B") AS forwarded_count,
  COUNT(DISTINCT vv.id) AS replied_count,
  0.5 AS ai_score
FROM tweets v
LEFT JOIN "_userLikes" ul ON ul."A" = v.id
LEFT JOIN "_userRetweets" ur ON ur."A" = v.id
LEFT JOIN tweets vv ON vv.replied_to_id = v.id
GROUP BY v.id;