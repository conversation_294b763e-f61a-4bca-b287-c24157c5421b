<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.ApiTagRelationMapper">

    <insert id="saveOrUpdateRelations">
        insert into api_tag_relations(tag_id, api_id, create_user_id, update_time, is_deleted)
        values
        <foreach collection="rels" item="rel" separator=",">
            (#{rel.tagId}, #{rel.apiId}, #{rel.createUserId}, current_timestamp, false)
        </foreach>
        on conflict on constraint api_tag_relations_tag_id_api_id_key
            do update set update_time = current_timestamp, is_deleted = false
    </insert>
</mapper>
