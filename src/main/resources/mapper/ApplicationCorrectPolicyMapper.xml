<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.ApplicationCorrectPolicyMapper">

    <resultMap id="BaseResultMap" type="com.telecom.apigateway.model.entity.ApplicationCorrectPolicy">
        <id column="id" property="id"/>
        <result column="policy_id" property="policyId"/>
        <result column="policy_name" property="policyName"/>
        <result column="status" property="status"/>
        <result column="action" property="action"/>
        <!-- 明确指定为 VARCHAR 类型 -->
        <result column="conditions" property="conditions"
                typeHandler="com.telecom.apigateway.config.mybatisplus.PolicyConditionListTypeHandler"/>
        <result column="relate_app_id" property="relateAppId"/>
        <result column="ever_enabled" property="everEnabled"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="group_id" property="groupId"/>
    </resultMap>

    <resultMap id="PolicyResponseMap" type="com.telecom.apigateway.model.vo.response.ApplicationCorrectPolicyResponse">
        <result column="policy_id" property="policyId"/>
        <result column="policy_name" property="policyName"/>
        <result column="status" property="status"/>
        <result column="action" property="action"/>
        <result column="conditions" property="conditions"
                typeHandler="com.telecom.apigateway.config.mybatisplus.PolicyConditionListTypeHandler"/>
        <result column="relate_app_id" property="relateAppId"/>
        <result column="ever_enabled" property="everEnabled"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询策略列表 -->
    <select id="queryPage" resultMap="PolicyResponseMap">
        SELECT
        policy_id,
        policy_name,
        status,
        action,
        conditions,
        relate_app_id,
        ever_enabled,
        create_user,
        create_time,
        update_time,
        group_id
        FROM application_correct_policy
        WHERE is_deleted = false
        <if test="policyName != null and policyName != ''">
            AND policy_name ILIKE '%' || #{policyName} || '%'
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="action != null and action != ''">
            AND action = #{action}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper> 