<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.UserMapper">

    <resultMap id="UserResponseMap" type="com.telecom.apigateway.model.vo.response.QueryUserResponse">
        <result property="username" column="username"/>
        <result property="realName" column="real_name"/>
        <result property="status" column="status"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="department" column="department"/>
        <result property="updateTime" column="update_time"/>
        <result property="validTime" column="validTime"/>
        <result property="loginMode" column="login_mode"/>
        <result property="updateUser" column="update_user"/>
        <result property="roles" column="roles" typeHandler="com.telecom.apigateway.config.StringListTypeHandler"/>
    </resultMap>

    <!-- 查询用户列表 -->
    <select id="queryUsers" resultMap="UserResponseMap">
        select
        distinct u.username,
        ui.real_name,
        case
        when u.account_status = 'locked' then 'DISABLED'
        when ui.valid_end_time &lt;= NOW() then 'EXPIRED'
        when ui.valid_end_time &lt;= NOW() + interval '30 days' then 'EXPIRING'
        else 'ACTIVE'
        end as status,
        ui.email,
        u.phone,
        ui.department,
        u.update_time,
        ui.valid_end_time as validTime,
        'PASSWORD' as login_mode,
        (select real_name from user_info where username = ui.update_user) as update_user,
        (
        select
        STRING_AGG(distinct r.name, ',')
        from
        user_roles ur2
        left join roles r on ur2.role_id = r.role_id
        where
        ur2.username = u.username and ur2.is_deleted = false
        ) as roles
        from
        users u
        left join user_info ui on u.username = ui.username
        left join user_roles ur on u.username = ur.username
        <where>
            u.is_deleted = false and ui.is_deleted = false and ur.is_deleted =false
            <if test="request.type != null and request.type == 1">
                AND ui.valid_end_time &lt;= NOW() + INTERVAL '30 days'
                AND ui.valid_end_time > NOW()
            </if>
            <if test="request.type != null and request.type == 2">
                AND u.account_status = 'locked'
            </if>
            <if test="request.type != null and request.type == 3">
                AND ui.valid_end_time &lt;= NOW()
            </if>
            <if test="request.department != null and request.department != ''">
                AND ui.department = #{request.department}
            </if>
            <if test="request.roleIds != null and request.roleIds.size() > 0">
                AND ur.role_id IN
                <foreach collection="request.roleIds" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                u.username ILIKE '%' || #{request.keyword} || '%'
                OR ui.real_name ILIKE '%' || #{request.keyword} || '%'
                )
            </if>
        </where>
    </select>

    <select id="queryAllDepartments" resultType="string">
        SELECT DISTINCT department
        FROM user_info
        WHERE is_deleted = false
        AND department IS NOT NULL
        AND department != ''
        ORDER BY department
    </select>
</mapper>