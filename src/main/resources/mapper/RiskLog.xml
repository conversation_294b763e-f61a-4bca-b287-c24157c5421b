<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.RiskLogMapper">

    <select id="queryPage" resultType="com.telecom.apigateway.model.dto.QueryThreatDTO">
        select
            api.uri,
            api.id as apiId,
            app.name as belongApplication,
            rl.id as riskLogId,
            rl.crs_rule_id as ruleId,
            rl.is_dealt as dealt,
            rl.crs_severity,
            rl.client_ip as clientIp,
            rl.client_port as clientPort,
            concat(rl.client_country, '-', rl.client_city) as clientRegion,
            rl.target_addr as targetAddress,
            concat(rl.target_country, '-', rl.target_city) as targetRegion,
            rl.create_time as attackedTime,
            rl.score
        from risk_log rl
        left join api api on api.id = rl.api_id
        left join applications app on app.application_id = api.app_id
        <where>
            1=1
            <if test="query.riskLogId != null and query.riskLogId != ''">
                and rl.id = #{query.riskLogId}
            </if>
            <if test="query.startTime != null and query.endTime != null">
                and rl.create_time between #{query.startTime} and #{query.endTime}
            </if>
            <if test="query.uri != null and query.uri != ''">
                and api.uri ilike concat('%', #{query.uri}, '%')
            </if>
            <if test="query.belongApplication != null and query.belongApplication.size() > 0">
                and app.application_id in
                <foreach collection="query.belongApplication" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="query.dealt != null">
                and rl.is_dealt = #{query.dealt}
            </if>
            <if test="query.attackType != null and query.attackType.size() > 0">
                and rl.crs_short_rule_id in
                <foreach collection="query.attackType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="query.clientIp != null and query.clientIp != ''">
                and rl.client_ip ilike concat('%', #{query.clientIp}, '%')
            </if>
            <if test="query.clientPort != null and query.clientPort != ''">
                and rl.client_port = #{query.clientPort}
            </if>
            <if test="query.riskLevel != null and query.riskLevel.size() > 0">
                <foreach collection="query.riskLevel" item="level" open="and (" separator=" or " close=")">
                    <choose>
                        <when test="level == 0">
                            rl.score &lt; 1
                        </when>
                        <when test="level == 1">
                            rl.score &gt;= 3 and rl.score &lt; 5
                        </when>
                        <when test="level == 2">
                            rl.score &gt; 3 and rl.score &lt;= 5
                        </when>
                        <when test="level == 3">
                            rl.score &gt; 5
                        </when>
                    </choose>
                </foreach>
            </if>
        </where>
        order by rl.create_time desc
    </select>

    <select id="queryAll" resultType="com.telecom.apigateway.model.dto.QueryThreatDTO">
        select
        api.uri,
        api.id as apiId,
        app.name as belongApplication,
        rl.id as riskLogId,
        rl.crs_rule_id as ruleId,
        rl.is_dealt as dealt,
        rl.crs_severity,
        rl.client_ip as clientIp,
        rl.client_port as clientPort,
        concat(rl.client_country, '-', rl.client_city) as clientRegion,
        rl.target_addr as targetAddress,
        concat(rl.target_country, '-', rl.target_city) as targetRegion,
        rl.create_time as attackedTime,
        rl.score,
        rl.log_id as logId
        from risk_log rl
        left join api api on api.id = rl.api_id
        left join applications app on app.application_id = api.app_id
        left join rules on rules.rule_id = rl.crs_short_rule_id
        <where>
            1=1
            <if test="query.riskLogId != null and query.riskLogId != ''">
                and rl.id = #{query.riskLogId}
            </if>
            <if test="query.startTime != null and query.endTime != null">
                and rl.create_time between #{query.startTime} and #{query.endTime}
            </if>
            <if test="query.uri != null and query.uri != ''">
                and api.uri ilike concat('%', #{query.uri}, '%')
            </if>
            <if test="query.belongApplication != null and query.belongApplication.size() > 0">
                and app.application_id in
                <foreach collection="query.belongApplication" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="query.dealt != null">
                and rl.is_dealt = #{query.dealt}
            </if>
            <if test="query.attackType != null and query.attackType.size() > 0">
                and rules.type in
                <foreach collection="query.attackType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="query.clientIp != null and query.clientIp != ''">
                and rl.client_ip ilike concat('%', #{query.clientIp}, '%')
            </if>
            <if test="query.clientPort != null and query.clientPort != ''">
                and CAST(rl.client_port AS TEXT)  ilike concat('%', #{query.clientPort}, '%')
            </if>
            <if test="query.riskLevel != null and query.riskLevel.size() > 0">
                <foreach collection="query.riskLevel" item="level" open="and (" separator=" or " close=")">
                    <choose>
                        <when test="level == 0">
                            rl.score &lt; 1
                        </when>
                        <when test="level == 1">
                            rl.score &gt;= 1 and rl.score &lt; 3
                        </when>
                        <when test="level == 2">
                            rl.score &gt;= 3 and rl.score &lt; 5
                        </when>
                        <when test="level == 3">
                            rl.score &gt;= 5
                        </when>
                    </choose>
                </foreach>
            </if>
            <if test="query.api != null">
                <choose>
                    <when test="query.api == true">
                        and api.request_resource_type = 'api'
                    </when>
                    <otherwise>
                        and api.request_resource_type != 'api'
                    </otherwise>
                </choose>
            </if>
        </where>
        order by rl.create_time desc
    </select>

</mapper>
