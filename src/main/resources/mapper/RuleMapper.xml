<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.RuleMapper">
    <select id="queryCveRuleWithConfig" resultType="com.telecom.apigateway.model.vo.response.QueryCveRuleResponse">
        SELECT 
            r.rule_id as cveId,
            r.attack_type as cveName,
            rc.type as status,
            r.update_time as updateTime
        FROM rules r
        LEFT JOIN rule_config rc ON r.rule_id = rc.rule_id
        WHERE  r.category = #{category}
        <if test="cveName != null and cveName != ''">
            AND r.attack_type ILIKE '%' || #{cveName} || '%'
        </if>
        <if test="status != null and status != ''">
            AND rc.type = #{status}
        </if>
        ORDER BY r.update_time DESC
    </select>
</mapper> 