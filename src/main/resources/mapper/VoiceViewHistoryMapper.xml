<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panorai.mapper.VoiceViewHistoryMapper">

    <!-- 获取指定voice IDs的最后浏览时间 -->
    <select id="getLastViewedTime" resultType="com.panorai.model.dto.LastViewedTimeDTO">
        SELECT
            voice_id as voiceId,
            MAX(created_at) as lastViewedTime
        FROM voice_view_history
        WHERE user_id = #{userId} AND voice_id IN
        <foreach collection="voiceIds" item="voiceId" open="(" separator="," close=")">
            #{voiceId}
        </foreach>
        GROUP BY voice_id
    </select>

    <!-- 获取指定voice IDs的浏览统计信息（最后浏览时间 + 浏览次数） -->
    <select id="getViewStatistics" resultType="com.panorai.model.dto.LastViewedTimeDTO">
        SELECT
            voice_id as voiceId,
            MAX(created_at) as lastViewedTime,
            COUNT(*) as viewCount
        FROM voice_view_history
        WHERE user_id = #{userId} AND voice_id IN
        <foreach collection="voiceIds" item="voiceId" open="(" separator="," close=")">
            #{voiceId}
        </foreach>
        GROUP BY voice_id
    </select>

</mapper>