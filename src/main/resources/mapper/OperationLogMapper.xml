<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.OperationLogMapper">
    <resultMap id="BaseResultMap" type="com.telecom.apigateway.model.vo.response.OperationLogResponse">
        <result column="log_id" property="logId" jdbcType="VARCHAR"/>
        <result column="operation_type" property="operationType" jdbcType="VARCHAR"/>
        <result column="resource_type" property="resourceType" jdbcType="VARCHAR"/>
        <result column="format_description" property="description" jdbcType="VARCHAR"/>
        <result column="create_time" property="operationTime" jdbcType="TIMESTAMP"/>
        <result column="operation_user" property="username" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="login_ip" property="ip" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryPage" resultMap="BaseResultMap">
        SELECT
            ol.log_id,
            ol.operation_type,
            ol.resource_type,
            ol.format_description,
            ol.create_time,
            ol.operation_user,
            ui.real_name,
            ol.login_ip
        FROM
            operation_logs ol
        LEFT JOIN user_info ui ON ol.operation_user = ui.username AND ui.is_deleted = false
        <where>
            1=1
            <if test="username != null and username != ''">
                AND ol.operation_user ilike '%'|| #{username} || '%'
            </if>
            <if test="operationType != null and operationType != ''">
                AND ol.operation_type = #{operationType}
            </if>
            <if test="startTime != null and startTime != ''">
                AND ol.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND ol.create_time &lt;= #{endTime}
            </if>
            <if test="ip != null and ip != ''">
                AND ol.login_ip ilike '%' || #{ip} ||'%'
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>
