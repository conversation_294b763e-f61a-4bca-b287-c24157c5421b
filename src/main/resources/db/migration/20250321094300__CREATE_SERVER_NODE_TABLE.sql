-- 创建服务器节点信息表
CREATE TABLE IF NOT EXISTS server_node (
    id SERIAL PRIMARY KEY,
    host_id TEXT NOT NULL UNIQUE,
    host_name TEXT NOT NULL,
    remarks TEXT,
    cpu_total INTEGER,
    memory_total BIGINT,
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 添加表和字段注释
COMMENT ON TABLE server_node IS '服务器节点信息表';
COMMENT ON COLUMN server_node.host_id IS '主机ID';
COMMENT ON COLUMN server_node.host_name IS '主机名称';
COMMENT ON COLUMN server_node.remarks IS '备注';
COMMENT ON COLUMN server_node.cpu_total IS 'CPU总数';
COMMENT ON COLUMN server_node.memory_total IS '内存总量（单位：字节）';
COMMENT ON COLUMN server_node.create_time IS '创建时间';
COMMENT ON COLUMN server_node.update_time IS '更新时间';