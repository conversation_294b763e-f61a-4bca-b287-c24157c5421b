-- auto-generated definition
create table sensitive_log
(
    id                              uuid not null
        primary key,
    log_id                          text,
    log_time                        timestamp,
    client_ip                       text,
    http_method                     text,
    scheme                          text,
    uri                             text,
    url                             text,
    status_code                     text,
    user_agent                      text,
    request_header                  text,
    request_body                    text,
    decrypted_request_body          text,
    request_param                   text,
    response_header                 text,
    response_data                   text,
    api_id                          text,
    app_id                          text,
    client_country                  text,
    client_province                 text,
    client_city                     text,
    rule_id                         text,
    content                         text,
    http_field                      text,
    waf_detect_status               text,
    crs_detect_status               text,
    abnormal_behavior_detect_status text,
    create_time                     timestamp
);

comment on table sensitive_log is '日志记录表：记录请求、响应及威胁检测信息';

comment on column sensitive_log.id is '主键，使用 UUID 作为唯一标识';

comment on column sensitive_log.log_id is '业务日志 ID，用于关联原始日志';

comment on column sensitive_log.log_time is '日志记录时间（服务器时间）';

comment on column sensitive_log.client_ip is '请求来源客户端的 IP 地址';

comment on column sensitive_log.http_method is '请求方法';

comment on column sensitive_log.scheme is '请求协议（http 或 https）';

comment on column sensitive_log.uri is '请求路径 URI，不含协议和主机名';

comment on column sensitive_log.url is '完整请求 URL，包含协议、主机名和参数';

comment on column sensitive_log.status_code is '响应状态码，例如 200、403、500';

comment on column sensitive_log.user_agent is '请求头中的 User-Agent 信息';

comment on column sensitive_log.request_header is '请求头内容（JSON 或原始文本）';

comment on column sensitive_log.request_body is '原始请求体内容';

comment on column sensitive_log.decrypted_request_body is '解密后的请求体内容（如果启用了加密）';

comment on column sensitive_log.request_param is '请求参数字符串（如 URL 查询参数或表单参数）';

comment on column sensitive_log.response_header is '响应头内容（JSON 或原始文本）';

comment on column sensitive_log.response_data is '响应数据内容';

comment on column sensitive_log.api_id is '接口标识符，用于定位 API';

comment on column sensitive_log.app_id is '调用方应用标识符';

comment on column sensitive_log.client_country is '客户端地理位置：国家';

comment on column sensitive_log.client_province is '客户端地理位置：省份/州';

comment on column sensitive_log.client_city is '客户端地理位置：城市';

comment on column sensitive_log.rule_id is '涉敏规则id';

comment on column sensitive_log.content is '涉敏内容';

comment on column sensitive_log.http_field is '检测模块';

comment on column sensitive_log.create_time is '数据创建时间';

alter table sensitive_log
    owner to postgres;

