-- 创建Voice系统相关表

-- 推文表 (Voice主表)
CREATE TABLE IF NOT EXISTS tweets (
    id text NOT NULL,
    "text" text NOT NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    author_id text NOT NULL,
    photo_url text NULL,
    is_retweet bool DEFAULT false NOT NULL,
    retweet_of_id text NULL,
    is_reply bool DEFAULT false NOT NULL,
    replied_to_id text NULL,
    is_pinned bool DEFAULT false NOT NULL,
    is_hidden bool DEFAULT false NOT NULL,
    warning_text text NULL,
    visibility text DEFAULT 'public'::text NOT NULL,
    "type" text DEFAULT 'voice'::text NOT NULL,
    related_id text NULL,
    main_voice_id text NULL,
    main_reply_id text NULL,
    CONSTRAINT tweets_pkey PRIMARY KEY (id)
);

-- 创建tweets表索引
CREATE INDEX IF NOT EXISTS idx_tweets_main_reply_id ON tweets USING btree (main_reply_id);
CREATE INDEX IF NOT EXISTS idx_tweets_main_voice_id ON tweets USING btree (main_voice_id);
CREATE INDEX IF NOT EXISTS tweets_author_id_idx ON tweets USING btree (author_id);
CREATE INDEX IF NOT EXISTS tweets_created_at_index ON tweets USING btree (created_at DESC);
CREATE INDEX IF NOT EXISTS tweets_related_id_idx ON tweets USING btree (related_id);
CREATE INDEX IF NOT EXISTS tweets_replied_to_id_idx ON tweets USING btree (replied_to_id);
CREATE INDEX IF NOT EXISTS tweets_retweet_of_id_idx ON tweets USING btree (retweet_of_id);

-- Voice统计表
CREATE TABLE IF NOT EXISTS voice_stats (
    id bigserial NOT NULL,
    voice_id text NOT NULL,
    ai_score float8 NULL,
    liked_count int4 NULL,
    forwarded_count int4 NULL,
    replied_count int4 NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT voice_stats_pkey PRIMARY KEY (id)
);

-- voice_stats表索引
CREATE INDEX IF NOT EXISTS idx_voice_stats_ai_score ON voice_stats USING btree (ai_score);
CREATE INDEX IF NOT EXISTS idx_voice_stats_created_at ON voice_stats USING btree (created_at);
CREATE INDEX IF NOT EXISTS idx_voice_stats_voice_id ON voice_stats USING btree (voice_id);

-- 表注释
COMMENT ON TABLE voice_stats IS 'Voice AI评分表，存储每条Voice的AI基础分';
COMMENT ON COLUMN voice_stats.id IS '主键ID';
COMMENT ON COLUMN voice_stats.voice_id IS 'Voice ID，关联tweets表';
COMMENT ON COLUMN voice_stats.ai_score IS 'AI基础分，范围0.0-1.0';
COMMENT ON COLUMN voice_stats.created_at IS '创建时间';
COMMENT ON COLUMN voice_stats.updated_at IS '更新时间';

-- Voice浏览历史表
CREATE TABLE IF NOT EXISTS voice_view_history (
    id bigserial NOT NULL,
    user_id text NOT NULL,
    voice_id text NOT NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT voice_view_history_pkey PRIMARY KEY (id)
);

-- voice_view_history表索引
CREATE INDEX IF NOT EXISTS idx_voice_view_history_created_at ON voice_view_history USING btree (created_at);
CREATE INDEX IF NOT EXISTS idx_voice_view_history_user_created_at ON voice_view_history USING btree (user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_voice_view_history_user_id ON voice_view_history USING btree (user_id);
CREATE INDEX IF NOT EXISTS idx_voice_view_history_user_voice ON voice_view_history USING btree (user_id, voice_id);
CREATE INDEX IF NOT EXISTS idx_voice_view_history_voice_id ON voice_view_history USING btree (voice_id);

-- 表注释
COMMENT ON TABLE voice_view_history IS '用户浏览历史表，记录用户与Voice的交互行为';
COMMENT ON COLUMN voice_view_history.id IS '主键ID';
COMMENT ON COLUMN voice_view_history.user_id IS '用户ID';
COMMENT ON COLUMN voice_view_history.voice_id IS 'Voice ID，关联tweets表';
COMMENT ON COLUMN voice_view_history.created_at IS '浏览时间'; 