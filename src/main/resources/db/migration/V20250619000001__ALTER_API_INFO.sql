-- 为api表添加主api
ALTER TABLE api
ADD COLUMN main_application_id TEXT;

-- 更新现有记录，让 main_application_id 等于 application_id
UPDATE api
SET main_application_id = app_id
WHERE main_application_id IS NULL;

-- 设置字段为 NOT NULL
ALTER TABLE api
ALTER COLUMN main_application_id SET NOT NULL;

-- 添加列注释
COMMENT ON COLUMN api.main_application_id IS '合并到的主应用ID';

-- 创建索引
CREATE INDEX idx_api_main_application_id ON api(main_application_id);