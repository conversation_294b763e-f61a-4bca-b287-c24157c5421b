-- 为applications表添加来源和修正策略字段
ALTER TABLE applications 
ADD COLUMN source TEXT NOT NULL DEFAULT 'AUTO_IDENTIFY';

ALTER TABLE applications 
ADD COLUMN correct_policy_id TEXT;

ALTER TABLE applications 
ADD COLUMN excluded_from_assets BOOLEAN NOT NULL DEFAULT FALSE;

-- 先添加 main_application_id 字段为可空字段
ALTER TABLE applications 
ADD COLUMN main_application_id TEXT;

-- 更新现有记录，让 main_application_id 等于 application_id
UPDATE applications 
SET main_application_id = application_id 
WHERE main_application_id IS NULL;

-- 设置字段为 NOT NULL
ALTER TABLE applications 
ALTER COLUMN main_application_id SET NOT NULL;

ALTER TABLE applications 
ADD COLUMN is_main_application BOOLEAN NOT NULL DEFAULT FALSE;

-- 添加列注释
COMMENT ON COLUMN applications.source IS '应用来源：MANUAL_ADD-手动新增，AUTO_IDENTIFY-自动识别，APP_MERGE-应用合并';
COMMENT ON COLUMN applications.correct_policy_id IS '应用修正策略ID';
COMMENT ON COLUMN applications.excluded_from_assets IS '是否排除在资产统计外';
COMMENT ON COLUMN applications.main_application_id IS '合并到的主应用ID';
COMMENT ON COLUMN applications.is_main_application IS '是否为主应用（合并组的代表应用）';

-- 创建索引
CREATE INDEX idx_applications_source ON applications(source);
CREATE INDEX idx_applications_correct_policy_id ON applications(correct_policy_id);
CREATE INDEX idx_applications_excluded_from_assets ON applications(excluded_from_assets);
CREATE INDEX idx_applications_main_application_id ON applications(main_application_id);
CREATE INDEX idx_applications_is_main_application ON applications(is_main_application); 