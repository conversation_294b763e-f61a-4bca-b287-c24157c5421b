drop table if exists api_new;
create table api_new
(
    id                  varchar(100)                               not null primary key,
    merge_id            varchar(100),
    name                varchar(100)                               not null,
    app_id              text                                       not null,
    main_application_id text                                       not null,
    http_methods        text[]                                     not null,
    hosts               text[]                                     not null,
    port                integer                                    not null,
    uri                 text                                       not null,
    source              integer                                    not null,
    sensitive_level     integer      default 0                     not null,
    is_online           boolean      default false                 not null,
    is_encrypt          boolean      default false                 not null,
    is_active           boolean      default false                 not null,
    is_deleted          boolean      default false                 not null,
    online_time         timestamp,
    discover_time       timestamp,
    create_time         timestamp    default CURRENT_TIMESTAMP     not null,
    create_user         varchar(255)                               not null,
    update_time         timestamp    default CURRENT_TIMESTAMP     not null,
    update_user         varchar(255)                               not null,
    remark              varchar(255) default ''::character varying not null
);

comment on table api_new is 'api 表';

comment on column api_new.id is '主键';

comment on column api_new.merge_id is '合并api id';

comment on column api_new.name is '名称';

comment on column api_new.app_id is '应用id';

comment on column api_new.main_application_id is '合并到的主应用ID';

comment on column api_new.http_methods is 'http方法';

comment on column api_new.hosts is '域名或ip';

comment on column api_new.port is '端口';

comment on column api_new.uri is 'uri';

comment on column api_new.source is '来源, 0自动1手动';

comment on column api_new.is_encrypt is '标识是否为加密 API，需解密后使用';

comment on column api_new.is_active is '是否激活, 0否1是';

comment on column api_new.is_deleted is '是否删除 0否1是';

comment on column api_new.online_time is '上线时间';

comment on column api_new.discover_time is '发现时间';

comment on column api_new.create_time is '创建时间';

comment on column api_new.create_user is '创建用户';

comment on column api_new.update_time is '更新时间';

comment on column api_new.update_user is '更新用户';

comment on column api_new.remark is '备注';


update api
set update_time = create_time,
    update_user = create_user
where update_user is null;

INSERT INTO api_new(id,
                    name,
                    app_id,
                    main_application_id,
                    http_methods,
                    hosts,
                    port,
                    uri,
                    source,
                    sensitive_level,
                    is_online,
                    is_encrypt,
                    is_active,
                    is_deleted,
                    online_time,
                    discover_time,
                    create_time,
                    create_user,
                    update_time,
                    update_user,
                    remark)
SELECT id,
       name,
       app_id,
       app_id,
       array [http_method],
       array [host],
       port,
       uri,
       source,
       sensitive_level,
       is_online,
       is_encrypt,
       is_active,
       is_deleted,
       online_time,
       discover_time,
       create_time,
       create_user,
       update_time,
       update_user,
       remark
FROM api;

drop table if exists api;
CREATE TABLE api
    (
        LIKE api_new INCLUDING ALL
        );
INSERT INTO api
SELECT *
FROM api_new;
drop table if exists api_new;
