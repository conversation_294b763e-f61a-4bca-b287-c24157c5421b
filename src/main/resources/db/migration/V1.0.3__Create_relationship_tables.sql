-- 创建用户关系表

-- 用户关注关系表
CREATE TABLE IF NOT EXISTS "_userFollows" (
    "A" text NOT NULL,
    "B" text NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS "_userFollows_AB_unique" ON "_userFollows" USING btree ("A", "B");
CREATE INDEX IF NOT EXISTS "_userFollows_B_index" ON "_userFollows" USING btree ("B");

-- 用户点赞关系表
CREATE TABLE IF NOT EXISTS "_userLikes" (
    "A" text NOT NULL,
    "B" text NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS "_userLikes_AB_unique" ON "_userLikes" USING btree ("A", "B");
CREATE INDEX IF NOT EXISTS "_userLikes_B_index" ON "_userLikes" USING btree ("B");

-- 用户转发关系表
CREATE TABLE IF NOT EXISTS "_userRetweets" (
    "A" text NOT NULL,
    "B" text NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS "_userRetweets_AB_unique" ON "_userRetweets" USING btree ("A", "B");
CREATE INDEX IF NOT EXISTS "_userRetweets_B_index" ON "_userRetweets" USING btree ("B");

-- 用户屏蔽表
CREATE TABLE IF NOT EXISTS blocked_users (
    id bigserial NOT NULL,
    user_id text NOT NULL,
    blocked_user_id text NOT NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamptz NOT NULL,
    CONSTRAINT blocked_users_pkey PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS blocked_users_blocked_user_id_idx ON blocked_users USING btree (blocked_user_id);
CREATE INDEX IF NOT EXISTS blocked_users_user_id_idx ON blocked_users USING btree (user_id); 