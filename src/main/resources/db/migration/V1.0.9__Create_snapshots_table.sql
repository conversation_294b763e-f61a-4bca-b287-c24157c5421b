-- Flyway migration script for QueerEcho snapshots system
-- File: V1.0.9__Create_snapshots_table.sql

-- 检查快照表是否存在，如果不存在则创建
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'snapshots') THEN
        -- 创建快照表用于存储更改前和删除前的数据
        CREATE TABLE snapshots (
            id BIGSERIAL PRIMARY KEY,
            table_name TEXT NOT NULL, -- 原表名
            snapshot_data JSONB NOT NULL, -- 存储的原始数据
            operation_type TEXT NOT NULL CHECK (operation_type IN ('UPDATE', 'DELETE')), -- 'UPDATE', 'DELETE'
            user_id TEXT, -- 执行操作的用户ID（可为空，支持系统操作）
            ip_address INET, -- 操作来源IP
            user_agent TEXT, -- 用户代理
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
        );

        -- 创建索引优化查询性能，通过JSON路径获取原记录ID
        CREATE INDEX idx_snapshots_table_record_id ON snapshots(table_name, (snapshot_data->>'id'));
        
        -- 创建其他有用的索引
        CREATE INDEX idx_snapshots_table_name ON snapshots(table_name);
        CREATE INDEX idx_snapshots_operation_type ON snapshots(operation_type);
        CREATE INDEX idx_snapshots_user_id ON snapshots(user_id) WHERE user_id IS NOT NULL;
        CREATE INDEX idx_snapshots_created_at ON snapshots(created_at);
        
        -- 添加表和字段注释
        COMMENT ON TABLE snapshots IS '数据快照表，用于存储更改前和删除前的数据';
        COMMENT ON COLUMN snapshots.id IS '快照记录主键ID';
        COMMENT ON COLUMN snapshots.table_name IS '原表名称';
        COMMENT ON COLUMN snapshots.snapshot_data IS '快照数据，JSON格式存储原始记录';
        COMMENT ON COLUMN snapshots.operation_type IS '操作类型：UPDATE-更新前快照，DELETE-删除前快照';
        COMMENT ON COLUMN snapshots.user_id IS '执行操作的用户ID，系统操作时可为空';
        COMMENT ON COLUMN snapshots.ip_address IS '操作来源IP地址';
        COMMENT ON COLUMN snapshots.user_agent IS '用户代理信息';
        COMMENT ON COLUMN snapshots.created_at IS '快照创建时间';
        
        RAISE NOTICE '快照表 snapshots 创建成功';
    ELSE
        RAISE NOTICE '快照表 snapshots 已存在，跳过创建';
    END IF;
END $$; 