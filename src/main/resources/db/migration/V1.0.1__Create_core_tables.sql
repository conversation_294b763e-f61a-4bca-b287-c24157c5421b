-- 创建核心表：用户、账户、用户资料

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id text NOT NULL,
    "name" text NOT NULL,
    email text NOT NULL,
    email_verified bool NOT NULL,
    image text NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL,
    CONSTRAINT users_email_key UNIQUE (email),
    CONSTRAINT users_pkey PRIMARY KEY (id)
);

-- 账户表
CREATE TABLE IF NOT EXISTS accounts (
    id text NOT NULL,
    account_id text NOT NULL,
    provider_id text NOT NULL,
    user_id text NOT NULL,
    access_token text NULL,
    refresh_token text NULL,
    id_token text NULL,
    access_token_expires_at timestamp NULL,
    refresh_token_expires_at timestamp NULL,
    "scope" text NULL,
    "password" text NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL,
    CONSTRAINT accounts_pkey PRIMARY KEY (id)
);

-- 用户资料表
CREATE TABLE IF NOT EXISTS user_profiles (
    id text NOT NULL,
    user_id text NOT NULL,
    description varchar(160) NULL,
    "location" varchar(30) NULL,
    website varchar(300) NULL,
    photo_url text NULL,
    header_url text NULL,
    username varchar(20) NOT NULL,
    gender varchar(20) NOT NULL,
    pronoun text DEFAULT 'They'::text NOT NULL,
    is_premium bool DEFAULT false NOT NULL,
    created_at timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    visibility text DEFAULT 'public'::text NOT NULL,
    active_user bool DEFAULT false NOT NULL,
    CONSTRAINT user_profiles_pkey PRIMARY KEY (id)
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS user_profiles_user_id_key ON user_profiles USING btree (user_id);

-- 验证表
CREATE TABLE IF NOT EXISTS verifications (
    id text NOT NULL,
    identifier text NOT NULL,
    value text NOT NULL,
    expires_at timestamp NOT NULL,
    created_at timestamp NULL,
    updated_at timestamp NULL,
    CONSTRAINT verifications_pkey PRIMARY KEY (id)
); 