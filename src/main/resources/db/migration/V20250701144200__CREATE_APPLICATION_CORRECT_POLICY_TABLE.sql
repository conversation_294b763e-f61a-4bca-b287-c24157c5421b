DROP TABLE IF EXISTS application_correct_policy;

-- 创建应用修正策略表
CREATE TABLE IF NOT EXISTS application_correct_policy (
    id BIGSERIAL PRIMARY KEY,
    policy_id TEXT NOT NULL UNIQUE,
    policy_name TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'DISABLED',
    action TEXT NOT NULL,
    conditions JSONB,
    relate_app_id TEXT,
    ever_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    create_user TEXT NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    group_id TEXT
);

-- 添加表注释
COMMENT ON TABLE application_correct_policy IS '应用修正策略表';

-- 添加列注释
COMMENT ON COLUMN application_correct_policy.id IS '主键ID';
COMMENT ON COLUMN application_correct_policy.policy_id IS '策略ID';
COMMENT ON COLUMN application_correct_policy.policy_name IS '策略名称';
COMMENT ON COLUMN application_correct_policy.status IS '策略状态：ENABLED-启用，DISABLED-禁用';
COMMENT ON COLUMN application_correct_policy.action IS '策略动作：EXCLUDE_FROM_ASSETS-不计入应用资产，MERGE_TO_ONE_APP-合并为一个应用';
COMMENT ON COLUMN application_correct_policy.conditions IS '策略条件JSON';
COMMENT ON COLUMN application_correct_policy.relate_app_id IS '应用关联id';
COMMENT ON COLUMN application_correct_policy.ever_enabled IS '是否曾经启用过';
COMMENT ON COLUMN application_correct_policy.create_user IS '创建人';
COMMENT ON COLUMN application_correct_policy.create_time IS '创建时间';
COMMENT ON COLUMN application_correct_policy.update_time IS '更新时间';
COMMENT ON COLUMN application_correct_policy.is_deleted IS '是否删除';
COMMENT ON COLUMN application_correct_policy.group_id IS '所属分组ID';

-- 创建索引
CREATE INDEX idx_policy_name ON application_correct_policy(policy_name);
CREATE INDEX idx_status ON application_correct_policy(status);
CREATE INDEX idx_action ON application_correct_policy(action);
CREATE INDEX idx_create_time ON application_correct_policy(create_time);
CREATE INDEX idx_deleted ON application_correct_policy(is_deleted);
CREATE INDEX idx_group_id ON application_correct_policy(group_id);
