-- 创建独立的订阅表
CREATE TABLE IF NOT EXISTS subscriptions (
    id VARCHAR(255) PRIMARY KEY,
    plan VARCHAR(100),
    user_id VARCHAR(255) NOT NULL,
    stripe_customer_id VARCHAR(255),
    stripe_subscription_id VARCHAR(255),
    status VARCHAR(50),
    period_start TIMESTAMP,
    period_end TIMESTAMP,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    seats INTEGER DEFAULT 1,
    trial_start TIMESTAMP,
    trial_end TIMESTAMP,
    stripe_price_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- 添加索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer_id ON subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription_id ON subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);

-- 添加字段注释
COMMENT ON TABLE subscriptions IS '订阅管理表';
COMMENT ON COLUMN subscriptions.id IS '订阅记录ID';
COMMENT ON COLUMN subscriptions.plan IS '计划名称(basic/pro/enterprise)';
COMMENT ON COLUMN subscriptions.user_id IS '用户ID';
COMMENT ON COLUMN subscriptions.stripe_customer_id IS 'Stripe客户ID';
COMMENT ON COLUMN subscriptions.stripe_subscription_id IS 'Stripe订阅ID';
COMMENT ON COLUMN subscriptions.status IS '订阅状态(active/canceled/past_due等)';
COMMENT ON COLUMN subscriptions.period_start IS '计费周期开始时间';
COMMENT ON COLUMN subscriptions.period_end IS '计费周期结束时间';
COMMENT ON COLUMN subscriptions.cancel_at_period_end IS '是否在周期结束时取消';
COMMENT ON COLUMN subscriptions.seats IS '座位数(团队订阅)';
COMMENT ON COLUMN subscriptions.trial_start IS '试用期开始时间';
COMMENT ON COLUMN subscriptions.trial_end IS '试用期结束时间';
COMMENT ON COLUMN subscriptions.stripe_price_id IS 'Stripe价格计划ID';