CREATE TABLE abnormal_behavior_rule (
	id varchar(32) NOT NULL, -- 主键
	"name" varchar(100) NOT NULL, -- 规则名
	abnormal_type varchar(20) NOT NULL, -- 异常类型
	abnormal_operation varchar(20) DEFAULT 'gt'::character varying NOT NULL, -- 计算匹配方式
	abnormal_duration int4 NOT NULL, -- 持续时间(s)
	abnormal_threshold int4 NOT NULL, -- 异常次数
	asset_type varchar(20) NULL, -- 资产类型
	asset_id _varchar DEFAULT '{}'::character varying[] NULL, -- 资产id(app,api)
	"condition" jsonb NOT NULL, -- 规则条件
	"source" varchar(10) NOT NULL, -- 来源(SYSTEM,MANUAL)
	priority int4 NOT NULL, -- 优先级
	"policy" varchar(20) NOT NULL, -- 限制策略
	policy_duration int4 NOT NULL, -- 策略持续时间(s,-1无限)
	is_enable bool NOT NULL, -- 是否启用
	is_deleted bool NOT NULL, -- 是否删除
	create_time timestamp NOT NULL, -- 创建时间
	create_user varchar(32) NOT NULL, -- 创建用户
	update_time timestamp NULL, -- 更新时间
	update_user varchar(32) NULL, -- 更新用户
	CONSTRAINT abr_pk_id PRIMARY KEY (id)
);
COMMENT ON TABLE public.abnormal_behavior_rule IS '异常行为规则';

-- Column comments

COMMENT ON COLUMN public.abnormal_behavior_rule.id IS '主键';
COMMENT ON COLUMN public.abnormal_behavior_rule."name" IS '规则名';
COMMENT ON COLUMN public.abnormal_behavior_rule.abnormal_type IS '异常类型';
COMMENT ON COLUMN public.abnormal_behavior_rule.abnormal_operation IS '计算匹配方式';
COMMENT ON COLUMN public.abnormal_behavior_rule.abnormal_duration IS '持续时间(s)';
COMMENT ON COLUMN public.abnormal_behavior_rule.abnormal_threshold IS '异常次数';
COMMENT ON COLUMN public.abnormal_behavior_rule.asset_type IS '资产类型';
COMMENT ON COLUMN public.abnormal_behavior_rule.asset_id IS '资产id(app,api)';
COMMENT ON COLUMN public.abnormal_behavior_rule."condition" IS '规则条件';
COMMENT ON COLUMN public.abnormal_behavior_rule."source" IS '来源(SYSTEM,MANUAL)';
COMMENT ON COLUMN public.abnormal_behavior_rule.priority IS '优先级';
COMMENT ON COLUMN public.abnormal_behavior_rule."policy" IS '限制策略';
COMMENT ON COLUMN public.abnormal_behavior_rule.policy_duration IS '策略持续时间(s,-1无限)';
COMMENT ON COLUMN public.abnormal_behavior_rule.is_enable IS '是否启用';
COMMENT ON COLUMN public.abnormal_behavior_rule.is_deleted IS '是否删除';
COMMENT ON COLUMN public.abnormal_behavior_rule.create_time IS '创建时间';
COMMENT ON COLUMN public.abnormal_behavior_rule.create_user IS '创建用户';
COMMENT ON COLUMN public.abnormal_behavior_rule.update_time IS '更新时间';
COMMENT ON COLUMN public.abnormal_behavior_rule.update_user IS '更新用户';


-- public.abnormal_behavior_rule_trigger definition

-- Drop table

-- DROP TABLE abnormal_behavior_rule_trigger;

CREATE TABLE abnormal_behavior_rule_trigger (
	id varchar(32) NOT NULL, -- 主键
	rule_id varchar(32) NOT NULL, -- 异常行为规则id
	rule_name text NOT NULL, -- 规则名
	asset_id varchar(32) NOT NULL, -- 资产id
	asset_type varchar(20) NOT NULL, -- 资产类型
	client_ip varchar(100) NOT NULL, -- 触发的客户端ip
	client_port varchar(5) NOT NULL, -- 触发的客户端ip
	"condition" jsonb NOT NULL, -- 规则条件
	abnormal_type varchar(20) NOT NULL, -- 异常类型
	abnormal_operation varchar(20) DEFAULT 'gt'::character varying NOT NULL, -- 计算匹配方式
	abnormal_duration int4 NOT NULL, -- 异常持续时间(s)
	abnormal_threshold int4 NOT NULL, --  异常阈值次数
	"policy" varchar(20) NOT NULL, -- 限制策略
	priority int4 NOT NULL, -- 优先级
	policy_duration int4 NOT NULL, -- 策略持续时间(s,-1无限)
	waf_condition text NOT NULL, -- waf规则条件
	is_valid bool NOT NULL, -- 是否生效
	create_time timestamp NOT NULL, -- 创建时间
	invalid_time timestamp NOT NULL, -- 失效时间
	update_time timestamp NULL, -- 更新时间
	update_user varchar(100) NULL, -- 更新用户
	remark text DEFAULT ''::text NOT NULL, -- 备注
	CONSTRAINT abrt_pk_id PRIMARY KEY (id)
);
COMMENT ON TABLE public.abnormal_behavior_rule_trigger IS '异常行为触发';

-- Column comments

COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.id IS '主键';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.rule_id IS '异常行为规则id';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.rule_name IS '规则名';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.asset_id IS '资产id';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.asset_type IS '资产类型';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.client_ip IS '触发的客户端ip';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.client_port IS '触发的客户端ip';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger."condition" IS '规则条件';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.abnormal_type IS '异常类型';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.abnormal_operation IS '计算匹配方式';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.abnormal_duration IS '异常持续时间(s)';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.abnormal_threshold IS ' 异常阈值次数';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger."policy" IS '限制策略';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.priority IS '优先级';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.policy_duration IS '策略持续时间(s,-1无限)';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.waf_condition IS 'waf规则条件';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.is_valid IS '是否生效';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.create_time IS '创建时间';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.invalid_time IS '失效时间';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.update_time IS '更新时间';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.update_user IS '更新用户';
COMMENT ON COLUMN public.abnormal_behavior_rule_trigger.remark IS '备注';


-- public.api definition

-- Drop table

-- DROP TABLE api;

CREATE TABLE api (
	id varchar(100) NOT NULL, -- 主键
	"name" varchar(100) NOT NULL, -- 名称
	app_id text NOT NULL, -- 应用id
	http_method varchar(20) NULL, -- http方法
	risk_level int4 NULL, -- 风险等级,字典
	uri varchar(255) NOT NULL, -- uri
	path_level int4 NULL, -- 路径级别 0:根 1:一级 2:二级
	is_active bool DEFAULT false NULL, -- 是否激活, 0否1是
	is_online bool NULL,
	"source" int4 NULL, -- 来源, 0自动1手动
	discover_time timestamp NULL, -- 发现时间
	create_user varchar(255) NOT NULL, -- 创建用户
	update_user varchar(255) NULL, -- 更新用户
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 创建时间
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- 更新时间
	is_deleted bool DEFAULT false NULL, -- 是否删除 0否1是
	remark varchar(255) NULL, -- 备注
	online_time timestamp NULL, -- 上线时间
	host varchar(255) NULL, -- 域名或ip
	port int4 NULL, -- 端口
	sensitive_level int4 DEFAULT 0 NOT NULL, -- 涉敏等级
	CONSTRAINT api_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.api IS 'api 表';

-- Column comments

COMMENT ON COLUMN public.api.id IS '主键';
COMMENT ON COLUMN public.api."name" IS '名称';
COMMENT ON COLUMN public.api.app_id IS '应用id';
COMMENT ON COLUMN public.api.http_method IS 'http方法';
COMMENT ON COLUMN public.api.risk_level IS '风险等级,字典';
COMMENT ON COLUMN public.api.uri IS 'uri';
COMMENT ON COLUMN public.api.path_level IS '路径级别 0:根 1:一级 2:二级';
COMMENT ON COLUMN public.api.is_active IS '是否激活, 0否1是';
COMMENT ON COLUMN public.api."source" IS '来源, 0自动1手动';
COMMENT ON COLUMN public.api.discover_time IS '发现时间';
COMMENT ON COLUMN public.api.create_user IS '创建用户';
COMMENT ON COLUMN public.api.update_user IS '更新用户';
COMMENT ON COLUMN public.api.create_time IS '创建时间';
COMMENT ON COLUMN public.api.update_time IS '更新时间';
COMMENT ON COLUMN public.api.is_deleted IS '是否删除 0否1是';
COMMENT ON COLUMN public.api.remark IS '备注';
COMMENT ON COLUMN public.api.online_time IS '上线时间';
COMMENT ON COLUMN public.api.host IS '域名或ip';
COMMENT ON COLUMN public.api.port IS '端口';
COMMENT ON COLUMN public.api.sensitive_level IS '涉敏等级';


-- public.api_decrypt definition

-- Drop table

-- DROP TABLE api_decrypt;

CREATE TABLE api_decrypt (
	id serial4 NOT NULL,
	api_id text NOT NULL,
	decrypt_key text NOT NULL,
	create_time timestamp DEFAULT now() NOT NULL,
	decrypt_type text NOT NULL,
	CONSTRAINT api_decrypt_pk PRIMARY KEY (id)
);


-- public.api_tag_relations definition

-- Drop table

-- DROP TABLE api_tag_relations;

CREATE TABLE api_tag_relations (
	id serial4 NOT NULL,
	tag_id text NOT NULL,
	api_id text NOT NULL,
	create_user_id text NOT NULL,
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	is_deleted bool DEFAULT false NOT NULL,
	CONSTRAINT api_tag_relations_pkey PRIMARY KEY (id),
	CONSTRAINT api_tag_relations_tag_id_api_id_key UNIQUE (tag_id, api_id)
);


-- public.applications definition

-- Drop table

-- DROP TABLE applications;

CREATE TABLE applications (
	id serial4 NOT NULL, -- 主键ID
	application_id text NOT NULL, -- 业务ID
	create_user_id text NOT NULL, -- 创建应用的用户id
	"name" text NOT NULL, -- 名称
	host text NOT NULL, -- 域名
	port text NOT NULL, -- 端口
	uri text NOT NULL, -- uri
	remark text NULL, -- 备注
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 创建时间
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 更新时间
	is_deleted bool DEFAULT false NOT NULL, -- 是否删除，false：未删除，true：已删除
	protocol text DEFAULT 'HTTP'::text NOT NULL, -- 协议
	"owner" text NULL, -- 应用负责人
	phone text NULL, -- 电话
	email text NULL, -- 邮箱
	"type" text NOT NULL, -- 类型。应用/功能
	parent_id text NULL, -- 所属应用id
	area _text DEFAULT '{}'::text[] NOT NULL, -- 资产地区
	CONSTRAINT applications_application_id_key UNIQUE (application_id),
	CONSTRAINT applications_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_application_create_user_id ON public.applications USING btree (create_user_id);
CREATE INDEX idx_application_host ON public.applications USING btree (host);
CREATE INDEX idx_application_name ON public.applications USING btree (name);
COMMENT ON TABLE public.applications IS '应用资产表';

-- Column comments

COMMENT ON COLUMN public.applications.id IS '主键ID';
COMMENT ON COLUMN public.applications.application_id IS '业务ID';
COMMENT ON COLUMN public.applications.create_user_id IS '创建应用的用户id';
COMMENT ON COLUMN public.applications."name" IS '名称';
COMMENT ON COLUMN public.applications.host IS '域名';
COMMENT ON COLUMN public.applications.port IS '端口';
COMMENT ON COLUMN public.applications.uri IS 'uri';
COMMENT ON COLUMN public.applications.remark IS '备注';
COMMENT ON COLUMN public.applications.create_time IS '创建时间';
COMMENT ON COLUMN public.applications.update_time IS '更新时间';
COMMENT ON COLUMN public.applications.is_deleted IS '是否删除，false：未删除，true：已删除';
COMMENT ON COLUMN public.applications.protocol IS '协议';
COMMENT ON COLUMN public.applications."owner" IS '应用负责人';
COMMENT ON COLUMN public.applications.phone IS '电话';
COMMENT ON COLUMN public.applications.email IS '邮箱';
COMMENT ON COLUMN public.applications."type" IS '类型。应用/功能';
COMMENT ON COLUMN public.applications.parent_id IS '所属应用id';
COMMENT ON COLUMN public.applications.area IS '资产地区';


-- public.blocklists definition

-- Drop table

-- DROP TABLE blocklists;

CREATE TABLE blocklists (
	id serial4 NOT NULL, -- 主键
	block_id text NOT NULL, -- 业务id
	"name" text NOT NULL, -- 名称
	status text NOT NULL, -- 状态（例如：Active，Inactive）
	"type" text NOT NULL, -- 类型（例如：Black，White）
	"condition" text NOT NULL, -- 相关的条件
	create_time timestamp NOT NULL, -- 创建的时间戳
	update_time timestamp NOT NULL, -- 最后更新的时间戳
	is_deleted bool NOT NULL, -- 逻辑删除
	priority int4 DEFAULT 0 NULL, -- 优先级
	CONSTRAINT blocklists_block_id_key UNIQUE (block_id),
	CONSTRAINT blocklists_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blocklists IS '黑名单';

-- Column comments

COMMENT ON COLUMN public.blocklists.id IS '主键';
COMMENT ON COLUMN public.blocklists.block_id IS '业务id';
COMMENT ON COLUMN public.blocklists."name" IS '名称';
COMMENT ON COLUMN public.blocklists.status IS '状态（例如：Active，Inactive）';
COMMENT ON COLUMN public.blocklists."type" IS '类型（例如：Black，White）';
COMMENT ON COLUMN public.blocklists."condition" IS '相关的条件';
COMMENT ON COLUMN public.blocklists.create_time IS '创建的时间戳';
COMMENT ON COLUMN public.blocklists.update_time IS '最后更新的时间戳';
COMMENT ON COLUMN public.blocklists.is_deleted IS '逻辑删除';
COMMENT ON COLUMN public.blocklists.priority IS '优先级';


-- public.ip_group_relation definition

-- Drop table

-- DROP TABLE ip_group_relation;

CREATE TABLE ip_group_relation (
	id serial4 NOT NULL, -- 自增id
	ip_relation_id varchar NOT NULL, -- 业务id
	ip_group_id varchar NOT NULL,
	ip_address varchar NULL, -- ip地址
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	is_deleted bool DEFAULT false NOT NULL,
	CONSTRAINT ip_group_relation_pk PRIMARY KEY (id),
	CONSTRAINT ip_group_relation_unique_id UNIQUE (ip_relation_id)
);
CREATE INDEX idx_ip_group_relation_ip_group_id ON public.ip_group_relation USING btree (ip_group_id);

-- Column comments

COMMENT ON COLUMN public.ip_group_relation.id IS '自增id';
COMMENT ON COLUMN public.ip_group_relation.ip_relation_id IS '业务id';
COMMENT ON COLUMN public.ip_group_relation.ip_address IS 'ip地址';


-- public.ip_groups definition

-- Drop table

-- DROP TABLE ip_groups;

CREATE TABLE ip_groups (
	id serial4 NOT NULL, -- 自增id
	group_id varchar NOT NULL, -- 业务id
	"name" varchar NOT NULL, -- ip 组名称
	"type" varchar NOT NULL, -- 类型：系统/自定义
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	is_deleted bool DEFAULT false NOT NULL,
	CONSTRAINT ip_groups_pk PRIMARY KEY (id),
	CONSTRAINT ip_groups_unique_group_id UNIQUE (group_id)
);

-- Column comments

COMMENT ON COLUMN public.ip_groups.id IS '自增id';
COMMENT ON COLUMN public.ip_groups.group_id IS '业务id';
COMMENT ON COLUMN public.ip_groups."name" IS 'ip 组名称';
COMMENT ON COLUMN public.ip_groups."type" IS '类型：系统/自定义';


-- public.ip_info definition

-- Drop table

-- DROP TABLE ip_info;

CREATE TABLE ip_info (
	ip varchar(200) NOT NULL, -- ip
	person varchar(200) NULL, -- 姓名
	isp varchar(200) NULL, -- 运营商
	email varchar(200) NULL, -- 邮箱
	country varchar(20) NULL, -- 国家
	phone varchar(200) NULL, -- 号码
	province varchar(200) NULL, -- 省
	city varchar(200) NULL, -- 城市
	CONSTRAINT ip_info_pk PRIMARY KEY (ip)
);

-- Column comments

COMMENT ON COLUMN public.ip_info.ip IS 'ip';
COMMENT ON COLUMN public.ip_info.person IS '姓名';
COMMENT ON COLUMN public.ip_info.isp IS '运营商';
COMMENT ON COLUMN public.ip_info.email IS '邮箱';
COMMENT ON COLUMN public.ip_info.country IS '国家';
COMMENT ON COLUMN public.ip_info.phone IS '号码';
COMMENT ON COLUMN public.ip_info.province IS '省';
COMMENT ON COLUMN public.ip_info.city IS '城市';


-- public.llm_log definition

-- Drop table

-- DROP TABLE llm_log;

CREATE TABLE llm_log (
	id varchar(200) NOT NULL,
	client_ip varchar(255) NULL, -- 客户端ip
	log_id varchar(255) NULL, -- 日志id
	client_port varchar(255) NULL, -- 客户端端口
	create_time timestamptz(6) NULL,
	update_time timestamptz NULL,
	update_user varchar(255) NULL, -- 更新人
	use_user varchar(255) NULL, -- 调用该LLM的账号
	anaysis_request text NULL, -- 请求内容
	anaysis_response text NULL, -- 响应内容
	anaysis_type int4 NULL, -- 分析类型，1为威胁分析，2为敏感信息分析
	status int4 NULL, -- 状态，1为发起请求，2为正常响应，100为报错
	msg varchar(255) NULL, -- 描述内容，用于备注
	CONSTRAINT llm_log_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.llm_log.client_ip IS '客户端ip';
COMMENT ON COLUMN public.llm_log.log_id IS '日志id';
COMMENT ON COLUMN public.llm_log.client_port IS '客户端端口';
COMMENT ON COLUMN public.llm_log.update_user IS '更新人';
COMMENT ON COLUMN public.llm_log.use_user IS '调用该LLM的账号';
COMMENT ON COLUMN public.llm_log.anaysis_request IS '请求内容';
COMMENT ON COLUMN public.llm_log.anaysis_response IS '响应内容';
COMMENT ON COLUMN public.llm_log.anaysis_type IS '分析类型，1为威胁分析，2为敏感信息分析';
COMMENT ON COLUMN public.llm_log.status IS '状态，1为发起请求，2为正常响应，100为报错';
COMMENT ON COLUMN public.llm_log.msg IS '描述内容，用于备注';


-- public.menu_permissions definition

-- Drop table

-- DROP TABLE menu_permissions;

CREATE TABLE menu_permissions (
	id serial4 NOT NULL, -- 主键
	"name" varchar(100) NOT NULL, -- 菜单名称
	route_path varchar(200) NULL, -- 前端路由路径
	category varchar(100) NULL, -- 所属分类
	category_sort int4 DEFAULT 0 NULL, -- 分类排序
	menu_sort int4 DEFAULT 0 NULL, -- 菜单排序
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	is_deleted bool DEFAULT false NULL,
	code text NULL,
	route_name text NOT NULL, -- 路由名称
	parent_code text NULL, -- 父页面id
	category_name varchar NULL,
	CONSTRAINT menu_permissions_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_category ON public.menu_permissions USING btree (category);
CREATE INDEX idx_category_sort ON public.menu_permissions USING btree (category_sort);
COMMENT ON TABLE public.menu_permissions IS '菜单权限表';

-- Column comments

COMMENT ON COLUMN public.menu_permissions.id IS '主键';
COMMENT ON COLUMN public.menu_permissions."name" IS '菜单名称';
COMMENT ON COLUMN public.menu_permissions.route_path IS '前端路由路径';
COMMENT ON COLUMN public.menu_permissions.category IS '所属分类';
COMMENT ON COLUMN public.menu_permissions.category_sort IS '分类排序';
COMMENT ON COLUMN public.menu_permissions.menu_sort IS '菜单排序';
COMMENT ON COLUMN public.menu_permissions.route_name IS '路由名称';
COMMENT ON COLUMN public.menu_permissions.parent_code IS '父页面id';


-- public.regions definition

-- Drop table

-- DROP TABLE regions;

CREATE TABLE regions (
	id serial4 NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	longitude numeric(9, 6) NULL,
	latitude numeric(8, 6) NULL,
	CONSTRAINT regions_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_region_code ON public.regions USING btree (code);
CREATE INDEX idx_region_name ON public.regions USING btree (name);


-- public.risk_log definition

-- Drop table

-- DROP TABLE risk_log;

CREATE TABLE risk_log (
	id varchar(200) NOT NULL, -- uuid
	log_id varchar(50) NULL, -- 日志id
	api_id varchar(50) NULL, -- api id
	crs_tags text NULL, -- crs 风险标签
	crs_severity varchar(20) NULL, -- crs 风险等级
	crs_rule_id varchar(6) NULL, -- crs 规则id
	crs_short_rule_id varchar(3) NULL, -- crs 3位规则id
	crs_message text NULL, -- crs 风险信息
	client_ip varchar(50) NULL, -- 客户端ip
	client_port varchar(10) NULL, -- 客户端端口
	client_country varchar(50) NULL, -- 客户端国家
	client_city varchar(50) NULL, -- 客户端城市
	target_addr text NULL, -- 目标地址
	target_country varchar(50) NULL, -- 国家
	target_city varchar(50) NULL, -- 城市
	create_time timestamp NOT NULL, -- 创建时间
	update_user varchar(200) NULL, -- 更新用户
	update_time timestamp NULL, -- 更新时间
	is_dealt bool NULL, -- 是否处置
	is_deleted bool NULL, -- 是否删除
	score int4 NULL, -- 风险得分
	log jsonb NULL, -- nginx原始log
	CONSTRAINT risk_log_pk PRIMARY KEY (id)
);
CREATE INDEX risk_log_log_id_index ON public.risk_log USING btree (log_id);
COMMENT ON TABLE public.risk_log IS '风险日志信息';

-- Column comments

COMMENT ON COLUMN public.risk_log.id IS 'uuid';
COMMENT ON COLUMN public.risk_log.log_id IS '日志id';
COMMENT ON COLUMN public.risk_log.api_id IS 'api id';
COMMENT ON COLUMN public.risk_log.crs_tags IS 'crs 风险标签';
COMMENT ON COLUMN public.risk_log.crs_severity IS 'crs 风险等级';
COMMENT ON COLUMN public.risk_log.crs_rule_id IS 'crs 规则id';
COMMENT ON COLUMN public.risk_log.crs_short_rule_id IS 'crs 3位规则id';
COMMENT ON COLUMN public.risk_log.crs_message IS 'crs 风险信息';
COMMENT ON COLUMN public.risk_log.client_ip IS '客户端ip';
COMMENT ON COLUMN public.risk_log.client_port IS '客户端端口';
COMMENT ON COLUMN public.risk_log.client_country IS '客户端国家';
COMMENT ON COLUMN public.risk_log.client_city IS '客户端城市';
COMMENT ON COLUMN public.risk_log.target_addr IS '目标地址';
COMMENT ON COLUMN public.risk_log.target_country IS '国家';
COMMENT ON COLUMN public.risk_log.target_city IS '城市';
COMMENT ON COLUMN public.risk_log.create_time IS '创建时间';
COMMENT ON COLUMN public.risk_log.update_user IS '更新用户';
COMMENT ON COLUMN public.risk_log.update_time IS '更新时间';
COMMENT ON COLUMN public.risk_log.is_dealt IS '是否处置';
COMMENT ON COLUMN public.risk_log.is_deleted IS '是否删除';
COMMENT ON COLUMN public.risk_log.score IS '风险得分';
COMMENT ON COLUMN public.risk_log.log IS 'nginx原始log';

-- Constraint comments

COMMENT ON CONSTRAINT risk_log_pk ON public.risk_log IS '主键';


-- public.roles definition

-- Drop table

-- DROP TABLE roles;

CREATE TABLE roles (
	id serial4 NOT NULL, -- 主键
	role_id varchar(50) NOT NULL, -- 角色id
	"name" varchar(50) NOT NULL, -- 角色名称
	description varchar(200) NULL, -- 角色描述
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- 创建时间
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- 更新时间
	is_deleted bool DEFAULT false NULL, -- 是否删除
	menu_permissions _text NOT NULL, -- 菜单权限码
	update_user text NULL, -- 更新人
	application_permissions _text DEFAULT '{}'::text[] NOT NULL, -- 应用权限
	CONSTRAINT roles_pkey PRIMARY KEY (id),
	CONSTRAINT roles_role_id_key UNIQUE (role_id)
);
CREATE INDEX idx_role_id ON public.roles USING btree (role_id);
COMMENT ON TABLE public.roles IS '角色信息表';

-- Column comments

COMMENT ON COLUMN public.roles.id IS '主键';
COMMENT ON COLUMN public.roles.role_id IS '角色id';
COMMENT ON COLUMN public.roles."name" IS '角色名称';
COMMENT ON COLUMN public.roles.description IS '角色描述';
COMMENT ON COLUMN public.roles.create_time IS '创建时间';
COMMENT ON COLUMN public.roles.update_time IS '更新时间';
COMMENT ON COLUMN public.roles.is_deleted IS '是否删除';
COMMENT ON COLUMN public.roles.menu_permissions IS '菜单权限码';
COMMENT ON COLUMN public.roles.update_user IS '更新人';
COMMENT ON COLUMN public.roles.application_permissions IS '应用权限';

-- public.rule_config definition

-- Drop table

-- DROP TABLE rule_config;

CREATE TABLE rule_config (
	id serial4 NOT NULL, -- 自增id
	config_id varchar NOT NULL, -- 配置id
	rule_id varchar NULL, -- 规则id
	"type" varchar NULL, -- 规则类型
	category varchar NOT NULL, -- 规则来源，system-系统内置crs规则，custom-自定义规则
	filename varchar NULL, -- 内置crs规则文件名
	directives text NULL, -- 自定义规则内容
	CONSTRAINT rule_config_pk PRIMARY KEY (id),
	CONSTRAINT rule_config_unique UNIQUE (config_id)
);

-- Column comments

COMMENT ON COLUMN public.rule_config.id IS '自增id';
COMMENT ON COLUMN public.rule_config.config_id IS '配置id';
COMMENT ON COLUMN public.rule_config.rule_id IS '规则id';
COMMENT ON COLUMN public.rule_config."type" IS '规则类型';
COMMENT ON COLUMN public.rule_config.category IS '规则来源，system-系统内置crs规则，custom-自定义规则';
COMMENT ON COLUMN public.rule_config.filename IS '内置crs规则文件名';
COMMENT ON COLUMN public.rule_config.directives IS '自定义规则内容';


-- public.rules definition

-- Drop table

-- DROP TABLE rules;

CREATE TABLE rules (
	id serial4 NOT NULL, -- 自增id
	rule_id varchar NOT NULL, -- 业务id
	attack_type varchar NOT NULL, -- 威胁类型
	"module" varchar NULL, -- 防护模块
	score_keyword varchar NULL, -- 分数检索关键字
	"type" varchar NULL,
	category text NULL,
	is_deleted bool DEFAULT false NULL,
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT rules_pk PRIMARY KEY (id),
	CONSTRAINT rules_unique UNIQUE (rule_id)
);
CREATE INDEX rules_type_idx ON public.rules USING btree (type);

-- Column comments

COMMENT ON COLUMN public.rules.id IS '自增id';
COMMENT ON COLUMN public.rules.rule_id IS '业务id';
COMMENT ON COLUMN public.rules.attack_type IS '威胁类型';
COMMENT ON COLUMN public.rules."module" IS '防护模块';
COMMENT ON COLUMN public.rules.score_keyword IS '分数检索关键字';


-- public.sensitive_api definition

-- Drop table

-- DROP TABLE sensitive_api;

CREATE TABLE sensitive_api (
	id varchar(200) NOT NULL, -- uuid
	api_id varchar(200) NOT NULL, -- api id
	sensitive_rule_id varchar(200) NOT NULL, -- 涉敏规则id
	is_deleted bool NULL, -- 是否删除
	create_time timestamp NULL,
	update_time timestamp NULL,
	create_user varchar(200) NULL,
	update_user varchar(200) NULL,
	nonsensitive_count int4 DEFAULT 0 NULL, -- 未涉敏次数
	is_dealt bool NULL, -- 是否处置
	sensitive_count int4 DEFAULT 0 NULL, -- 涉敏次数
	last_sensitive_time timestamp NULL, -- 最新涉敏时间
	CONSTRAINT sensitive_api_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.sensitive_api IS '涉敏api';

-- Column comments

COMMENT ON COLUMN public.sensitive_api.id IS 'uuid';
COMMENT ON COLUMN public.sensitive_api.api_id IS 'api id';
COMMENT ON COLUMN public.sensitive_api.sensitive_rule_id IS '涉敏规则id';
COMMENT ON COLUMN public.sensitive_api.is_deleted IS '是否删除';
COMMENT ON COLUMN public.sensitive_api.nonsensitive_count IS '未涉敏次数';
COMMENT ON COLUMN public.sensitive_api.is_dealt IS '是否处置';
COMMENT ON COLUMN public.sensitive_api.sensitive_count IS '涉敏次数';
COMMENT ON COLUMN public.sensitive_api.last_sensitive_time IS '最新涉敏时间';

-- public.sensitive_rule definition

-- Drop table

-- DROP TABLE sensitive_rule;

CREATE TABLE sensitive_rule (
	id text NOT NULL, -- uuid
	app_id text NULL, -- 应用id
	"name" text NOT NULL, -- 规则名称
	"level" int4 NOT NULL, -- 涉敏等级
	detect_parts varchar(200) NULL, -- 检测部分(header/body等)
	match_case varchar(50) NULL, -- 匹配条件
	match_method varchar(50) NULL, -- 匹配方式
	keywords text NULL, -- 关键词或正则等,可多个
	is_enable bool DEFAULT true NOT NULL, -- 是否启用
	"source" text NOT NULL, -- 来源
	create_time timestamp NULL, -- 创建时间
	create_user text NULL, -- 创建人
	update_time timestamp NULL, -- 更新时间
	update_user text NULL, -- 更新人
	is_deleted bool DEFAULT false NULL, -- 是否删除
	code varchar(200) NULL, -- 规则代码
	CONSTRAINT pk_code UNIQUE (code),
	CONSTRAINT sensitive_rule_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.sensitive_rule IS '涉敏规则表';

-- Column comments

COMMENT ON COLUMN public.sensitive_rule.id IS 'uuid';
COMMENT ON COLUMN public.sensitive_rule.app_id IS '应用id';
COMMENT ON COLUMN public.sensitive_rule."name" IS '规则名称';
COMMENT ON COLUMN public.sensitive_rule."level" IS '涉敏等级';
COMMENT ON COLUMN public.sensitive_rule.detect_parts IS '检测部分(header/body等)';
COMMENT ON COLUMN public.sensitive_rule.match_case IS '匹配条件';
COMMENT ON COLUMN public.sensitive_rule.match_method IS '匹配方式';
COMMENT ON COLUMN public.sensitive_rule.keywords IS '关键词或正则等,可多个';
COMMENT ON COLUMN public.sensitive_rule.is_enable IS '是否启用';
COMMENT ON COLUMN public.sensitive_rule."source" IS '来源';
COMMENT ON COLUMN public.sensitive_rule.create_time IS '创建时间';
COMMENT ON COLUMN public.sensitive_rule.create_user IS '创建人';
COMMENT ON COLUMN public.sensitive_rule.update_time IS '更新时间';
COMMENT ON COLUMN public.sensitive_rule.update_user IS '更新人';
COMMENT ON COLUMN public.sensitive_rule.is_deleted IS '是否删除';
COMMENT ON COLUMN public.sensitive_rule.code IS '规则代码';


-- public.sensitive_stat_count definition

-- Drop table

-- DROP TABLE sensitive_stat_count;

CREATE TABLE sensitive_stat_count (
	id varchar(200) NOT NULL, -- 主键
	"time" varchar NULL, -- 时间字符串
	time_type varchar(5) NULL, -- 时间类型 DAY/MONTH/YEAR
	sensitive_api_count int4 NULL, -- 涉敏api总数
	new_sensitive_api_count int4 NULL, -- 新增涉敏api总数
	app_id varchar(200) NULL, -- 应用id
	stat_time timestamp NULL, -- 统计时间, 统计日的0点
	high_sensitive_count int4 NULL,
	mid_sensitive_count int4 NULL,
	low_sensitive_count int4 NULL,
	CONSTRAINT sensitive_stat_count_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.sensitive_stat_count IS '涉敏统计数量';

-- Column comments

COMMENT ON COLUMN public.sensitive_stat_count.id IS '主键';
COMMENT ON COLUMN public.sensitive_stat_count."time" IS '时间字符串';
COMMENT ON COLUMN public.sensitive_stat_count.time_type IS '时间类型 DAY/MONTH/YEAR';
COMMENT ON COLUMN public.sensitive_stat_count.sensitive_api_count IS '涉敏api总数';
COMMENT ON COLUMN public.sensitive_stat_count.new_sensitive_api_count IS '新增涉敏api总数';
COMMENT ON COLUMN public.sensitive_stat_count.app_id IS '应用id';
COMMENT ON COLUMN public.sensitive_stat_count.stat_time IS '统计时间, 统计日的0点';


-- public.sensitive_white_list definition

-- Drop table

-- DROP TABLE sensitive_white_list;

CREATE TABLE sensitive_white_list (
	id varchar(200) NULL, -- uuid
	sensitive_rule_id varchar(200) NULL, -- 涉敏规则id
	api_id varchar(200) NULL -- api 的id
);
COMMENT ON TABLE public.sensitive_white_list IS '涉敏白名单';

-- Column comments

COMMENT ON COLUMN public.sensitive_white_list.id IS 'uuid';
COMMENT ON COLUMN public.sensitive_white_list.sensitive_rule_id IS '涉敏规则id';
COMMENT ON COLUMN public.sensitive_white_list.api_id IS 'api 的id';


-- public.tags definition

-- Drop table

-- DROP TABLE tags;

CREATE TABLE tags (
	id serial4 NOT NULL, -- 主键ID
	tag_id text NOT NULL, -- 业务ID
	create_user_id text NOT NULL, -- 创建标签的用户id
	tag_name text NOT NULL, -- 标签名称
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 创建时间
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 更新时间
	is_deleted bool DEFAULT false NOT NULL, -- 是否删除，false：未删除，true：已删除
	CONSTRAINT tags_pkey PRIMARY KEY (id),
	CONSTRAINT tags_tag_id_key UNIQUE (tag_id)
);
CREATE INDEX idx_tag_create_user_id ON public.tags USING btree (create_user_id);
CREATE INDEX idx_tag_name ON public.tags USING btree (tag_name);
COMMENT ON TABLE public.tags IS '标签表';

-- Column comments

COMMENT ON COLUMN public.tags.id IS '主键ID';
COMMENT ON COLUMN public.tags.tag_id IS '业务ID';
COMMENT ON COLUMN public.tags.create_user_id IS '创建标签的用户id';
COMMENT ON COLUMN public.tags.tag_name IS '标签名称';
COMMENT ON COLUMN public.tags.create_time IS '创建时间';
COMMENT ON COLUMN public.tags.update_time IS '更新时间';
COMMENT ON COLUMN public.tags.is_deleted IS '是否删除，false：未删除，true：已删除';


-- public.user_info definition

-- Drop table

-- DROP TABLE user_info;

CREATE TABLE user_info (
	id serial4 NOT NULL, -- 表id
	username text NOT NULL, -- 用户名，唯一
	real_name text NULL, -- 真实姓名
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	is_deleted bool DEFAULT false NULL, -- 是否逻辑删除
	department text NULL,
	phone text NULL,
	email text NULL,
	valid_start_time timestamp NULL,
	valid_end_time timestamp NULL,
	update_user text NULL,
	CONSTRAINT user_info_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_user_info_username ON public.user_info USING btree (username);
COMMENT ON TABLE public.user_info IS '用户信息表';

-- Column comments

COMMENT ON COLUMN public.user_info.id IS '表id';
COMMENT ON COLUMN public.user_info.username IS '用户名，唯一';
COMMENT ON COLUMN public.user_info.real_name IS '真实姓名';
COMMENT ON COLUMN public.user_info.create_time IS '创建时间';
COMMENT ON COLUMN public.user_info.update_time IS '更新时间';
COMMENT ON COLUMN public.user_info.is_deleted IS '是否逻辑删除';

-- public.user_roles definition

-- Drop table

-- DROP TABLE user_roles;

CREATE TABLE user_roles (
	id serial4 NOT NULL,
	username text NOT NULL, -- 用户ID
	role_id text NOT NULL, -- 角色ID
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	is_deleted bool DEFAULT false NOT NULL,
	CONSTRAINT user_roles_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_user_role_username ON public.user_roles USING btree (username);
CREATE INDEX idx_user_roles_role_id ON public.user_roles USING btree (role_id);
CREATE INDEX idx_user_roles_username ON public.user_roles USING btree (username);
COMMENT ON TABLE public.user_roles IS '用户角色关联表';

-- Column comments

COMMENT ON COLUMN public.user_roles.username IS '用户ID';
COMMENT ON COLUMN public.user_roles.role_id IS '角色ID';


-- public.users definition

-- Drop table

-- DROP TABLE users;

CREATE TABLE users (
	id serial4 NOT NULL, -- 主键ID
	username varchar(64) NULL, -- 用户名，唯一
	"password" varchar(128) NOT NULL, -- 密码
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 创建时间
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 更新时间
	is_deleted bool DEFAULT false NOT NULL, -- 是否删除，false：未删除，true：已删除
	phone text NULL, -- 用于登录的手机号
	account_status text NULL, -- 账号状态
	change_password bool DEFAULT true NOT NULL, -- 是否需要修改密码
	CONSTRAINT users_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_users_username ON public.users USING btree (username);
COMMENT ON TABLE public.users IS '用户表';

-- Column comments

COMMENT ON COLUMN public.users.id IS '主键ID';
COMMENT ON COLUMN public.users.username IS '用户名，唯一';
COMMENT ON COLUMN public.users."password" IS '密码';
COMMENT ON COLUMN public.users.create_time IS '创建时间';
COMMENT ON COLUMN public.users.update_time IS '更新时间';
COMMENT ON COLUMN public.users.is_deleted IS '是否删除，false：未删除，true：已删除';
COMMENT ON COLUMN public.users.phone IS '用于登录的手机号';
COMMENT ON COLUMN public.users.account_status IS '账号状态';
COMMENT ON COLUMN public.users.change_password IS '是否需要修改密码';


-- public.system_config definition

-- Drop table

-- DROP TABLE system_config;

CREATE TABLE system_config (
	id serial4 NOT NULL, -- 自增id
	parent_id int4 NULL, -- 父id
	category varchar(100) NOT NULL, -- 分类
	"key" varchar(255) NOT NULL,
	value text NULL,
	description text NULL, -- 描述
	create_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	update_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT system_config_pkey PRIMARY KEY (id),
	CONSTRAINT unique_category_key UNIQUE (category, key),
	CONSTRAINT system_config_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES system_config(id) ON DELETE CASCADE
);
COMMENT ON TABLE public.system_config IS '系统配置表';

-- Column comments

COMMENT ON COLUMN public.system_config.id IS '自增id';
COMMENT ON COLUMN public.system_config.parent_id IS '父id';
COMMENT ON COLUMN public.system_config.category IS '分类';
COMMENT ON COLUMN public.system_config.description IS '描述';

INSERT INTO system_config ( parent_id, category, key, value, description, create_time, update_time) VALUES (null, 'SENSITIVE_RULE_REG', 'MOBILE_PHONE', '(?<!\d)(1[3-9]\d{9})(?!\d)', '手机号', now(), now());
INSERT INTO system_config ( parent_id, category, key, value, description, create_time, update_time) VALUES (null, 'SENSITIVE_RULE_REG', 'PHONE', '(?<![\d-])(0[0-9]{2,3}-[0-9]{7,8})(?![\d-])', '电话号码', now(), now());
INSERT INTO system_config ( parent_id, category, key, value, description, create_time, update_time) VALUES (null, 'SENSITIVE_RULE_REG', 'BANKCARD', '(?<!\d)([1-9]\d{15,18})(?!\d)', '银行卡号', now(), now());
INSERT INTO system_config ( parent_id, category, key, value, description, create_time, update_time) VALUES (null, 'SENSITIVE_RULE_REG', 'ID_CARD', '(?<!\d)(\d{17}[0-9Xx]|\d{15})(?!\d)', '身份证号', now(), now());
INSERT INTO system_config ( parent_id, category, key, value, description, create_time, update_time) VALUES (null, 'SENSITIVE_RULE_REG', 'LICENSE_PLATE', '([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z](([DF]((?![IO])[A-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[DF]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})', '车牌号', now(), now());
INSERT INTO system_config ( parent_id, category, key, value, description, create_time, update_time) VALUES (null, 'SENSITIVE_RULE_REG', 'ORG_CODE', '(?<![\d-])(\d{8}-[0-9A-Za-z])(?![\d-])', '组织机构代码', now(), now());
INSERT INTO system_config ( parent_id, category, key, value, description, create_time, update_time) VALUES (null, 'SENSITIVE_RULE_REG', 'BUSINESS_LICENSE', '''/(^(?:(?![IOZSV])[\dA-Z]){2}\d{6}(?:(?![IOZSV])[\dA-Z]){10}$)|(^\d{15}$)/'';', '营业执照号码', now(), now());
INSERT INTO system_config ( parent_id, category, key, value, description, create_time, update_time) VALUES (null, 'SENSITIVE_RULE_REG', 'CREDIT_CODE', '(?<![\dA-Za-z])([1-9]\d{14}[0-9A-Za-z])(?![\dA-Za-z])', '社会信用代码', now(), now());
INSERT INTO system_config ( parent_id, category, key, value, description, create_time, update_time) VALUES (null, 'SENSITIVE_RULE_REG', 'EMAIL', '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', '邮箱', now(), now());


INSERT INTO public.abnormal_behavior_rule (id,"name",abnormal_type,abnormal_operation,abnormal_duration,abnormal_threshold,asset_type,asset_id,"condition","source",priority,"policy",policy_duration,is_enable,is_deleted,create_time,create_user,update_time,update_user) VALUES
	 ('3','高频错误限制','ERROR','gt',100,5,'APPLICATION','{}','[]','SYSTEM',1,'BANNED',-1,false,false,now(),'admin',NULL,NULL),
	 ('1','高频访问限制','VISIT','gt',100,5,'APPLICATION','{}','[]','SYSTEM',1,'BLOCK',10000000,true,false,now(),'admin',NULL,NULL),
	 ('2','高频攻击限制','ATTACK','gt',100,2,'APPLICATION','{}','[]','SYSTEM',1,'BANNED',3600,true,false,now(),'admin',NULL,NULL),
	 ('4','高频涉敏限制','SENSITIVE','gt',100,2,'APPLICATION','{}','[]','SYSTEM',1,'BANNED',3600,true,false,now(),'admin',NULL,NULL);

INSERT INTO public.menu_permissions ("name",route_path,category,category_sort,menu_sort,code,route_name,parent_code,category_name) VALUES
	 ('日志记录','/logList','请求记录',2,1,'LOG_LIST','logList',NULL,'requestRecord'),
	 ('风险概览','/riskOverview','风险分析',3,1,'RISK_OVERVIEW','riskOverview',NULL,'riskAnalysis'),
	 ('威胁清单','/threatDetails','风险分析',3,2,'THREAT_LIST','threatDetails',NULL,'riskAnalysis'),
	 ('涉敏清单','/sensitiveDetails','风险分析',3,3,'SENSITIVE_LIST','sensitiveDetails',NULL,'riskAnalysis'),
	 ('态势大屏','/dataMonitoringScreen/situationScreen','数据监控大屏',-1,1,'SCREEN','situationScreen',NULL,'dataMonitoringScreen'),
	 ('API列表','/assetManage/apiList','资产中心',1,1,'API_LIST','apiList',NULL,'AssetCenter'),
	 ('应用列表','/applicationAssetManagement/appList','资产中心',1,2,'APP_LIST','appList',NULL,'AssetCenter'),
	 ('攻击者画像','/attackerportrait','风险分析',3,4,'ATTACKER_PORTRAIT','attackerportrait',NULL,'riskAnalysis'),
	 ('防护配置','/protectionConfig','安全防护',4,1,'PROTECTION_CONFIG','protectionConfig',NULL,'safetyProtection'),
	 ('涉敏规则','/sensitiveRules','安全防护',4,2,'SENSITIVE_CONFIG','sensitiveRules',NULL,'safetyProtection');
INSERT INTO public.menu_permissions ("name",route_path,category,category_sort,menu_sort,code,route_name,parent_code,category_name) VALUES
	 ('黑白名单设置','/bwListSetting','安全防护',4,3,'BLOCK_LIST','bwListSetting',NULL,'safetyProtection'),
	 ('用户管理','/userManage','系统设置',5,1,'USER_MANAGE','userManage',NULL,'systemSetting'),
	 ('权限管理','/accessControl','系统设置',5,2,'ACCESS_CONTROL','accessControl',NULL,'systemSetting'),
	 ('应用详情','/applicationAssetManagement/appList/appdetail','资产中心',1,-1,'APP_DETAIL','appdetail','APP_LIST','AssetCenter'),
	 ('API详情','/assetManage/apiList/apidetail','资产中心',1,-1,'API_DETAIL','apidetail','API_LIST','AssetCenter'),
	 ('基础分组详情页','/applicationAssetManagement/appList/unknownDetail','资产中心',1,-1,'UNKNOWN_DETAIL','unknownDetail','APP_LIST','AssetCenter'),
	 ('攻击者画像详情','/attackerportraitDetail','风险分析',3,-1,'ATTACKER_PORTRAIT_DETAIL','attackerportraitDetail','ATTACKER_PORTRAIT','riskAnalysis');


INSERT INTO public.regions (code,"name",longitude,latitude) VALUES
	 ('110000','北京市',116.405285,39.904989),
	 ('120000','天津市',117.190182,39.125596),
	 ('130000','河北省',114.502461,38.045474),
	 ('140000','山西省',112.549248,37.857014),
	 ('150000','内蒙古自治区',111.670801,40.818311),
	 ('210000','辽宁省',123.429096,41.796767),
	 ('220000','吉林省',125.324500,43.886841),
	 ('230000','黑龙江省',126.642464,45.756967),
	 ('310000','上海市',121.472644,31.231706),
	 ('320000','江苏省',118.767413,32.041544);
INSERT INTO public.regions (code,"name",longitude,latitude) VALUES
	 ('330000','浙江省',120.153576,30.287459),
	 ('340000','安徽省',117.283042,31.861190),
	 ('350000','福建省',119.306239,26.075302),
	 ('360000','江西省',115.892151,28.676493),
	 ('370000','山东省',117.000923,36.675807),
	 ('410000','河南省',113.665412,34.757975),
	 ('420000','湖北省',114.298572,30.584355),
	 ('430000','湖南省',112.982279,28.194090),
	 ('440000','广东省',113.280637,23.125178),
	 ('450000','广西壮族自治区',108.320004,22.824020);
INSERT INTO public.regions (code,"name",longitude,latitude) VALUES
	 ('460000','海南省',110.331190,20.031971),
	 ('500000','重庆市',106.504962,29.533155),
	 ('510000','四川省',104.065735,30.659462),
	 ('520000','贵州省',106.713478,26.578343),
	 ('530000','云南省',102.712251,25.040609),
	 ('540000','西藏自治区',91.132212,29.660361),
	 ('610000','陕西省',108.948024,34.263161),
	 ('620000','甘肃省',103.823557,36.058039),
	 ('630000','青海省',101.778916,36.623178),
	 ('640000','宁夏回族自治区',106.278179,38.466370);
INSERT INTO public.regions (code,"name",longitude,latitude) VALUES
	 ('650000','新疆维吾尔自治区',87.617733,43.792818),
	 ('710000','台湾省',121.509062,25.044332),
	 ('810000','香港特别行政区',114.173355,22.320048),
	 ('820000','澳门特别行政区',113.549090,22.198951);

INSERT INTO public.roles (role_id,"name",description,menu_permissions,update_user,application_permissions) VALUES
	 ('admin','超级管理员','超级管理员','{*}','admin','{*}');

INSERT INTO public.rule_config (config_id,rule_id,"type",category,filename) VALUES
	 ('8f6838a6d9ec446bbb2c3733bf876cc6','921','GUARD','system','REQUEST-921-PROTOCOL-ATTACK.conf'),
	 ('d4c1d79b0c6343628d914e638c5547e9','920','GUARD','system','REQUEST-920-PROTOCOL-ENFORCEMENT.conf'),
	 ('5d0d59ca88c5455da5cb9176ed8f529c','930','GUARD','system','REQUEST-930-APPLICATION-ATTACK-LFI.conf'),
	 ('c9d97a045b874cd8aaf7155ed50ff982','950','GUARD','system','RESPONSE-950-DATA-LEAKAGES.conf'),
	 ('1b71be42f4a242b38964651fca0ec459','952','GUARD','system','RESPONSE-952-DATA-LEAKAGES-JAVA.conf'),
	 ('f3db20adec6148068581a2117a813d48','941','GUARD','system','REQUEST-941-APPLICATION-ATTACK-XSS.conf'),
	 ('2879b126b26b4b44b29356a0db448da7','955','GUARD','system','RESPONSE-955-WEB-SHELLS.conf'),
	 ('89d109ded07b4cd0b0ddb7fc4e428641','913','GUARD','system','REQUEST-913-SCANNER-DETECTION.conf'),
	 ('d15a8ef7ddf44d41ad4735354e6f4916','954','GUARD','system','RESPONSE-954-DATA-LEAKAGES-IIS.conf'),
	 ('dc2ce36229b24459981aba5a70ec1d7a','943','GUARD','system','REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION.conf');
INSERT INTO public.rule_config (config_id,rule_id,"type",category,filename) VALUES
	 ('7e329230667d45e888d9a32652827910','922','GUARD','system','REQUEST-922-MULTIPART-ATTACK.conf'),
	 ('efa05af4891f438b8f323fa88cad97da','953','GUARD','system','RESPONSE-953-DATA-LEAKAGES-PHP.conf'),
	 ('37631ebc7bef4f67a3595df48923777d','933','GUARD','system','REQUEST-933-APPLICATION-ATTACK-PHP.conf'),
	 ('499ec211011c44cabc11a38591305045','931','GUARD','system','REQUEST-931-APPLICATION-ATTACK-RFI.conf'),
	 ('8362f0c25bbe4eb4ad482767ed59f9f9','932','GUARD','system','REQUEST-932-APPLICATION-ATTACK-RCE.conf'),
	 ('2779171f48d24bfe91e3440f8b9e424a','934','GUARD','system','REQUEST-934-APPLICATION-ATTACK-GENERIC.conf'),
	 ('efee8ff3920a4799b4152d7f8d598e23','944','GUARD','system','REQUEST-944-APPLICATION-ATTACK-JAVA.conf'),
	 ('42096c55e8e34447abd78562687bc98b','942','GUARD','system','REQUEST-942-APPLICATION-ATTACK-SQLI.conf'),
	 ('0f034957e7fe4587b55021012557b1cf','951','GUARD','system','RESPONSE-951-DATA-LEAKAGES-SQL.conf');


INSERT INTO public.rules (rule_id,attack_type,"module",score_keyword,"type",category) VALUES
	 ('930','文件和路径攻击','文件和路径攻击检测','lfi_score','001','system'),
	 ('931','远程文件包含攻击','远程文件包含攻击检测','rfi_score','002','system'),
	 ('932','远程代码执行攻击','远程代码执行攻击检测','rce_score','003','system'),
	 ('934','远程代码执行攻击','远程代码执行攻击检测','rce_score','003','system'),
	 ('944','远程代码执行攻击','远程代码执行攻击检测','rce_score','003','system'),
	 ('943','会话固定攻击','会话固定攻击检测','session_fixation_score','007','system'),
	 ('913','漏洞扫描行为','漏洞扫描行为检测','scanner_detection_score','008','system'),
	 ('955','WEBSHELL攻击','WEBSHELL攻击检测','web_shell_score','009','system'),
	 ('920','协议违规','协议违规检测','http_violation_score','010','system'),
	 ('921','协议违规','协议违规检测','http_violation_score','010','system');
INSERT INTO public.rules (rule_id,attack_type,"module",score_keyword,"type",category) VALUES
	 ('922','multipart攻击','multipart攻击检测','multipart_score','011','system'),
	 ('950','数据泄露','数据泄露检测','data_leakages_score','012','system'),
	 ('952','数据泄露','数据泄露检测','data_leakages_score','012','system'),
	 ('953','数据泄露','数据泄露检测','data_leakages_score','012','system'),
	 ('954','数据泄露','数据泄露检测','data_leakages_score','012','system'),
	 ('933','代码注入攻击','代码注入攻击检测','php_injection_score','004','system'),
	 ('941','跨站点脚本攻击(XSS)','跨站点脚本攻击检测','xss_score','005','system'),
	 ('942','SQL注入攻击','SQL注入检测','sql_injection_score','006','system'),
	 ('951','SQL注入攻击','SQL注入检测','sql_injection_score','006','system');


INSERT INTO public.user_info (username,real_name,department,phone,email,valid_start_time,valid_end_time,update_user) VALUES
	 ('admin','超级管理员',NULL,NULL,NULL,now(),'2038-12-31 23:59:59.999','admin');

INSERT INTO public.user_roles (username,role_id) VALUES
	 ('admin','admin');

INSERT INTO public.users (username,"password",create_time,update_time,is_deleted,phone,account_status,change_password) VALUES
     ('admin','$2a$10$g1k3nWNV0VClSh1P7esH7.kqn/y.OAHi4KUL707C.7UjLxeuZd0u.','now()','2038-12-31 23:59:59.999',false,'admin','active',false);

INSERT INTO public.sensitive_rule (id,app_id,name,"level",detect_parts,match_case,match_method,keywords,is_enable,"source",create_time,create_user,update_time,update_user,is_deleted,code) VALUES
	 ('bc3523d5f42a4e9ca6b65d31b8a9a68a',NULL,'邮箱',3,'REQUEST_BODY','KEYWORD','CONTAIN_ANY','[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',true,'SYSTEM',now(),'admin',now(),'admin',false,'EMAIL'),
	 ('f5d357e2a02c4e36b79528c5a020903d',NULL,'社会信用代码',3,'REQUEST_BODY','KEYWORD','CONTAIN_ANY','\b[0-9]{2}[0-9]{6}[0-9A-Z]{9}[0-9A-Z]\b',true,'SYSTEM',now(),'admin',now(),'admin',false,'SOCIAL_CREDIT_CODE'),
	 ('a772094f95de4109b4de14dda5fbbed1',NULL,'身份证号',3,'REQUEST_BODY','KEYWORD','CONTAIN_ANY','(?<!\d)[1-9]\d{5}(19\d{2}|20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx](?!\d)',true,'SYSTEM',now(),'admin',now(),'admin',false,'ID_CARD'),
	 ('8824305cffb3461598a92ad613a1827f',NULL,'银行账号',3,'REQUEST_BODY','KEYWORD','CONTAIN_ANY','(?<!\d)([1-9]\d{15,18})(?!\d)',true,'SYSTEM',now(),'admin',now(),'admin',false,'BANK_CARD'),
	 ('9ff778f22c8140efae6dc4f282c7388c',NULL,'车牌号',2,'REQUEST_BODY','KEYWORD','CONTAIN_ANY','([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}[A-Z](([DF]((?![IO])[A-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[DF]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})',true,'SYSTEM',now(),'admin',now(),'admin',false,'LICENSE_PLATE'),
	 ('bc3523d5f42a4e9ca6b65d31b8a9a681',NULL,'组织机构代码',1,'REQUEST_BODY','KEYWORD','CONTAIN_ANY','(?<![\d-])(\d{8}-[0-9A-Za-z])(?![\d-])',true,'SYSTEM',now(),'admin',now(),'admin',false,'ORG_CODE'),
	 ('ac4e165e76f64f60a6fe82c940420055',NULL,'电话号码',2,'REQUEST_BODY','KEYWORD','CONTAIN_ANY','(?<!\d)(1[3-9]\d{9})(?!\d)',true,'SYSTEM',now(),'admin',now(),'admin',false,'MOBILE_PHONE');




