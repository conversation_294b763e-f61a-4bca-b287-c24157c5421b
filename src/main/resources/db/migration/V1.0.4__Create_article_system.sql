-- 创建文章系统相关表

-- 文章表
CREATE TABLE IF NOT EXISTS articles (
    id bigserial NOT NULL,
    article_id text NOT NULL,
    title varchar(200) NOT NULL,
    "content" text NOT NULL,
    author_id text NOT NULL,
    cover_image text NULL,
    summary varchar(500) NULL,
    tags _text DEFAULT '{}'::text[] NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL,
    visibility text DEFAULT 'public'::text NOT NULL,
    is_hidden bool DEFAULT false NOT NULL,
    warning_text text NULL,
    deleted_at timestamp NULL,
    CONSTRAINT articles_article_id_unique UNIQUE (article_id),
    CONSTRAINT articles_pkey PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS articles_author_id_idx ON articles USING btree (author_id);
CREATE INDEX IF NOT EXISTS articles_created_at_idx ON articles USING btree (created_at);

-- 文章评论表
CREATE TABLE IF NOT EXISTS article_comments (
    id bigserial NOT NULL,
    comment_id text NOT NULL,
    "content" text NOT NULL,
    author_id text NOT NULL,
    article_id text NOT NULL,
    parent_id text NULL,
    root_comment_id text NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL,
    deleted_at timestamp NULL,
    CONSTRAINT article_comments_comment_id_unique UNIQUE (comment_id),
    CONSTRAINT article_comments_pkey PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS article_comments_article_id_idx ON article_comments USING btree (article_id);
CREATE INDEX IF NOT EXISTS article_comments_author_id_idx ON article_comments USING btree (author_id);
CREATE INDEX IF NOT EXISTS article_comments_parent_id_idx ON article_comments USING btree (parent_id);
CREATE INDEX IF NOT EXISTS article_comments_root_comment_id_idx ON article_comments USING btree (root_comment_id);

-- 文章点赞表
CREATE TABLE IF NOT EXISTS article_likes (
    id bigserial NOT NULL,
    article_id text NOT NULL,
    user_id text NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    deleted_at timestamp NULL,
    CONSTRAINT article_likes_pkey PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS article_likes_article_id_idx ON article_likes USING btree (article_id);
CREATE INDEX IF NOT EXISTS article_likes_user_id_idx ON article_likes USING btree (user_id);

-- 文章浏览日志表
CREATE TABLE IF NOT EXISTS article_logs (
    id bigserial NOT NULL,
    user_id text NULL,
    article_id text NOT NULL,
    ip_address inet NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT article_logs_pkey PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS idx_article_logs_article_id ON article_logs USING btree (article_id);
CREATE INDEX IF NOT EXISTS idx_article_logs_created_at ON article_logs USING btree (created_at);
CREATE INDEX IF NOT EXISTS idx_article_logs_ip_address ON article_logs USING btree (ip_address);
CREATE INDEX IF NOT EXISTS idx_article_logs_user_article ON article_logs USING btree (user_id, article_id);
CREATE INDEX IF NOT EXISTS idx_article_logs_user_created_at ON article_logs USING btree (user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_article_logs_user_id ON article_logs USING btree (user_id);

-- 表注释
COMMENT ON TABLE article_logs IS '文章浏览日志表，记录用户与文章的交互行为';
COMMENT ON COLUMN article_logs.id IS '主键ID';
COMMENT ON COLUMN article_logs.user_id IS '用户ID，可为空（匿名用户）';
COMMENT ON COLUMN article_logs.article_id IS '文章ID，关联articles表';
COMMENT ON COLUMN article_logs.ip_address IS '访问者IP地址';
COMMENT ON COLUMN article_logs.created_at IS '浏览时间'; 