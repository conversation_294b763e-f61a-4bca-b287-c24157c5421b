-- Create operation_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS operation_logs (
    id SERIAL PRIMARY KEY,
    log_id TEXT NOT NULL,
    operation_type TEXT NOT NULL,
    resource_type TEXT NULL,
    format_description TEXT NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    operation_user TEXT NOT NULL,
    login_ip TEXT NOT NULL
);

-- Add comments to the table and columns
COMMENT ON TABLE operation_logs IS '用户操作日志表';
COMMENT ON COLUMN operation_logs.id IS '主键ID';
COMMENT ON COLUMN operation_logs.log_id IS '日志ID';
COMMENT ON COLUMN operation_logs.operation_type IS '操作类型（登录、增加、删除、修改、查看）';
COMMENT ON COLUMN operation_logs.resource_type IS '资源类型（应用、资产、规则等）';
COMMENT ON COLUMN operation_logs.format_description IS '操作描述';
COMMENT ON COLUMN operation_logs.create_time IS '操作时间';
COMMENT ON COLUMN operation_logs.operation_user IS '操作用户名';
COMMENT ON COLUMN operation_logs.login_ip IS '操作IP地址';

-- Create index for faster queries
CREATE INDEX idx_operation_logs_create_time ON operation_logs(create_time DESC);
CREATE INDEX idx_operation_logs_operation_user ON operation_logs(operation_user);
CREATE INDEX idx_operation_logs_operation_type ON operation_logs(operation_type);

-- Add menu item for operation logs
INSERT INTO public.menu_permissions (
    name, 
    route_path, 
    category, 
    category_sort, 
    menu_sort, 
    code, 
    route_name, 
    parent_code, 
    category_name
) VALUES (
    '操作日志', 
    '/operationLogs', 
    '系统设置', 
    5, 
    2, 
    'OPERATION_LOGS', 
    'operationLogs', 
    NULL, 
    'systemSetting'
);
