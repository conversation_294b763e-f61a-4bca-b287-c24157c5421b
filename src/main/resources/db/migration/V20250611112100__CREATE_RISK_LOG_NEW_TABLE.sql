drop table if exists risk_log_new;
-- auto-generated definition
create table risk_log_new
(
    id                     uuid not null
        primary key,
    log_id                 text,
    log_time               timestamp,
    client_ip              text,
    scheme                 text,
    uri                    text,
    url                    text,
    status_code            text,
    user_agent             text,
    request_header         text,
    request_body           text,
    decrypted_request_body text,
    request_param          text,
    request_resource_type  text,
    response_header        text,
    response_data          text,
    api_id                 text,
    app_id                 text,
    client_country         text,
    client_province        text,
    client_city            text,
    crs_detect_status      text,
    crs_rule_id            text,
    score                  integer,
    crs_short_rule_id      text,
    crs_severity           text,
    rule_type              text,
    content                text,
    is_dealt               boolean,
    reason                 text,
    process_suggestion     text,
    create_time            timestamp,
    update_time            timestamp,
    update_user            text
);

comment on table risk_log_new is '日志记录表：记录请求、响应及威胁检测信息';

comment on column risk_log_new.id is '主键，使用 UUID 作为唯一标识';

comment on column risk_log_new.log_id is '业务日志 ID，用于关联原始日志';

comment on column risk_log_new.log_time is '日志记录时间（服务器时间）';

comment on column risk_log_new.client_ip is '请求来源客户端的 IP 地址';

comment on column risk_log_new.scheme is '请求协议（http 或 https）';

comment on column risk_log_new.uri is '请求路径 URI，不含协议和主机名';

comment on column risk_log_new.url is '完整请求 URL，包含协议、主机名和参数';

comment on column risk_log_new.status_code is '响应状态码，例如 200、403、500';

comment on column risk_log_new.user_agent is '请求头中的 User-Agent 信息';

comment on column risk_log_new.request_header is '请求头内容（JSON 或原始文本）';

comment on column risk_log_new.request_body is '原始请求体内容';

comment on column risk_log_new.decrypted_request_body is '解密后的请求体内容（如果启用了加密）';

comment on column risk_log_new.request_param is '请求参数字符串（如 URL 查询参数或表单参数）';

comment on column risk_log_new.request_resource_type is '请求资源类型（如 API、页面、静态资源）';

comment on column risk_log_new.response_header is '响应头内容（JSON 或原始文本）';

comment on column risk_log_new.response_data is '响应数据内容';

comment on column risk_log_new.api_id is '接口标识符，用于定位 API';

comment on column risk_log_new.app_id is '调用方应用标识符';

comment on column risk_log_new.client_country is '客户端地理位置：国家';

comment on column risk_log_new.client_province is '客户端地理位置：省份/州';

comment on column risk_log_new.client_city is '客户端地理位置：城市';

comment on column risk_log_new.crs_detect_status is 'WAF/CRS 检测结果（PASS/REJECT/ERROR）';

comment on column risk_log_new.crs_rule_id is '命中的完整 CRS 规则 ID';

comment on column risk_log_new.score is '威胁评分，用于评估风险等级';

comment on column risk_log_new.crs_short_rule_id is '简化规则 ID，便于展示';

comment on column risk_log_new.crs_severity is '威胁等级（如 high、medium、low）';

comment on column risk_log_new.rule_type is '规则类型(rules#type)';

comment on column risk_log_new.content is '威胁内容或异常描述';

comment on column risk_log_new.is_dealt is '是否已处置该威胁事件（true 表示已处置）';

comment on column risk_log_new.reason is '误报说明，人工分析后的备注';

comment on column risk_log_new.process_suggestion is '推荐的处理建议（如加入白名单、加强规则）';

comment on column risk_log_new.create_time is '数据创建时间';

comment on column risk_log_new.update_time is '数据最后更新时间';

comment on column risk_log_new.update_user is '更新人';

alter table risk_log_new owner to postgres;

