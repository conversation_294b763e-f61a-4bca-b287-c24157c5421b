-- 创建通信系统相关表

-- 消息表
CREATE TABLE IF NOT EXISTS messages (
    id text NOT NULL,
    "text" varchar(280) NOT NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    sender_id text NOT NULL,
    recipient_id text NOT NULL,
    photo_url text NULL,
    is_read bool DEFAULT false NOT NULL,
    CONSTRAINT messages_pkey PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS messages_recipient_id_idx ON messages USING btree (recipient_id);
CREATE INDEX IF NOT EXISTS messages_sender_id_idx ON messages USING btree (sender_id);

-- 通知表
CREATE TABLE IF NOT EXISTS notifications (
    id text NOT NULL,
    "type" text NOT NULL,
    "content" text NOT NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_read bool DEFAULT false NOT NULL,
    user_id text NOT NULL,
    CONSTRAINT notifications_pkey PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS notifications_user_id_idx ON notifications USING btree (user_id);

-- AI消息表
CREATE TABLE IF NOT EXISTS ai_messages (
    id bigserial NOT NULL,
    "content" jsonb NOT NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    user_id text NOT NULL,
    "role" text NOT NULL,
    CONSTRAINT ai_messages_pkey PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS ai_messages_created_at_idx ON ai_messages USING btree (created_at DESC); 