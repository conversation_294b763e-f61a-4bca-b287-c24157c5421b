-- Flyway migration script for QueerEcho content type and incentive system
-- File: V<version>__add_content_type_and_incentive_fields.sql

-- 为articles表添加内容类型和创作激励相关字段
ALTER TABLE articles
ADD COLUMN content_type TEXT NOT NULL DEFAULT 'original',
ADD COLUMN original_author TEXT NULL,
ADD COLUMN original_source TEXT NULL,
ADD COLUMN translation_permission TEXT NULL,
ADD COLUMN apply_for_incentive BOOLEAN NOT NULL DEFAULT false;

-- 为新字段创建索引以提高查询性能
CREATE INDEX idx_articles_content_type ON articles(content_type);
CREATE INDEX idx_articles_apply_for_incentive ON articles(apply_for_incentive);
CREATE INDEX idx_articles_content_type_incentive ON articles(content_type, apply_for_incentive);

-- 添加注释
COMMENT ON COLUMN articles.content_type IS '内容类型：original-首发原创，distribution-分发内容，translation-翻译内容';
COMMENT ON COLUMN articles.original_author IS '原作者姓名（仅翻译内容使用）';
COMMENT ON COLUMN articles.original_source IS '原文来源链接（仅翻译内容使用）';
COMMENT ON COLUMN articles.translation_permission IS '翻译授权详情（仅翻译内容使用）';
COMMENT ON COLUMN articles.apply_for_incentive IS '是否申请创作激励';