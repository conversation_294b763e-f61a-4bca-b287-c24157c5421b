create table api_merge
(
    id          text
        constraint api_merge_pk
            primary key,
    name        text,
    api_name    text,
    policy      text,
    condition   jsonb,
    url_reg     text,
    http_method text[],
    api         text[],
    is_enable   boolean,
    is_deleted  boolean,
    create_user text,
    create_time timestamp,
    update_time timestamp,
    update_user text,
    remark      text
);
