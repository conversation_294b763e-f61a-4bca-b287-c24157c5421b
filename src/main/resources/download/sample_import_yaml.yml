apis:
  - name: "自用接口"
    httpMethod: "GET"
    uri: "/api/v1/self"
    url: "https://www.example.com/api/v1/self"
    type: "自用"
    onlineTime: "2024-12-12 14:00:00"
  - name: "测试用接口 "
    httpMethod: "GET"
    uri: "/api/v1/test"
    url: "https://www.example.com/api/v1/test"
    type: "测试"
    onlineTime: "2024-12-12 14:00:00"
  - name: "用户登录接口 "
    httpMethod: "GET"
    uri: "/api/v1/login"
    url: "https://www.example.com/api/v1/login"
    type: "登录"
    onlineTime: "2024-12-12 14:00:00"
  - name: "访问信息接口"
    httpMethod: "GET"
    uri: "/api/v1/fangwen"
    url: "https://www.example.com/api/v1/fangwen"
    type: "访问"
    onlineTime: "2024-12-12 14:00:00"
