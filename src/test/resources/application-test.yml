spring:
  datasource:
    url: jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # 使用H2内存数据库进行测试
  h2:
    console:
      enabled: true
  
  # 测试环境不启用Flyway
  flyway:
    enabled: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# Sa-Token配置
sa-token:
  timeout: -1
  active-timeout: -1
  is-concurrent: true
  token-style: simple-uuid
  is-log: false
  token-name: Authorization
  token-prefix: Bearer

# Stripe测试配置
stripe:
  api:
    key: sk_test_fake_key_for_testing
  webhook:
    secret: whsec_test_fake_secret
  success:
    url: http://localhost:3000/test/success
  cancel:
    url: http://localhost:3000/test/cancel

# 邮件服务测试配置
spring:
  mail:
    host: localhost
    port: 1025
    username: test
    password: test
    from: <EMAIL>