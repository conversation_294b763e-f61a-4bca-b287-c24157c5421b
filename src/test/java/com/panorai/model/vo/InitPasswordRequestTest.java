package com.panorai.model.vo;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class InitPasswordRequestTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void validRequest_ShouldPassValidation() {
        // Given
        InitPasswordRequest request = new InitPasswordRequest("user123", "password123");

        // When
        Set<ConstraintViolation<InitPasswordRequest>> violations = validator.validate(request);

        // Then
        assertTrue(violations.isEmpty());
    }

    @Test
    void request_ShouldFailValidation_WhenUserIdIsNull() {
        // Given
        InitPasswordRequest request = new InitPasswordRequest(null, "password123");

        // When
        Set<ConstraintViolation<InitPasswordRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("不能为空") || v.getMessage().contains("must not be blank")));
    }

    @Test
    void request_ShouldFailValidation_WhenUserIdIsBlank() {
        // Given
        InitPasswordRequest request = new InitPasswordRequest("", "password123");

        // When
        Set<ConstraintViolation<InitPasswordRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("不能为空") || v.getMessage().contains("must not be blank")));
    }

    @Test
    void request_ShouldFailValidation_WhenPasswordTooShort() {
        // Given
        InitPasswordRequest request = new InitPasswordRequest("user123", "1234567"); // 7位

        // When
        Set<ConstraintViolation<InitPasswordRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("长度需要在8和20之间") || v.getMessage().contains("length must be between 8 and 20")));
    }

    @Test
    void request_ShouldFailValidation_WhenPasswordTooLong() {
        // Given
        InitPasswordRequest request = new InitPasswordRequest("user123", "123456789012345678901"); // 21位

        // When
        Set<ConstraintViolation<InitPasswordRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("长度需要在8和20之间") || v.getMessage().contains("length must be between 8 and 20")));
    }

    @Test
    void request_ShouldPassValidation_WithMinimumPasswordLength() {
        // Given
        InitPasswordRequest request = new InitPasswordRequest("user123", "12345678"); // 8位

        // When
        Set<ConstraintViolation<InitPasswordRequest>> violations = validator.validate(request);

        // Then
        assertTrue(violations.isEmpty());
    }

    @Test
    void request_ShouldPassValidation_WithMaximumPasswordLength() {
        // Given
        InitPasswordRequest request = new InitPasswordRequest("user123", "12345678901234567890"); // 20位

        // When
        Set<ConstraintViolation<InitPasswordRequest>> violations = validator.validate(request);

        // Then
        assertTrue(violations.isEmpty());
    }

    @Test
    void request_ShouldFailValidation_WhenPasswordIsNull() {
        // Given
        InitPasswordRequest request = new InitPasswordRequest("user123", null);

        // When
        Set<ConstraintViolation<InitPasswordRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("不能为空") || v.getMessage().contains("must not be blank")));
    }

    @Test
    void request_ShouldFailValidation_WhenPasswordIsEmpty() {
        // Given
        InitPasswordRequest request = new InitPasswordRequest("user123", "");

        // When
        Set<ConstraintViolation<InitPasswordRequest>> violations = validator.validate(request);

        // Then
        assertEquals(2, violations.size()); // @NotBlank 和 @Length 都会失败
        assertTrue(violations.stream().anyMatch(v -> 
            v.getMessage().contains("不能为空") || 
            v.getMessage().contains("must not be blank") ||
            v.getMessage().contains("长度需要在8和20之间") || 
            v.getMessage().contains("length must be between 8 and 20")
        ));
    }

    @Test
    void request_ShouldFailValidation_WhenPasswordIsBlank() {
        // Given
        InitPasswordRequest request = new InitPasswordRequest("user123", "   "); // 只包含空白字符

        // When
        Set<ConstraintViolation<InitPasswordRequest>> violations = validator.validate(request);

        // Then
        assertEquals(2, violations.size()); // @NotBlank 和 @Length 都会失败
        assertTrue(violations.stream().anyMatch(v -> 
            v.getMessage().contains("不能为空") || 
            v.getMessage().contains("must not be blank") ||
            v.getMessage().contains("长度需要在8和20之间") || 
            v.getMessage().contains("length must be between 8 and 20")
        ));
    }
} 