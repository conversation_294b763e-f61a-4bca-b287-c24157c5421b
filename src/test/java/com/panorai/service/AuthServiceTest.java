package com.panorai.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {

    @Mock
    private AccountService accountService;

    @InjectMocks
    private AuthService authService;

    private String testUserId;
    private String testPassword;

    @BeforeEach
    void setUp() {
        testUserId = "test-user-id";
        testPassword = "testPassword123";
    }

    @Test
    void initPassword_ShouldReturnTrue_WhenPasswordInitializedSuccessfully() {
        // Given
        doNothing().when(accountService).initPassword(testUserId, testPassword);

        // When
        boolean result = authService.initPassword(testUserId, testPassword);

        // Then
        assertTrue(result);
        verify(accountService).initPassword(testUserId, testPassword);
    }

    @Test
    void initPassword_ShouldThrowException_WhenAccountServiceFails() {
        // Given
        doThrow(new RuntimeException("Account creation failed")).when(accountService).initPassword(testUserId, testPassword);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            authService.initPassword(testUserId, testPassword);
        });
        
        verify(accountService).initPassword(testUserId, testPassword);
    }

    @Test
    void initPassword_ShouldCallAccountServiceWithCorrectParameters() {
        // Given
        doNothing().when(accountService).initPassword(testUserId, testPassword);

        // When
        authService.initPassword(testUserId, testPassword);

        // Then
        verify(accountService).initPassword(eq(testUserId), eq(testPassword));
    }

    @Test
    void initPassword_ShouldAlwaysReturnTrue_WhenNoExceptionThrown() {
        // Given
        String shortPassword = "12345"; // 小于8位的密码
        String longPassword = "123456789012345678901"; // 大于20位的密码
        doNothing().when(accountService).initPassword(anyString(), anyString());

        // When & Then - 测试短密码
        boolean result1 = authService.initPassword(testUserId, shortPassword);
        assertTrue(result1);

        // When & Then - 测试长密码  
        boolean result2 = authService.initPassword(testUserId, longPassword);
        assertTrue(result2);

        // 验证调用
        verify(accountService).initPassword(testUserId, shortPassword);
        verify(accountService).initPassword(testUserId, longPassword);
    }
} 