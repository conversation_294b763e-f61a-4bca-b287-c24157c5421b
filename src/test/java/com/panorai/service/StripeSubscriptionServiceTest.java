package com.panorai.service;

import com.panorai.config.StripeConfig;
import com.panorai.model.entity.Subscription;
import com.panorai.model.entity.User;
import com.panorai.repository.SubscriptionRepository;
import com.panorai.repository.UserRepository;
import com.stripe.exception.StripeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class StripeSubscriptionServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private SubscriptionRepository subscriptionRepository;

    @Mock
    private StripeConfig stripeConfig;

    @InjectMocks
    private StripeSubscriptionService stripeSubscriptionService;

    private User testUser;
    private Subscription testSubscription;

    @BeforeEach
    void setUp() {
        // 初始化测试用户
        testUser = new User("<EMAIL>");
        testUser.setId("user_123");
        testUser.setName("Test User");

        // 初始化测试订阅
        testSubscription = new Subscription();
        testSubscription.setId("sub_123");
        testSubscription.setUserId("user_123");
        testSubscription.setStripeCustomerId("cus_123");
        testSubscription.setStripeSubscriptionId("stripe_sub_123");
        testSubscription.setStatus("active");
        testSubscription.setStripePriceId("price_123");
    }

    @Test
    void getCurrentSubscription_ShouldReturnUserSubscription() {
        // 配置mock
        when(subscriptionRepository.getActiveSubscriptionByUserId(testUser.getId()))
                .thenReturn(Optional.of(testSubscription));

        // 执行测试
        Optional<Subscription> result = stripeSubscriptionService.getCurrentSubscription(testUser.getId());

        // 验证
        assertTrue(result.isPresent());
        assertEquals(testSubscription.getId(), result.get().getId());
    }

    @Test
    void hasValidSubscription_WhenHasActiveSubscription_ShouldReturnTrue() {
        // 配置mock
        when(subscriptionRepository.hasValidSubscription(testUser.getId())).thenReturn(true);

        // 执行测试
        boolean result = stripeSubscriptionService.hasValidSubscription(testUser.getId());

        // 验证
        assertTrue(result);
    }

    @Test
    void hasValidSubscription_WhenNoActiveSubscription_ShouldReturnFalse() {
        // 配置mock
        when(subscriptionRepository.hasValidSubscription(testUser.getId())).thenReturn(false);

        // 执行测试
        boolean result = stripeSubscriptionService.hasValidSubscription(testUser.getId());

        // 验证
        assertFalse(result);
    }

    @Test
    void createCheckoutSession_WhenUserNotExists_ShouldThrowException() {
        // 模拟用户不存在
        when(userRepository.getById("non_existent_user")).thenReturn(null);
        
        // 执行测试并验证异常
        assertThrows(IllegalArgumentException.class, () -> 
            stripeSubscriptionService.createCheckoutSession("non_existent_user", "price_123")
        );
    }

    @Test
    void createCheckoutSession_WhenUserHasActiveSubscription_ShouldThrowException() {
        // 模拟用户存在
        when(userRepository.getById(testUser.getId())).thenReturn(testUser);
        // 模拟用户已有活跃订阅
        when(subscriptionRepository.getActiveSubscriptionByUserId(testUser.getId()))
                .thenReturn(Optional.of(testSubscription));

        // 执行测试并验证异常
        assertThrows(IllegalStateException.class, () -> 
            stripeSubscriptionService.createCheckoutSession(testUser.getId(), "price_123")
        );
    }

    @Test
    void cancelSubscription_WhenNoActiveSubscription_ShouldThrowException() {
        // 模拟用户存在
        when(userRepository.getById(testUser.getId())).thenReturn(testUser);
        // 模拟没有活跃订阅
        when(subscriptionRepository.getActiveSubscriptionByUserId(testUser.getId()))
                .thenReturn(Optional.empty());

        // 执行测试并验证异常
        assertThrows(IllegalStateException.class, () -> 
            stripeSubscriptionService.cancelSubscription(testUser.getId())
        );
    }

    @Test
    void updateSubscriptionPlan_WhenNoActiveSubscription_ShouldThrowException() {
        // 模拟用户存在
        when(userRepository.getById(testUser.getId())).thenReturn(testUser);
        // 模拟没有活跃订阅
        when(subscriptionRepository.getActiveSubscriptionByUserId(testUser.getId()))
                .thenReturn(Optional.empty());

        // 执行测试并验证异常
        assertThrows(IllegalStateException.class, () -> 
            stripeSubscriptionService.updateSubscriptionPlan(testUser.getId(), "new_price_123")
        );
    }

    @Test
    void createPortalSession_WhenNoCustomerId_ShouldThrowException() {
        // 模拟没有客户ID
        when(subscriptionRepository.getByUserId(testUser.getId()))
                .thenReturn(Optional.empty());

        // 执行测试并验证异常
        assertThrows(IllegalStateException.class, () -> 
            stripeSubscriptionService.createPortalSession(testUser.getId())
        );
    }
}