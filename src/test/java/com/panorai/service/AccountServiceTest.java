package com.panorai.service;

import cn.dev33.satoken.secure.BCrypt;
import com.panorai.model.entity.Account;
import com.panorai.repository.AccountRepository;
import com.panorai.utils.IdUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AccountServiceTest {

    @Mock
    private AccountRepository accountRepository;

    @InjectMocks
    private AccountService accountService;

    private String testUserId;
    private String testPassword;
    private String testAccountId;

    @BeforeEach
    void setUp() {
        testUserId = "test-user-id";
        testPassword = "testPassword123";
        testAccountId = "test-account-id";
    }

    @Test
    void initPassword_ShouldCreateAccountWithHashedPassword() {
        // Given
        try (MockedStatic<BCrypt> bcryptMock = Mockito.mockStatic(BCrypt.class);
             MockedStatic<IdUtils> idUtilsMock = Mockito.mockStatic(IdUtils.class);
             MockedStatic<Instant> instantMock = Mockito.mockStatic(Instant.class)) {

            String hashedPassword = "$2a$10$hashedPassword";
            String salt = "$2a$10$salt";
            Instant now = Instant.parse("2024-01-01T00:00:00Z");

            bcryptMock.when(() -> BCrypt.gensalt(10)).thenReturn(salt);
            bcryptMock.when(() -> BCrypt.hashpw(testPassword, salt)).thenReturn(hashedPassword);
            idUtilsMock.when(IdUtils::generateId).thenReturn(testAccountId);
            instantMock.when(Instant::now).thenReturn(now);

            // When
            accountService.initPassword(testUserId, testPassword);

            // Then
            verify(accountRepository).save(argThat(account -> {
                assertEquals(testUserId, account.getId());
                assertEquals(testAccountId, account.getAccountId());
                assertEquals(testUserId, account.getUserId());
                assertEquals("credential", account.getProviderId());
                assertEquals(hashedPassword, account.getPassword());
                assertEquals(now, account.getCreatedAt());
                assertEquals(now, account.getUpdatedAt());
                return true;
            }));
        }
    }

    @Test
    void initPassword_ShouldThrowException_WhenRepositorySaveFails() {
        // Given
        try (MockedStatic<BCrypt> bcryptMock = Mockito.mockStatic(BCrypt.class);
             MockedStatic<IdUtils> idUtilsMock = Mockito.mockStatic(IdUtils.class)) {

            String hashedPassword = "$2a$10$hashedPassword";
            bcryptMock.when(() -> BCrypt.gensalt(10)).thenReturn("salt");
            bcryptMock.when(() -> BCrypt.hashpw(anyString(), anyString())).thenReturn(hashedPassword);
            idUtilsMock.when(IdUtils::generateId).thenReturn(testAccountId);

            doThrow(new RuntimeException("Database error")).when(accountRepository).save(any(Account.class));

            // When & Then
            assertThrows(RuntimeException.class, () -> {
                accountService.initPassword(testUserId, testPassword);
            });
        }
    }

    @Test
    void initPassword_ShouldHashPasswordCorrectly() {
        // Given
        try (MockedStatic<BCrypt> bcryptMock = Mockito.mockStatic(BCrypt.class);
             MockedStatic<IdUtils> idUtilsMock = Mockito.mockStatic(IdUtils.class)) {

            String salt = "$2a$10$salt";
            String hashedPassword = "$2a$10$hashedPassword";

            bcryptMock.when(() -> BCrypt.gensalt(10)).thenReturn(salt);
            bcryptMock.when(() -> BCrypt.hashpw(testPassword, salt)).thenReturn(hashedPassword);
            idUtilsMock.when(IdUtils::generateId).thenReturn(testAccountId);

            // When
            accountService.initPassword(testUserId, testPassword);

            // Then
            bcryptMock.verify(() -> BCrypt.gensalt(10));
            bcryptMock.verify(() -> BCrypt.hashpw(testPassword, salt));
        }
    }

    @Test
    void initPassword_ShouldGenerateUniqueAccountId() {
        // Given
        try (MockedStatic<BCrypt> bcryptMock = Mockito.mockStatic(BCrypt.class);
             MockedStatic<IdUtils> idUtilsMock = Mockito.mockStatic(IdUtils.class)) {

            bcryptMock.when(() -> BCrypt.gensalt(10)).thenReturn("salt");
            bcryptMock.when(() -> BCrypt.hashpw(anyString(), anyString())).thenReturn("hashedPassword");
            idUtilsMock.when(IdUtils::generateId).thenReturn(testAccountId);

            // When
            accountService.initPassword(testUserId, testPassword);

            // Then
            idUtilsMock.verify(IdUtils::generateId);
            verify(accountRepository).save(argThat(account -> 
                testAccountId.equals(account.getAccountId())
            ));
        }
    }
} 