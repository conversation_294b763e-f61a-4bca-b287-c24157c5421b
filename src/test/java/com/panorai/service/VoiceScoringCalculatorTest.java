package com.panorai.service;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;

/**
 * Voice打分算法计算器测试
 * 独立测试打分算法，不依赖Spring框架
 */
@DisplayName("Voice打分算法计算器测试")
class VoiceScoringCalculatorTest {

    @Test
    @DisplayName("测试24小时文章和25小时有11个赞内容的打分结果")
    void testScoringComparison() {
        Instant now = Instant.now();
        
        System.out.println("=== Voice打分算法测试结果 ===\n");
        
        // 测试场景1: 24小时前发布的文章
        testArticle24Hours(now);
        
        System.out.println();
        
        // 测试场景2: 25小时前发布，有11个赞的内容，不同AI得分
        testVoice25Hours11Likes(now, 0.5, "AI得分0.5分");
        testVoice25Hours11Likes(now, 0.9, "AI得分0.9分"); 
        testVoice25Hours11Likes(now, 0.2, "AI得分0.2分");
        
        System.out.println("\n=== 各组件分数详细分析 ===");
        analyzeComponents(now);
    }
    
    private void testArticle24Hours(Instant now) {
        System.out.println("【场景1】24小时前发布的文章：");
        
        Instant createdAt = now.minus(24, ChronoUnit.HOURS);
        
        // 文章固定分数为0.9
        double fixedScore = 0.9;
        
        // 时间新鲜度计算
        double freshnessScore = calculateFreshnessScore(createdAt, now);
        
        // 惩罚分数（用户未查看过）
        double penaltyScore = 1.0;
        
        // 最终得分
        double finalScore = fixedScore * freshnessScore * penaltyScore;
        
        System.out.println("  - 固定分数: " + fixedScore + " (文章类型)");
        System.out.println("  - 时间新鲜度: " + String.format("%.4f", freshnessScore) + " (24小时)");
        System.out.println("  - 惩罚分数: " + penaltyScore + " (未查看过)");
        System.out.println("  - 最终得分: " + String.format("%.4f", finalScore));
    }
    
    private void testVoice25Hours11Likes(Instant now, double aiScore, String description) {
        System.out.println("【场景2】25小时前发布，11个赞的内容（" + description + "）：");
        
        Instant createdAt = now.minus(25, ChronoUnit.HOURS);
        
        // 1. 热度分数计算
        int likedCount = 11;
        int forwardedCount = 0;
        int repliedCount = 0;
        double totalHeatScore = likedCount * 1.0 + forwardedCount * 2.0 + repliedCount * 3.0; // 11分
        double heatScore = Math.min(1.0, Math.log(totalHeatScore + 1) / Math.log(100));
        
        // 2. 固定分数计算 (AI分 * 0.6 + 热度分 * 0.4)
        double fixedScore = aiScore * 0.6 + heatScore * 0.4;
        
        // 3. 时间新鲜度计算
        double freshnessScore = calculateFreshnessScore(createdAt, now);
        
        // 4. 惩罚分数（用户未查看过）
        double penaltyScore = 1.0;
        
        // 5. 最终分数
        double finalScore = fixedScore * freshnessScore * penaltyScore;
        
        System.out.println("  - AI分数: " + aiScore);
        System.out.println("  - 热度分数: " + String.format("%.4f", heatScore) + " (基于11个赞，热度原始值=" + totalHeatScore + ")");
        System.out.println("  - 固定分数: " + String.format("%.4f", fixedScore) + " (AI*0.6 + 热度*0.4)");
        System.out.println("  - 时间新鲜度: " + String.format("%.4f", freshnessScore) + " (25小时)");
        System.out.println("  - 惩罚分数: " + penaltyScore + " (未查看过)");
        System.out.println("  - 最终得分: " + String.format("%.4f", finalScore));
        System.out.println();
    }
    
    private void analyzeComponents(Instant now) {
        // 时间新鲜度对比
        System.out.println("【时间新鲜度分析】");
        for (int hours : new int[]{1, 6, 12, 24, 25, 48, 72}) {
            Instant createdAt = now.minus(hours, ChronoUnit.HOURS);
            double score = calculateFreshnessScore(createdAt, now);
            System.out.println("  " + hours + "小时: " + String.format("%.4f", score));
        }
        
        System.out.println();
        
        // 热度分数对比
        System.out.println("【热度分数分析】");
        for (int likes : new int[]{0, 1, 5, 10, 11, 20, 50, 100}) {
            double totalScore = likes * 1.0;
            double heatScore = Math.min(1.0, Math.log(totalScore + 1) / Math.log(100));
            System.out.println("  " + likes + "个赞: " + String.format("%.4f", heatScore));
        }
        
        System.out.println();
        
        // AI得分对最终结果的影响
        System.out.println("【AI得分影响分析（25小时，11个赞）】");
        Instant createdAt = now.minus(25, ChronoUnit.HOURS);
        double freshnessScore = calculateFreshnessScore(createdAt, now);
        double heatScore = Math.min(1.0, Math.log(12) / Math.log(100)); // 11个赞
        
        for (double ai : new double[]{0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0}) {
            double fixedScore = ai * 0.6 + heatScore * 0.4;
            double finalScore = fixedScore * freshnessScore * 1.0;
            System.out.println("  AI得分" + String.format("%.1f", ai) + ": 最终得分" + String.format("%.4f", finalScore));
        }
    }
    
    /**
     * 计算时间新鲜度分数
     * 算法来源于VoiceScoringService.calculateFreshnessScore()
     */
    private double calculateFreshnessScore(Instant createdAt, Instant now) {
        long hours = Duration.between(createdAt, now).toHours();
        
        double raw = Math.exp(-0.02 * hours);
        if (hours <= 24) {
            raw = Math.exp(-0.01 * hours);
        }
        
        return Math.max(raw, 0.2); // 保底 0.2 分，旧帖也能参与竞争
    }
    
    @Test
    @DisplayName("验证算法正确性")
    void testAlgorithmCorrectness() {
        Instant now = Instant.now();
        
        // 验证24小时内使用不同的衰减系数
        double score24h = calculateFreshnessScore(now.minus(24, ChronoUnit.HOURS), now);
        double score25h = calculateFreshnessScore(now.minus(25, ChronoUnit.HOURS), now);
        
        System.out.println("\n=== 算法正确性验证 ===");
        System.out.println("24小时新鲜度: " + String.format("%.4f", score24h) + " (使用0.01衰减系数)");
        System.out.println("25小时新鲜度: " + String.format("%.4f", score25h) + " (使用0.02衰减系数)");
        System.out.println("验证: 25小时 < 24小时? " + (score25h < score24h));
        
        // 验证热度分数的对数归一化
        System.out.println("\n热度分数验证:");
        System.out.println("11个赞的热度分数: " + String.format("%.4f", Math.min(1.0, Math.log(12) / Math.log(100))));
        System.out.println("100个赞的热度分数: " + String.format("%.4f", Math.min(1.0, Math.log(101) / Math.log(100))));
        
        // 验证AI得分权重
        System.out.println("\n权重验证（AI=0.8, 热度=0.3）:");
        double fixedScore = 0.8 * 0.6 + 0.3 * 0.4;
        System.out.println("固定分数: " + String.format("%.4f", fixedScore) + " = 0.8*0.6 + 0.3*0.4");
    }
} 