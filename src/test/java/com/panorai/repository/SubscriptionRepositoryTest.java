package com.panorai.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.panorai.model.entity.Subscription;
import com.panorai.repository.impl.SubscriptionRepositoryImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
class SubscriptionRepositoryTest {

    @Autowired
    private SubscriptionRepository subscriptionRepository;

    private Subscription testSubscription;

    @BeforeEach
    void setUp() {
        // 清理数据
        subscriptionRepository.remove(new LambdaQueryWrapper<>());

        // 创建测试订阅
        testSubscription = new Subscription();
        testSubscription.setUserId("user_123");
        testSubscription.setStripeCustomerId("cus_123");
        testSubscription.setStripeSubscriptionId("sub_123");
        testSubscription.setStatus("active");
        testSubscription.setPlan("basic");
        testSubscription.setStripePriceId("price_123");
        testSubscription.setPeriodStart(Instant.now());
        testSubscription.setPeriodEnd(Instant.now().plusSeconds(2592000)); // 30天后
        subscriptionRepository.save(testSubscription);
    }

    @Test
    void getActiveSubscriptionByUserId_WhenActiveExists_ShouldReturn() {
        // 执行测试
        Optional<Subscription> result = subscriptionRepository.getActiveSubscriptionByUserId("user_123");

        // 验证
        assertTrue(result.isPresent());
        assertEquals("active", result.get().getStatus());
        assertEquals("user_123", result.get().getUserId());
    }

    @Test
    void getActiveSubscriptionByUserId_WhenOnlyCanceledExists_ShouldReturnEmpty() {
        // 修改状态为已取消
        testSubscription.setStatus("canceled");
        subscriptionRepository.updateById(testSubscription);

        // 执行测试
        Optional<Subscription> result = subscriptionRepository.getActiveSubscriptionByUserId("user_123");

        // 验证
        assertFalse(result.isPresent());
    }

    @Test
    void getActiveSubscriptionByUserId_WithTrialingStatus_ShouldReturn() {
        // 创建试用期订阅
        Subscription trialSubscription = new Subscription();
        trialSubscription.setUserId("user_456");
        trialSubscription.setStatus("trialing");
        trialSubscription.setStripeCustomerId("cus_456");
        trialSubscription.setStripeSubscriptionId("sub_456");
        subscriptionRepository.save(trialSubscription);

        // 执行测试
        Optional<Subscription> result = subscriptionRepository.getActiveSubscriptionByUserId("user_456");

        // 验证
        assertTrue(result.isPresent());
        assertEquals("trialing", result.get().getStatus());
    }

    @Test
    void getByStripeSubscriptionId_WhenExists_ShouldReturn() {
        // 执行测试
        Optional<Subscription> result = subscriptionRepository.getByStripeSubscriptionId("sub_123");

        // 验证
        assertTrue(result.isPresent());
        assertEquals("sub_123", result.get().getStripeSubscriptionId());
    }

    @Test
    void getByStripeCustomerId_WhenExists_ShouldReturn() {
        // 执行测试
        Optional<Subscription> result = subscriptionRepository.getByStripeCustomerId("cus_123");

        // 验证
        assertTrue(result.isPresent());
        assertEquals("cus_123", result.get().getStripeCustomerId());
    }

    @Test
    void getByUserId_WhenMultipleExists_ShouldReturnLatest() {
        // 创建旧订阅
        Subscription oldSubscription = new Subscription();
        oldSubscription.setUserId("user_123");
        oldSubscription.setStatus("canceled");
        oldSubscription.setStripeCustomerId("cus_old");
        oldSubscription.setCreatedAt(Instant.now().minusSeconds(86400)); // 1天前
        subscriptionRepository.save(oldSubscription);

        // 执行测试
        Optional<Subscription> result = subscriptionRepository.getByUserId("user_123");

        // 验证返回最新的订阅
        assertTrue(result.isPresent());
        assertEquals("cus_123", result.get().getStripeCustomerId());
    }

    @Test
    void hasValidSubscription_WhenActiveExists_ShouldReturnTrue() {
        // 执行测试
        boolean result = subscriptionRepository.hasValidSubscription("user_123");

        // 验证
        assertTrue(result);
    }

    @Test
    void hasValidSubscription_WhenNoActiveExists_ShouldReturnFalse() {
        // 修改状态
        testSubscription.setStatus("canceled");
        subscriptionRepository.updateById(testSubscription);

        // 执行测试
        boolean result = subscriptionRepository.hasValidSubscription("user_123");

        // 验证
        assertFalse(result);
    }

    @Test
    void hasValidSubscription_WhenUserNotExists_ShouldReturnFalse() {
        // 执行测试
        boolean result = subscriptionRepository.hasValidSubscription("non_existent_user");

        // 验证
        assertFalse(result);
    }

    @Test
    void multipleUsersWithSubscriptions_ShouldNotInterfere() {
        // 创建另一个用户的订阅
        Subscription otherSubscription = new Subscription();
        otherSubscription.setUserId("user_789");
        otherSubscription.setStatus("active");
        otherSubscription.setStripeCustomerId("cus_789");
        otherSubscription.setStripeSubscriptionId("sub_789");
        subscriptionRepository.save(otherSubscription);

        // 验证查询互不干扰
        Optional<Subscription> user1 = subscriptionRepository.getActiveSubscriptionByUserId("user_123");
        Optional<Subscription> user2 = subscriptionRepository.getActiveSubscriptionByUserId("user_789");

        assertTrue(user1.isPresent());
        assertTrue(user2.isPresent());
        assertNotEquals(user1.get().getId(), user2.get().getId());
        assertEquals("user_123", user1.get().getUserId());
        assertEquals("user_789", user2.get().getUserId());
    }
}