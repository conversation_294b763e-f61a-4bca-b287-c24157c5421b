package com.telecom.apigateway;

import cn.hutool.core.util.CreditCodeUtil;
import cn.hutool.core.util.IdcardUtil;
import lombok.extern.slf4j.Slf4j;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * @program: APIWG-Service
 * @ClassName RSATest
 * @description:
 * @author: Levi
 * @create: 2025-03-28 12:09
 * @Version 1.0
 **/
@Slf4j
public class KeyPairGeneratorExample {
    public static void main(String[] args) throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        String publicKeyBase64 = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
        String privateKeyBase64 = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());

        System.out.println("Public Key (Base64): " + publicKeyBase64);
        System.out.println("Private Key (Base64): " + privateKeyBase64);
        System.out.println(IdcardUtil.isValidCard("500233199509154211"));
        System.out.println(IdcardUtil.isValidCard("500233200012125536"));
        System.out.println(CreditCodeUtil.isCreditCode("92500236MA60WYBR66"));

    }
}