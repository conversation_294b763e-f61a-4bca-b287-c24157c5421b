package com.telecom.apigateway.interceptor;

import com.telecom.apigateway.config.interceptor.SensitiveUrlLogInterceptor;
import com.telecom.apigateway.model.entity.UserInfo;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.service.OperationLogService;
import com.telecom.apigateway.service.UserContextService;
import com.telecom.apigateway.service.UserInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class SensitiveUrlLogInterceptorTest {

    @Mock
    private OperationLogService operationLogService;

    @Mock
    private UserInfoService userInfoService;

    @Mock
    private UserContextService userContextService;

    @InjectMocks
    private SensitiveUrlLogInterceptor interceptor;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        
        // 设置RequestContextHolder
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
        
        // 模拟用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setUsername("testuser");
        userInfo.setRealName("测试用户");
        when(userInfoService.getByUsername(anyString())).thenReturn(Optional.of(userInfo));
        when(userContextService.getCurrentUsername()).thenReturn("testUser");
    }

    @Test
    void shouldLogSensitiveUrlAccess() {
        // 设置敏感URL
        request.setRequestURI("/api/risk/portrait");
        
        // 执行拦截器
        boolean result = interceptor.preHandle(request, response, null);
        
        // 验证结果
        verify(operationLogService, times(1)).createLog(
                anyString(),
                eq(OperationTypeEnum.VIEW),
                any(),
                anyString(),
                anyString()
        );
        
        // 确保拦截器返回true（继续处理请求）
        assert result;
    }

    @Test
    void shouldNotLogNonSensitiveUrlAccess() throws Exception {
        // 设置非敏感URL
        request.setRequestURI("/api/normal/endpoint");
        
        // 执行拦截器
        boolean result = interceptor.preHandle(request, response, null);
        
        // 验证结果：不应该调用createLog方法
        verify(operationLogService, times(0)).createLog(
                anyString(),
                any(),
                any(),
                anyString(),
                anyString()
        );
        
        // 确保拦截器返回true（继续处理请求）
        assert result;
    }
}
