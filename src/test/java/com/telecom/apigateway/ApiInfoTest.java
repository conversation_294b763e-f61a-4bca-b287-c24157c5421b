package com.telecom.apigateway;

import com.telecom.apigateway.service.ApiInfoService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-05-28
 */
@SpringBootTest
public class ApiInfoTest {

    @Resource
    private ApiInfoService apiInfoService;

    @Test
    void testResetRedisStore() {
        apiInfoService.resetRedisStore();
        Assertions.assertTrue(true);
    }
}
