package com.telecom.apigateway;

import com.telecom.apigateway.common.excption.WafException;
import com.telecom.apigateway.model.entity.Blocklist;
import com.telecom.apigateway.service.BlockListWafConfigService;
import com.telecom.apigateway.service.BlocklistService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024-12-08
 */
@SpringBootTest
@Slf4j
public class WafTest {
    @Resource
    private BlockListWafConfigService blockListWafConfigService;
    @Resource
    private BlocklistService blocklistService;

    @Test
    void addRawRule() {
        String ruleId = "{\"id\": 12,\"state\": \"on\",\"action\": [\"deny\"],\"hostname\": [\"*\",\"\"],\"uri\": " +
                "[\"*\",\"\"],\"app_ext\": [[\"posts_all\",[\">>>+\",\"jio\",false]]]}";
    }

    @Test
    void addConfig() throws WafException {
        Blocklist blocklist = blocklistService.findById("e561297afaa04b03a6aa733698c30328").get();
        // blockListWafConfigService.addRule(blocklist);
    }


    @Test
    void delByBlockId() throws WafException {
      //  blockListWafConfigService.deleteByBlockId(null);
    }

    void saveConfig2File() {
        // wafClient.save2File(WafClient.Mod.app_Mod);
    }
}

