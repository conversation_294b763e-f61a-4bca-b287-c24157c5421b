package com.telecom.apigateway.utils;

import cn.hutool.core.collection.ListUtil;
import lombok.Data;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class MapUtilTest {
    @Test
    void list2Map() {
        TestClass testClass1 = new TestClass();
        testClass1.setId("1");
        testClass1.setName("test1");

        TestClass testClass2 = new TestClass();
        testClass2.setId("1");
        testClass2.setName("test2");

        TestClass testClass3 = new TestClass();
        testClass3.setId("2");
        testClass3.setName("test3");

        List<TestClass> testClasses = ListUtil.of(testClass1, testClass2, testClass3);
        Map<String, List<TestClass>> result = MapUtil.grouping(TestClass::getId, testClasses);
        assertEquals( 2,result.size());
        assertEquals( 2,result.get("1").size());
        assertEquals( 1,result.get("2").size());
    }

    @Data
    class TestClass {
        private String id;
        private String name;
    }

}