package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.mapper.ApplicationMapper;
import com.telecom.apigateway.mapper.RiskLogNewMapper;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.enums.ProtocolEnum;
import com.telecom.apigateway.model.vo.request.AddApplicationRequest;
import com.telecom.apigateway.model.vo.request.UpdateApplicationRequest;
import com.telecom.apigateway.model.vo.response.ApplicationDetailResponse;
import com.telecom.apigateway.model.vo.response.ApplicationRiskResponse;
import com.telecom.apigateway.service.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ApplicationBusinessServiceImplTest {

    @Mock
    private ApplicationService applicationService;
    @Mock
    private ApiInfoService apiInfoService;
    @Mock
    private NginxAccessLogService nginxAccessLogService;
    @Mock
    private RiskLogService riskLogService;
    @Mock
    private NginxLogEsClient nginxLogEsClient;
    @Mock
    private RuleService ruleService;
    @Mock
    private RegionService regionService;
    @Mock
    private AbnormalBehaviorRuleTriggerService abnormalBehaviorRuleTriggerService;

    @Mock
    private LogHelperService logHelperService;

    @Mock
    private RiskLogNewService riskLogNewService;
    @Mock
    private RiskLogNewMapper riskLogNewMapper;

    private ApplicationBusinessServiceImpl service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        service = new ApplicationBusinessServiceImpl(
                applicationService,
                apiInfoService,
                nginxAccessLogService,
                ruleService,
                regionService,
                new ThreatHelperService(applicationService),
                abnormalBehaviorRuleTriggerService,
                logHelperService,
                riskLogNewService,
                riskLogNewMapper
        );
    }

    @Test
    void addApplication_WhenValidRequest_ShouldCreateNewApplication() {
        // Given
        String userId = "testUser";
        AddApplicationRequest request = new AddApplicationRequest();
        request.setName("TestApp");
        request.setHost("localhost");
        request.setPort("8080");
        request.setUri("/api");
        request.setType(ApplicationTypeEnum.APPLICATION);
        request.setProtocol(ProtocolEnum.HTTP);
        request.setOwner("TestOwner");
        request.setPhone("***********");
        request.setEmail("<EMAIL>");

        // Mock the ApiInfo query and update
        LambdaQueryChainWrapper<ApiInfo> apiQueryWrapper = mock(LambdaQueryChainWrapper.class, RETURNS_SELF);
        when(apiQueryWrapper.list()).thenReturn(Collections.emptyList());
        when(apiInfoService.lambdaQuery()).thenReturn(apiQueryWrapper);

        // Mock the application query and mapper
        ApplicationMapper applicationMapper = mock(ApplicationMapper.class);
        when(applicationService.getBaseMapper()).thenReturn(applicationMapper);
        when(applicationService.getByUrl(request.getHost(), request.getPort(), request.getUri()))
                .thenReturn(Collections.emptyList());

        // When
        String result = service.addApplication(userId, request);

        // Then
        assertNotNull(result);
        verify(applicationMapper).insert(any(Application.class));
    }

    @Test
    void addApplication_WhenDuplicateApplication_ShouldThrowException() {
        // Given
        String userId = "testUser";
        AddApplicationRequest request = new AddApplicationRequest();
        request.setHost("localhost");
        request.setPort("8080");
        request.setUri("/api");
        request.setProtocol(ProtocolEnum.HTTP);

        Application existingApp = new Application(
                "existingId",
                "userId",
                "ExistingApp",
                "localhost",
                "8080",
                ProtocolEnum.HTTP,
                new String[]{"111", "11100"}
        );

        // Mock the application query
        when(applicationService.getByUrl(request.getHost(), request.getPort(), request.getUri()))
                .thenReturn(Collections.singletonList(existingApp));

        // When/Then
        assertThrows(BusinessException.class, () -> service.addApplication(userId, request));
    }

    @Test
    void updateApplication_WhenValidRequest_ShouldUpdateApplication() {
        // Given
        String userId = "testUser";
        String applicationId = "testAppId";
        UpdateApplicationRequest request = new UpdateApplicationRequest();
        request.setApplicationId(applicationId);
        request.setName("UpdatedApp");
        request.setOwner("NewOwner");
        request.setPhone("***********");
        request.setEmail("<EMAIL>");

        Application existingApp = new Application(
                applicationId,
                userId,
                "OldApp",
                "localhost",
                "8080",
                ProtocolEnum.HTTP,
                new String[]{"111", "11100"}
        );

        when(applicationService.getByApplicationId(applicationId)).thenReturn(Optional.of(existingApp));
        when(applicationService.updateById(any())).thenReturn(true);

        // When
        String result = service.updateApplication(userId, request).getApplicationId();

        // Then
        assertEquals(applicationId, result);
        verify(applicationService).updateById(any(Application.class));
    }

    @Test
    void deleteApplication_WhenValidId_ShouldDeleteApplication() {
        // Given
        String userId = "testUser";
        String applicationId = "testAppId";
        Application application = new Application(
                applicationId,
                userId,
                "TestApp",
                "localhost",
                "8080",
                ProtocolEnum.HTTP,
                new String[]{"111", "11100"}
        );

        when(applicationService.getByApplicationId(applicationId)).thenReturn(Optional.of(application));
        when(applicationService.updateById(any())).thenReturn(true);

        // Mock the update chain
        LambdaUpdateChainWrapper<ApiInfo> mockUpdateWrapper = mock(LambdaUpdateChainWrapper.class, RETURNS_SELF);
        when(mockUpdateWrapper.update()).thenReturn(true);
        when(apiInfoService.lambdaUpdate()).thenReturn(mockUpdateWrapper);

        // When
        service.deleteApplication(userId, applicationId);

        // Then
        verify(applicationService).updateById(any(Application.class));
        verify(mockUpdateWrapper).eq(any(), eq(applicationId));
        verify(mockUpdateWrapper).set(any(), eq(""));
        verify(mockUpdateWrapper).update();
    }

    @Test
    void getDetail_WhenValidId_ShouldReturnApplicationDetail() {
        // Given
        String applicationId = "testAppId";
        Application application = new Application(
                applicationId,
                "testUser",
                "TestApp",
                "localhost",
                "8080",
                ProtocolEnum.HTTP,
                new String[]{"111", "11100"}
        );

        ApiInfo activeApi = createApiInfo(true);
        activeApi.setHost("localhost");
        activeApi.setPort(8080);
        activeApi.setUri("/api/active");
        activeApi.setIsActive(true);
        activeApi.setSensitiveLevel(3);

        ApiInfo inactiveApi = createApiInfo(false);
        inactiveApi.setHost("localhost");
        inactiveApi.setPort(8080);
        inactiveApi.setUri("/api/inactive");
        inactiveApi.setIsActive(false);
        inactiveApi.setSensitiveLevel(1);

        List<ApiInfo> apiInfos = Arrays.asList(activeApi, inactiveApi);

        when(applicationService.getByApplicationId(applicationId)).thenReturn(Optional.of(application));
        when(apiInfoService.getByApplicationId(applicationId)).thenReturn(apiInfos);
        when(nginxLogEsClient.query(any())).thenReturn(Collections.emptyList());
        // when(apiInfoService.getSensitiveApi()).thenReturn(apiInfos);

        // When
        ApplicationDetailResponse result = service.getDetail(applicationId, false);

        // Then
        assertNotNull(result);
        assertEquals("TestApp", result.getName());
        assertEquals(2, result.getApiCount().getTotal());
        assertEquals(1, result.getApiCount().getActivationCount());
    }

    private ApiInfo createApiInfo(boolean isActive) {
        ApiInfo apiInfo = new ApiInfo();
        apiInfo.setId("api_" + (isActive ? "active" : "inactive"));
        apiInfo.setIsActive(isActive);
        apiInfo.setDiscoverTime(LocalDateTime.now());
        apiInfo.setDeleted(false);
        return apiInfo;
    }

    @Test
    void getRisksByApplicationId_ShouldReturnRiskCounts() {
        // Given
        String applicationId = "testAppId";
        List<ApiInfo> apiInfos = Arrays.asList(
                createApiInfoWithRisk(1),
                createApiInfoWithRisk(2),
                createApiInfoWithRisk(3)
        );

        when(apiInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(apiInfos);

        // When
        ApplicationRiskResponse result = service.getRisksByApplicationId(applicationId, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getRiskApiCount().getLow());
        assertEquals(1, result.getRiskApiCount().getMedium());
        assertEquals(1, result.getRiskApiCount().getHigh());
    }

    private ApiInfo createApiInfoWithRisk(int riskLevel) {
        ApiInfo apiInfo = new ApiInfo();
        apiInfo.setId("api_risk_" + riskLevel);
        apiInfo.setRiskLevel(riskLevel);
        apiInfo.setDeleted(false);
        apiInfo.setDiscoverTime(LocalDateTime.now());
        return apiInfo;
    }
}
