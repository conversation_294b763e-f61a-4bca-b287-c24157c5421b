package com.telecom.apigateway.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.dto.BlocklistDTO;
import com.telecom.apigateway.model.entity.Blocklist;
import com.telecom.apigateway.model.entity.IpGroup;
import com.telecom.apigateway.model.vo.request.BlocklistQueryRequest;
import com.telecom.apigateway.model.vo.request.BlocklistRequest;
import com.telecom.apigateway.service.BlocklistService;
import com.telecom.apigateway.service.IpGroupService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class BlocklistBusinessServiceTest {
    @Mock
    private BlocklistService blocklistService;

    @Mock
    private IpGroupService ipGroupService;

    @InjectMocks
    private BlocklistBusinessService blocklistBusinessService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // 列表查询测试
    @Test
    public void testListWithPagination() {
        BlocklistQueryRequest pageRequest = new BlocklistQueryRequest();
        Page<Blocklist> mockPage = new Page<>(1, 20, 2);
        mockPage.setRecords(Arrays.asList(
                createMockList("测试名单1", "White", JSONUtil.toJsonStr(Collections.singletonList(new BlocklistRequest.Condition("sourceIp", "equals", "***********")))),
                createMockList("测试名单2", "Black", JSONUtil.toJsonStr(Collections.singletonList(new BlocklistRequest.Condition("path", "contains", "/api/test"))))
        ));

        when(blocklistService.findAll(pageRequest)).thenReturn(mockPage);

        Page<BlocklistDTO> result = blocklistBusinessService.list(pageRequest);

        assertEquals(2, result.getRecords().size());
        assertEquals("测试名单1", result.getRecords().get(0).getName());
        // 验证排序是否正确
        verify(blocklistService).findAll(pageRequest);
    }

    // 新增规则测试
    @Test
    public void testAddWithSourceIpEquals() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("测试IP白名单");
        list.setType("White");
        list.setCondition(Collections.singletonList(new BlocklistRequest.Condition("sourceIp", "equals", "***********")));

        when(blocklistService.save(any())).thenReturn(true);

        Blocklist result = blocklistBusinessService.add(list);

        assertNotNull(result);
        assertEquals("测试IP白名单", result.getName());
    }

    @Test
    public void testAddWithNameExceedLimit() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("这是一个超过二十个字符的黑白名单名称测试用例");

        assertThrows(IllegalArgumentException.class, () ->
                blocklistBusinessService.add(list)
        );
    }

    @Test
    public void testAddWithPathCondition() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("路径测试");
        list.setType("Black");
        list.setCondition(Collections.singletonList(new BlocklistRequest.Condition("path", "contains", "/api/test")));

        when(blocklistService.save(any())).thenReturn(true);

        Blocklist result = blocklistBusinessService.add(list);

        assertNotNull(result);
        assertEquals(JSONUtil.toJsonStr(Collections.singletonList(new BlocklistRequest.Condition("path", "contains", "/api/test"))), result.getCondition());
    }

    @Test
    public void testAddWithHeaderCondition() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("Header测试");
        list.setType("Black");
        list.setCondition(Collections.singletonList(new BlocklistRequest.Condition("header", "equals", "X-Custom-Header")));

        when(blocklistService.save(any())).thenReturn(true);

        Blocklist result = blocklistBusinessService.add(list);

        assertNotNull(result);
    }

    // 状态更新测试
    @Test
    public void testUpdateStatus() {
        String id = "testId";
        boolean status = true;

        when(blocklistService.findById(id))
                .thenReturn(Optional.of(new Blocklist()));

        blocklistBusinessService.updateStatus(id, status);

        verify(blocklistService).updateById(any());
    }

    @Test
    public void testBatchUpdateStatus() {
        List<String> ids = Arrays.asList("id1", "id2");
        boolean status = true;

        when(blocklistService.findAllById(ids))
                .thenReturn(Arrays.asList(new Blocklist(), new Blocklist()));

        blocklistBusinessService.batchUpdateStatus(ids, status);

        verify(blocklistService, times(1)).updateBatchById(any());
    }

    // 删除测试
    @Test
    public void testDelete() {
        String id = "testId";

        blocklistBusinessService.delete(id);

        verify(blocklistService).deleteById(id);
    }

    @Test
    public void testBatchDelete() {
        List<String> ids = Arrays.asList("id1", "id2");

        blocklistBusinessService.batchDelete(ids);

        verify(blocklistService).deleteAllById(ids);
    }

    // 新增模糊匹配测试
    @Test
    public void testAddWithValidFuzzyHostMatch() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("Host模糊匹配测试");
        list.setType("Black");
        list.setCondition(Collections.singletonList(
                new BlocklistRequest.Condition("host", "fuzzy", "example.com")
        ));

        when(blocklistService.save(any())).thenReturn(true);

        Blocklist result = blocklistBusinessService.add(list);

        assertNotNull(result);
        assertEquals("Host模糊匹配测试", result.getName());
        // 验证条件是否正确保存
        assertNotNull(result.getCondition());
        assertTrue(result.getCondition().contains("fuzzy"));
    }

    @Test
    public void testAddWithInvalidFuzzyMatchTarget() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("无效的模糊匹配");
        list.setType("Black");
        list.setCondition(Collections.singletonList(
                new BlocklistRequest.Condition("path", "fuzzy", "/api/test")
        ));

        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> blocklistBusinessService.add(list)
        );
        assertEquals("模糊匹配只能用于Host", exception.getMessage());
    }

    // 组值匹配测试
    @Test
    public void testAddWithValidGroupValues() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("IP组测试");
        list.setType("Black");
        list.setCondition(Collections.singletonList(
                new BlocklistRequest.Condition("sourceIp", "inGroup", "***********,***********")
        ));

        when(blocklistService.save(any())).thenReturn(true);
        when(ipGroupService.getByGroupIds(any()))
                .thenReturn(Arrays.asList(new IpGroup("1", "test"), new IpGroup("2", "test")));
        Blocklist result = blocklistBusinessService.add(list);

        assertNotNull(result);
        assertEquals("IP组测试", result.getName());
        // 验证条件是否包含所有IP
        assertTrue(result.getCondition().contains("***********"));
        assertTrue(result.getCondition().contains("***********"));
    }

    @Test
    public void testAddWithEmptyGroupValues() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("空组值测试");
        list.setType("Black");
        list.setCondition(Collections.singletonList(
                new BlocklistRequest.Condition("sourceIp", "inGroup", "")
        ));

        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> blocklistBusinessService.add(list)
        );
        assertEquals("IP不能为空", exception.getMessage());
    }

    @Test
    public void testAddWithInvalidIpInGroup() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("无效IP组测试");
        list.setType("Black");
        list.setCondition(Collections.singletonList(
                new BlocklistRequest.Condition("sourceIp", "inGroup", "256.256.256.256,***********")
        ));

        when(ipGroupService.getByGroupIds(any()))
                .thenReturn(Collections.singletonList(new IpGroup("1", "test")));

        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> blocklistBusinessService.add(list)
        );
        assertEquals("IP组错误", exception.getMessage());
    }

    // 多条件组合测试
    @Test
    public void testAddWithMultipleConditions() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("多条件测试");
        list.setType("Black");
        list.setCondition(Arrays.asList(
                new BlocklistRequest.Condition("host", "fuzzy", "example.com"),
                new BlocklistRequest.Condition("sourceIp", "inGroup", "***********,***********")
        ));

        when(blocklistService.save(any())).thenReturn(true);
        when(ipGroupService.getByGroupIds(any()))
                .thenReturn(Arrays.asList(new IpGroup("1", "test"), new IpGroup("2", "test")));
        when(blocklistService.save(any())).thenReturn(true);

        Blocklist result = blocklistBusinessService.add(list);

        assertNotNull(result);
        assertEquals("多条件测试", result.getName());
        // 验证所有条件是否都被正确保存
        assertTrue(result.getCondition().contains("fuzzy"));
        assertTrue(result.getCondition().contains("inGroup"));
    }

    // 边界值测试
    @Test
    public void testAddWithNameBoundary() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("这是一个很长的二十个字符的名称测试用例啊");  // 正好20个字符
        list.setType("Black");
        list.setCondition(Collections.singletonList(
                new BlocklistRequest.Condition("host", "fuzzy", "example.com")
        ));

        when(blocklistService.save(any())).thenReturn(true);

        Blocklist result = blocklistBusinessService.add(list);

        assertNotNull(result);
        assertEquals(20, result.getName().length());
    }

    @Test
    public void testAddWithEmptyConditionList() {
        BlocklistRequest list = new BlocklistRequest();
        list.setName("空条件测试");
        list.setType("Black");
        list.setCondition(Collections.emptyList());

        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> blocklistBusinessService.add(list)
        );
        assertEquals("条件不能为空", exception.getMessage());
    }

    @Test
    public void testUpdate() {
        String blockId = "testId";
        BlocklistRequest request = new BlocklistRequest();
        request.setName("更新后的名称");
        request.setType("Black");
        request.setCondition(Collections.singletonList(
                new BlocklistRequest.Condition("sourceIp", "equals", "***********")
        ));

        Blocklist existingList = new Blocklist();
        existingList.setBlockId(blockId);
        existingList.setName("原名称");
        existingList.setType("White");
        existingList.setCondition(JSONUtil.toJsonStr(Collections.singletonList(
                new BlocklistRequest.Condition("sourceIp", "equals", "***********")
        )));

        when(blocklistService.findById(blockId))
                .thenReturn(Optional.of(existingList));
        when(blocklistService.updateById(any())).thenReturn(true);

        Blocklist result = blocklistBusinessService.update(blockId, request);

        assertNotNull(result);
        assertEquals("更新后的名称", result.getName());
        assertEquals("Black", result.getType());
        verify(blocklistService).updateById(any());
    }

    @Test
    public void testUpdateWithInvalidId() {
        String blockId = "invalidId";
        BlocklistRequest request = new BlocklistRequest();
        request.setName("测试名称");

        when(blocklistService.findById(blockId))
                .thenReturn(Optional.empty());

        assertThrows(IllegalArgumentException.class, () ->
                blocklistBusinessService.update(blockId, request)
        );
    }

    // 辅助方法
    private Blocklist createMockList(String name, String type, String condition) {
        Blocklist list = new Blocklist();
        list.setName(name);
        list.setType(type);
        list.setCondition(condition);
        list.setUpdateTime(LocalDateTime.now());
        return list;
    }
}
