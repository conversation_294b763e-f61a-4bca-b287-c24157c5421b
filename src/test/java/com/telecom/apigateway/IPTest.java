package com.telecom.apigateway;

import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.entity.IpInfo;
import com.telecom.apigateway.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;

/**
 * @program: APIWG-Service
 * @ClassName IPTest
 * @description:
 * @author: Levi
 * @create: 2025-01-21 18:33
 * @Version 1.0
 **/
@Slf4j
public class IPTest {
    @Test
    public void searchIp() {
        String[] ipArray = {
                "*************",
                "************",
                "**************",
                "*******",
                "***************",
                "*************",
                "**************",
                "**************",
                "**************"
        };
       // 使用增强 for 循环遍历 ipArray 数组中的每一个 IP 地址
for (String ip : ipArray) {
    // 调用 IpUtils 类的 getCountry 方法，根据 IP 地址获取对应的国家信息
    String country = IpUtils.getCountry(ip);

    // 调用 IpUtils 类的 getCity 方法，根据 IP 地址获取对应的城市信息
    String city = IpUtils.getCity(ip);

    // 打印 IP 地址及其对应的国家和城市信息，格式为 "IP: 国家-城市"
    System.out.println(ip + ": " + country + "-" + city);
}


        // nginxLogEsClient.updateByQuery(queryDTO, updateMap);
    }
}