# API Gateway

## Technology Stack

| Category         | Technology          | Version     |
| ---------------- | ------------------- |-------------|
| Backend Framework| Spring Boot         | 2.7.18      |
| Programming Language | Java               | 1.8         |
| Database         | PostgreSQL          | -           |
| ORM Framework    | MyBatis-Plus        | 3.5.7       |
| Code Enhancement | Lombok              | -           |
| Utility Library  | Apache Commons Lang | -           |
|                   | Hutool              | 5.8.26      |
| API Documentation| Springdoc OpenAPI   | 1.7.0       |
| Database Migration| Flyway              | -           |

## Running the Application

### Prerequisites
Ensure that <PERSON><PERSON> and Docker Compose are installed on your system. For installation instructions, refer to the [Docker Compose documentation](https://docs.docker.com/compose/install/).

### Start the Application

#### Linux or macOS
```sh
cd run && docker-compose up -d
```

#### Windows
```cmd
cd run
docker-compose up -d
```

### Verify the Data Source Server

Check that the Docker containers are running:
```sh
docker ps
```
Expected Result:
```cmd
 CONTAINER ID          IMAGE                  PORTS
[CONTAINER_ID]      postgres:latest    0.0.0.0:5432->5432/tcp
```

### Launch the Application

Run the application by executing:
```sh
src/main/java/com/telecom/apigateway/GatewayApplication.java
```

### Verify Server Availability

#### Health Check
Perform a health check to ensure the server is running:
```sh
curl http://localhost:10030/api/health/check
```

Expected Response:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": "OK",
  "success": true
}
```

#### Check Data Source Client
Test the data source client with the following cURL command:
```sh
curl -X POST http://localhost:10030/api/user/test-user \
     -H "Content-Type: application/json" \
     -d '{}'
```

Expected Response:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 2,
    "userName": "test",
    "userPassword": "123456",
    "createTime": "2024-08-13T13:05:04.784036Z",
    "updateTime": "2024-08-13T13:05:04.784036Z",
    "deleted": false
  },
  "success": true
}
```

### Access Swagger Documentation
Open the Swagger documentation at:
[http://localhost:10030/doc](http://localhost:10030/doc)
