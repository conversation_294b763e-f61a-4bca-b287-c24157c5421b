# Queerecho推荐系统使用说明

## API接口

### 1. 获取推荐列表
```http
POST /api/recommendations/voices
Content-Type: application/json

{
  "userId": "user123",
  "page": 1,
  "size": 20,
  "type": "mixed",
  "excludeViewed": true,
  "viewedDays": 7
}
```

**响应示例:**
```json
{
  "voices": [
    {
      "voice": {
        "id": "voice123",
        "text": "这是一条推文",
        "authorId": "author456",
        "createdAt": "2024-01-01T12:00:00"
      },
      "score": 0.85,
      "reason": "ai_recommended",
      "aiScore": 0.7,
      "heatScore": 0.6,
      "freshnessScore": 0.9,
      "penaltyScore": 1.0
    }
  ],
  "page": 1,
  "size": 20,
  "total": 100,
  "hasMore": true
}
```

### 2. 记录用户交互
```http
POST /api/recommendations/interaction?userId=user123&voiceId=voice456&interactionType=VIEW
```

**交互类型:**
- `VIEW` - 浏览
- `LIKE` - 点赞  
- `RETWEET` - 转发
- `REPLY` - 回复

## 推荐算法说明

### 召回策略
1. **时间倒序300条** - 最新内容优先
2. **热门倒序200条** - 高互动内容
3. **关注人100条** - 社交关系内容

### 排序公式
```
最终分数 = (AI分×0.6 + 热度分×0.4) × 时间衰减 × 惩罚分
```

- **AI分**: 0.0-1.0，存储在voice_scores表
- **热度分**: 点赞×1 + 转发×2 + 回复×3，对数归一化
- **时间衰减**: e^(-0.05×小时数)
- **惩罚分**: 避免重复浏览，5分钟内0.3，1小时内0.5，1天内0.7

### 过滤规则
- 排除用户自己的内容
- 排除隐藏的内容
- 排除N天内已浏览的内容（默认7天）

## 数据库表结构

### voice_scores表
```sql
CREATE TABLE voice_scores (
    id VARCHAR PRIMARY KEY,
    voice_id VARCHAR NOT NULL,
    ai_score DOUBLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### user_view_history表  
```sql
CREATE TABLE user_view_history (
    id VARCHAR PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    voice_id VARCHAR NOT NULL,
    viewed_at TIMESTAMP,
    interaction_type VARCHAR,
    created_at TIMESTAMP
);
```
