version: '0.1'
services:
  postgres:
    image: postgres:latest
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: root
    ports:
      - "5432:5432"
    volumes:
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
      - postgres_data:/var/lib/postgresql/data

  loki:
    image: grafana/loki:2.7.4
    container_name: loki
    ports:
      - "3100:3100"
    networks:
      - ops_default
    command: -config.file=/etc/loki/local-config.yaml

  promtail:
    image: grafana/promtail:2.7.4
    container_name: promtail-loki
    networks:
      - ops_default
    volumes:
      - /var/log:/var/log
    command: -config.file=/etc/promtail/config.yml

  grafana:
    image: grafana/grafana:latest
    container_name: grafana-loki
    networks:
      - ops_default
    ports:
      - "3000:3000"

networks:
  ops_default:
    driver: bridge

volumes:
  postgres_data: