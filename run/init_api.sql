DROP TABLE IF EXISTS api;
create table api
(
    id            serial
        primary key,
    name          varchar(100)                        not null,
    app_id        integer                             not null,
    http_method   varchar(20),
    risk_level    integer,
    is_sensitive  boolean   default false,
    uri           varchar(255)                        not null,
    path_level    integer,
    is_active     boolean   default false,
    is_online     boolean,
    type          integer,
    source        integer,
    discover_time timestamp,
    create_user   integer                             not null,
    update_user   integer,
    create_time   timestamp default CURRENT_TIMESTAMP not null,
    update_time   timestamp default CURRENT_TIMESTAMP,
    is_deleted    boolean   default false,
    remark        varchar(255)
);

comment on table api is 'api 表';

comment on column api.id is '主键';

comment on column api.name is '名称';

comment on column api.app_id is '应用id';

comment on column api.http_method is 'http方法';

comment on column api.risk_level is '风险等级,字典';

comment on column api.is_sensitive is '是否敏感, 包含敏感数据, 0:否 1:是';

comment on column api.uri is 'uri';

comment on column api.path_level is '路径级别 0:根 1:一级 2:二级';

comment on column api.is_active is '是否激活, 0否1是';

comment on column api.type is '类型,字典';

comment on column api.source is '来源, 0自动1手动';

comment on column api.discover_time is '发现时间';

comment on column api.create_user is '创建用户';

comment on column api.update_user is '更新用户';

comment on column api.create_time is '创建时间';

comment on column api.update_time is '更新时间';

comment on column api.is_deleted is '是否删除 0否1是';

comment on column api.remark is '备注';
