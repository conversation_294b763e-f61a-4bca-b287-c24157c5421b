version: '3.8'
services:
  postgres:
    image: postgres:latest
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: root
    ports:
      - "5432:5432"
    restart: always
    volumes:
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app_network

  api-gateway:
    image: api-gateway:v1
    restart: always
    ports:
      - "10030:10030"
    environment:
      pg.url: ***************************************************
      flyway.open: true
      loki.server: http://host.docker.internal:3100/loki/api/v1
      loki.accessLogLabel: access
      loki.errorLogLabel: error
      loki.enableSchedule: true

    networks:
      - app_network
    depends_on:
      - postgres

networks:
  app_network:
    driver: bridge

volumes:
  postgres_data:
