version: "3"

services:
  # 日志存储和解析
  loki:
    image: grafana/loki:2.9.9
    container_name: loki
    volumes:
      - ./conf/loki/:/etc/loki/
    # 修改loki默认配置文件路径
    command: -config.file=/etc/loki/loki.yml
    ports:
      - "3100:3100"

  # 日志收集器
  promtail:
    image: grafana/promtail:2.9.9
    container_name: promtail
    volumes:
      # 将需要收集的日志所在目录挂载到promtail容器中
      - ./logs/:/var/log/
      - ./conf/promtail/:/etc/promtail/
    # 修改promtail默认配置文件路径
    command: -config.file=/etc/promtail/promtail.yml

  # 日志可视化
  grafana:
    image: grafana/grafana:11.1.0
    container_name: grafana
    ports:
      - "3000:3000"

  # 网关
  gateway:
    image: openresty/openresty
    container_name: openresty
    volumes:
      - ./conf/nginx/nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf
      - ./conf/nginx/conf.d/httpbin.conf:/etc/nginx/conf.d/default.conf
      - ./logs/:/usr/local/openresty/nginx/logs/
    ports:
      - "8080:80"

  # 应用
  httpbin:
    image: kennethreitz/httpbin
    container_name: httpbin